package cn.huolala.arch.hermes.api.annotation;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.spec.classification.ApiStability;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * SOAReference
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.METHOD})
@ApiAudience.Public
@ApiStability.Stable
public @interface HermesReference {
    /**
     * alias for {@link #application()}
     */
    String value() default "";

    /**
     * The remote application name
     */
    String application() default "";

    /**
     * The ref service path
     */
    String path() default "";

    /**
     * The ref service version
     */
    String version() default "";

    /**
     * The url for peer-to-peer invocation
     */
    String url() default "";

    /**
     * The protocol name
     */
    String protocol() default "";

    /**
     * Cluster strategy
     */
    String cluster() default "";

    /**
     * group
     */
    String group() default "";

    /**
     * Load balance strategy
     */
    String loadbalance() default "";

    /**
     * Timeout
     */
    int timeout() default 0;

    /**
     * ConnectionTimeout
     */
    int connectionTimeout() default 0;
    
    /**
     * Fallback class: get bean or new
     * <ol>
     *     <li>FallbackFactory class</li>
     *     <li>Fallback class</li>
     *     <li>instance</li>
     * </ol>
     */
    Class<?> fallback() default void.class;
    
    /**
     *  Is https
     */
    boolean https() default false;

    /**
     * The method configuration
     */
    Method[] methods() default {};
}