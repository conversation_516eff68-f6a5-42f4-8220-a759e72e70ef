package cn.huolala.arch.hermes.api.annotation;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.spec.classification.ApiStability;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * SOAService
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Inherited
@ApiAudience.Public
@ApiStability.Stable
public @interface HermesService {
    /**
     * The service description
     */
    String description() default "";
    
    /**
     * alias for {@link #path()}
     */
    String value() default "";

    /**
     * The ref service path
     */
    String path() default "";


}