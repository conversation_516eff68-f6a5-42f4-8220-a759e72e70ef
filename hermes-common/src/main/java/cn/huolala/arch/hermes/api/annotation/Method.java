package cn.huolala.arch.hermes.api.annotation;

import cn.huolala.arch.hermes.api.http.HttpMethod;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.spec.classification.ApiStability;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Method
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@Inherited
@ApiAudience.Public
@ApiStability.Stable
public @interface Method {
    /**
     * The method name
     */
    String value() default "";

    /**
     * The method parameters desc
     */
    String paramDesc() default "";

    /**
     * The method path
     */
    String path() default "";

    /**
     * The customized commandKey
     */
    String commandKey() default "";

    /**
     * Load balance strategy
     */
    String loadbalance() default "";

    /**
     * Timeout/ReadTimeout
     */
    int timeout() default 0;

    /**
     * ConnectionTimeout
     */
    int connectionTimeout() default 0;

    /**
     * The method description
     */
    String description() default "";

    /**
     * Fallback class: get bean or new
     * <ol>
     *     <li>FallbackFactory class</li>
     *     <li>Fallback class</li>
     *     <li>instance</li>
     * </ol>
     */
    Class<?> fallback() default void.class;

    /**
     * Http method configuration
     *
     * @see HttpMethod
     */
    HttpMethod http() default @HttpMethod;
    
    /**
     * OneWay invoke
     * @return
     */
    boolean oneWay() default false;
}