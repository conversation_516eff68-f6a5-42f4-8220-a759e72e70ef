package cn.huolala.arch.hermes.api.annotation;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.spec.classification.ApiStability;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Param
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.PARAMETER)
@ApiAudience.Public
@ApiStability.Stable
public @interface Param {
    /**
     * The name of the parameter
     */
    String value();
}