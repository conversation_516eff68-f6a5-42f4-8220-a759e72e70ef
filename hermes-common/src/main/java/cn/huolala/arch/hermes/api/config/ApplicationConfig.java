package cn.huolala.arch.hermes.api.config;

import static cn.huolala.arch.hermes.common.constants.Constants.APPLICATION_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.DEFAULT_ENV;
import static cn.huolala.arch.hermes.common.constants.Constants.ENV_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.ORGANIZATION_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.OWNER_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.VERSION_KEY;

import java.util.Set;
import java.util.StringJoiner;

import cn.huolala.arch.hermes.api.config.support.AbstractConfig;
import cn.huolala.arch.hermes.api.config.support.Parameter;
import cn.huolala.arch.hermes.common.tool.Assert;
import cn.huolala.arch.hermes.common.util.StringUtils;

/**
 * Application Config
 */
public class ApplicationConfig extends AbstractConfig {
    private static final long serialVersionUID = 4970663316975501052L;

    /**
     * Application name
     */
    @Parameter(APPLICATION_KEY)
    private String name;

    /**
     * The application version
     */
    @Parameter(VERSION_KEY)
    private String version;

    /**
     * Application environment: STG, PRD ...
     */
    @Parameter(ENV_KEY)
    private String environment;

    /**
     * Application owner
     */
    @Parameter(OWNER_KEY)
    private String owner;

    /**
     * Application belong to organization;
     */
    @Parameter(ORGANIZATION_KEY)
    private String organization;

    /**
     * Grpc provider scan packages and Json rpc consumer scan packages
     */
    private Set<String> scanBasePackages;
    
    /**
     * Grayscale version
     */
    private String releaseVersion;

    private String zone;

    private String group;

    @Override
    public void checkAndInitConfig() throws IllegalStateException {
        super.checkAndInitConfig();

        Assert.notEmpty(name, new IllegalStateException("ApplicationConfig name is required"));

        if (StringUtils.isBlank(environment)) {
            environment = DEFAULT_ENV;
        }

        this.loadParameters();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public Set<String> getScanBasePackages() {
        return scanBasePackages;
    }

    public void setScanBasePackages(Set<String> scanBasePackages) {
        this.scanBasePackages = scanBasePackages;
    }

    public String getReleaseVersion() {
        return releaseVersion;
    }

    public void setReleaseVersion(String releaseVersion) {
        this.releaseVersion = releaseVersion;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ApplicationConfig.class.getSimpleName() + "[", "]")//
                .add("id='" + id + "'")
                .add("prefix='" + prefix + "'")
                .add("name='" + name + "'")
                .add("version='" + version + "'")
                .add("environment='" + environment + "'")
                .add("parameters=" + parameters)
                .add("organization=" + organization)
                .add("owner=" + owner)
                .add("zone=" + zone)
                .add("group=" + group)
                .toString();
    }

}
