package cn.huolala.arch.hermes.api.config;

import cn.huolala.arch.hermes.api.config.support.AbstractConfig;
import cn.huolala.arch.hermes.api.config.support.Parameter;
import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.tool.Assert;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.common.util.URLUtils;

import java.util.StringJoiner;

import static cn.huolala.arch.hermes.common.constants.Constants.ANYHOST_VALUE;
import static cn.huolala.arch.hermes.common.constants.Constants.CLUSTER_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.CONFIG_CENTER_PROTOCOL;
import static cn.huolala.arch.hermes.common.constants.Constants.PROTOCOL_KEY;
import static cn.huolala.arch.hermes.common.constants.SpiConstants.CONFIG_CENTER_DEFAULT;

/**
 * ConfigCenter Config
 */
public class ConfigCenterConfig extends AbstractConfig {
    private static final long serialVersionUID = 2033508372244713456L;

    /**
     * Protocol for ConfigCenter: apollo ...
     */
    @Parameter(PROTOCOL_KEY)
    private String protocol;

    /**
     * ConfigCenter address
     */
    private String address;

    /**
     * Default port
     */
    private Integer port;

    @Parameter(CLUSTER_KEY)
    private String cluster;

    @Override
    public void checkAndInitConfig() throws IllegalStateException {
        super.checkAndInitConfig();

        Assert.notEmpty(address, new IllegalStateException("ConfigCenterConfig address is required"));

        if (StringUtils.isBlank(protocol)) {
            protocol = CONFIG_CENTER_DEFAULT;
        }

        mergeParameters(getApplicationConfig());
        this.loadParameters();
    }

    public URL toUrl() {
        if (StringUtils.isEmpty(address)) {
            address = ANYHOST_VALUE;
        }

        return URLUtils.newURLBuilder(CONFIG_CENTER_PROTOCOL, address, port)
                .addParameters(parameters)
                .build();
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getCluster() {
        return cluster;
    }

    public void setCluster(String cluster) {
        this.cluster = cluster;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ConfigCenterConfig.class.getSimpleName() + "[", "]")
                .add("id='" + id + "'")
                .add("prefix='" + prefix + "'")
                .add("protocol='" + protocol + "'")
                .add("address='" + address + "'")
                .add("port=" + port)
                .add("cluster='" + cluster + "'")
                .add("parameters=" + parameters)
                .toString();
    }
}
