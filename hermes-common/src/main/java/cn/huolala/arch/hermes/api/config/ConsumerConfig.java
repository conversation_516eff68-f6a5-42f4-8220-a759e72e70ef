package cn.huolala.arch.hermes.api.config;

import static cn.huolala.arch.hermes.common.constants.Constants.DEFAULT_CONNECTION_TIMEOUT;
import static cn.huolala.arch.hermes.common.constants.Constants.DEFAULT_TIMEOUT;
import static cn.huolala.arch.hermes.common.constants.Constants.REFERENCE_FILTER_KEY;

import java.util.*;

import cn.huolala.arch.hermes.api.config.governance.GovernanceConfig;
import cn.huolala.arch.hermes.api.config.support.AbstractReferenceConfig;
import cn.huolala.arch.hermes.api.config.support.Parameter;
import cn.huolala.arch.hermes.api.fallback.FallbackFactory;
import cn.huolala.arch.hermes.common.util.CollectionUtils;

/**
 * The service consumer/reference default configuration
 */
public class ConsumerConfig extends AbstractReferenceConfig {
    private static final long serialVersionUID = -2483762527014770214L;

    /**
     * The discovery list the consumer will look up（Not open to the outside world）
     */
    private List<DiscoveryConfig> discoveries;

    /**
     * The governance config
     */
    private GovernanceConfig governance;

    /**
     * The fallback factory
     */
    private FallbackFactory<?> fallbackFactory;

    /**
     * The scan packages
     */
    private Set<String> scanBasePackages;

    /**
     * The scan Classes
     */
    private Set<String> scanPackageClasses;

    @Parameter
    private Boolean validation;

    /**
     * invoker logs
     */
    @Parameter
    private Boolean invokerlog;

    /**
     * if there are more than one, you can use commas to separate them
     */
    @Parameter(REFERENCE_FILTER_KEY)
    private String filter;

    /**
     * Is check invoker available
     */
    private Boolean check;

    /**
     * Is consumer enabled
     */
    private Boolean enabled;

    private Map<String, String> serializations;

    /**
     * 服务端治理异常后是否执行Fallback
     */
    @Parameter
    private Boolean fallbackForProvider;

    @Override
    public void checkAndInitConfig() throws IllegalStateException {
        super.checkAndInitConfig();

        if (CollectionUtils.isEmpty(discoveries)) {
            throw new IllegalStateException("ConsumerConfig discovreies is required");
        }
        if (enabled == null) {
            enabled = true;
        }
        if (check == null) {
            check = false;
        }
        if (validation == null) {
            validation = false;
        }
        if (invokerlog == null) {
            invokerlog = true;
        }
        if (connectionTimeout == null) {
            connectionTimeout = DEFAULT_CONNECTION_TIMEOUT;
        }
        if (timeout == null) {
            timeout = DEFAULT_TIMEOUT;
        }
        if (Objects.nonNull(governance)) {
            governance.checkAndInitConfig();
        }
        loadParameters();
    }

    public List<DiscoveryConfig> getDiscoveries() {
        return discoveries;
    }

    public void setDiscoveries(List<DiscoveryConfig> discoveries) {
        this.discoveries = discoveries;
    }

    public FallbackFactory<?> getFallbackFactory() {
        return fallbackFactory;
    }

    public void setFallbackFactory(FallbackFactory<?> fallbackFactory) {
        this.fallbackFactory = fallbackFactory;
    }

    public Set<String> getScanBasePackages() {
        return scanBasePackages;
    }

    public void setScanBasePackages(Set<String> scanBasePackages) {
        this.scanBasePackages = scanBasePackages;
    }

    public Set<String> getScanPackageClasses() {
        return scanPackageClasses;
    }

    public void setScanPackageClasses(Set<String> scanPackageClasses) {
        this.scanPackageClasses = scanPackageClasses;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getCheck() {
        return check;
    }

    public void setCheck(Boolean check) {
        this.check = check;
    }

    public Boolean getValidation() {
        return validation;
    }

    public void setValidation(Boolean validation) {
        this.validation = validation;
    }

    public Boolean getInvokerlog() {
        return invokerlog;
    }

    public void setInvokerlog(Boolean invokerlog) {
        this.invokerlog = invokerlog;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public GovernanceConfig getGovernance() {
        return governance;
    }

    public void setGovernance(GovernanceConfig governance) {
        this.governance = governance;
    }

    public Map<String, String> getSerializations() {
        return serializations;
    }

    public void setSerializations(Map<String, String> serializations) {
        this.serializations = serializations;
    }

    public Boolean getFallbackForProvider() {
        return fallbackForProvider;
    }

    public void setFallbackForProvider(Boolean fallbackForProvider) {
        this.fallbackForProvider = fallbackForProvider;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ConsumerConfig.class.getSimpleName() + "[", "]").add("id='" + id + "'")
                .add("prefix='" + prefix + "'")
                .add("timeout=" + timeout)
                .add("connectionTimeout=" + connectionTimeout)
                .add("loadbalance='" + loadbalance + "'")
                .add("cluster='" + cluster + "'")
                .add("discoveries=" + discoveries)
                .add("parameters=" + parameters)
                .add("scanBasePackages=" + scanBasePackages)
                .add("scanPackageClasses=" + scanPackageClasses)
                .add("check=" + check)
                .add("enabled=" + enabled)
                .add("validation=" + validation)
                .add("invokerlog=" + invokerlog)
                .add("filter=" + filter)
                .add("governance" + governance)
                .toString();
    }
}
