package cn.huolala.arch.hermes.api.config;

import cn.huolala.arch.hermes.api.config.support.AbstractConfig;
import cn.huolala.arch.hermes.api.config.support.Parameter;
import cn.huolala.arch.hermes.common.util.StringUtils;

import java.util.StringJoiner;

import static cn.huolala.arch.hermes.common.constants.Constants.CONNECT_AT_START_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.DEFAULT_PROTOCOL;
import static cn.huolala.arch.hermes.common.constants.Constants.DISPATCHER_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.IDLE_TIMEOUT_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.PAYLOAD_KEY;

/**
 * ConsumerProtocolConfig
 */
public class ConsumerProtocolConfig extends AbstractConfig {
    private static final long serialVersionUID = 4693579054024663677L;

    /**
     * Protocol name
     */
    private String name;

    /**
     * Thread dispatch mode
     */
    @Parameter(DISPATCHER_KEY)
    private String dispatcher;

    /**
     * Thread pool config
     */
    private ExecutorConfig executor;

    /**
     * Payload max length
     */
    @Parameter(PAYLOAD_KEY)
    private Integer payload;

    /**
     * Connection idle timeout
     */
    @Parameter(IDLE_TIMEOUT_KEY)
    private Integer idleTimeout;

    /**
     * Request connection at start
     */
    @Parameter(CONNECT_AT_START_KEY)
    private Boolean connectAtStart;

    @Override
    public void checkAndInitConfig() throws IllegalStateException {
        if (StringUtils.isEmpty(name)) {
            name = DEFAULT_PROTOCOL;
        }

        this.loadParameters();

        if (executor != null) {
            mergeParameters(loadParameters(executor));
        }
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDispatcher() {
        return dispatcher;
    }

    public void setDispatcher(String dispatcher) {
        this.dispatcher = dispatcher;
    }

    public ExecutorConfig getExecutor() {
        return executor;
    }

    public void setExecutor(ExecutorConfig executor) {
        this.executor = executor;
    }

    public Integer getPayload() {
        return payload;
    }

    public void setPayload(Integer payload) {
        this.payload = payload;
    }

    public Integer getIdleTimeout() {
        return idleTimeout;
    }

    public void setIdleTimeout(Integer idleTimeout) {
        this.idleTimeout = idleTimeout;
    }

    public Boolean getConnectAtStart() {
        return connectAtStart;
    }

    public void setConnectAtStart(Boolean connectAtStart) {
        this.connectAtStart = connectAtStart;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ConsumerProtocolConfig.class.getSimpleName() + "[", "]")
                .add("id='" + id + "'")
                .add("prefix='" + prefix + "'")
                .add("name='" + name + "'")
                .add("dispatcher='" + dispatcher + "'")
                .add("executor=" + executor)
                .add("payload=" + payload)
                .add("idleTimeout=" + idleTimeout)
                .add("connectAtStart=" + connectAtStart)
                .add("parameters=" + parameters)
                .toString();
    }
}
