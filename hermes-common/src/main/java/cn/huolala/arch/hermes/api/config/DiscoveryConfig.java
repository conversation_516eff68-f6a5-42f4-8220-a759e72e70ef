package cn.huolala.arch.hermes.api.config;

import cn.huolala.arch.hermes.api.config.support.AbstractConfig;
import cn.huolala.arch.hermes.api.config.support.Parameter;
import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.tool.Assert;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.common.util.URLUtils;

import java.util.Map;
import java.util.StringJoiner;

import static cn.huolala.arch.hermes.common.constants.Constants.ANYHOST_VALUE;
import static cn.huolala.arch.hermes.common.constants.Constants.DEFAULT_REGISTER_DELAY;
import static cn.huolala.arch.hermes.common.constants.Constants.DELAY_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.DISCOVERY_PROTOCOL;
import static cn.huolala.arch.hermes.common.constants.Constants.PROTOCOL_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.TAGS_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.TOKEN_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.WAIT_KEY;
import static cn.huolala.arch.hermes.common.constants.SpiConstants.DISCOVERY_DEFAULT;
import static cn.huolala.arch.hermes.common.constants.Constants.METADATA_KEY;

/**
 * Discovery Config
 */
public class DiscoveryConfig extends AbstractConfig {
    private static final long serialVersionUID = 523551115982283335L;

    /**
     * Protocol for discovery: consul ...
     */
    @Parameter(PROTOCOL_KEY)
    private String protocol;

    /**
     * Discovery address
     */
    private String address;

    /**
     * Default port
     */
    private Integer port;

    /**
     * token > username/password
     */
    @Parameter(TOKEN_KEY)
    private String token;
    private String username;
    private String password;

    /**
     * Delay time before export
     */
    @Parameter(DELAY_KEY)
    private Integer delay;

    /**
     * Wait time before unExport to notify all consumers
     */
    @Parameter(WAIT_KEY)
    private Integer wait;

    /**
     * Whether to register serviceInstance
     */
    @Parameter
    private Boolean register;

    /**
     * The customized tags
     */
    @Parameter(TAGS_KEY)
    private Map<String, String> tags;
    
    /**
     * The customized metadata
     */
    @Parameter(METADATA_KEY)
    private Map<String, String> metadata;
    
    
    
    @Override
    public void checkAndInitConfig() throws IllegalStateException {
        super.checkAndInitConfig();

        Assert.notEmpty(address, new IllegalStateException("DiscoveryConfig address is required"));

        if (StringUtils.isBlank(protocol)) {
            protocol = DISCOVERY_DEFAULT;
        }
        if (delay == null) {
            delay = DEFAULT_REGISTER_DELAY;
        }
        if (register == null) {
            register = true;
        }

        mergeParameters(getApplicationConfig());
        this.loadParameters();
    }

    public URL toUrl() {
        if (StringUtils.isEmpty(address)) {
            address = ANYHOST_VALUE;
        }

        return URLUtils.newURLBuilder(DISCOVERY_PROTOCOL, address, port)
                .setUsername(username)
                .setPassword(password)
                .addParameters(parameters)
                .build();
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getDelay() {
        return delay;
    }

    public void setDelay(Integer delay) {
        this.delay = delay;
    }

    public Integer getWait() {
        return wait;
    }

    public void setWait(Integer wait) {
        this.wait = wait;
    }

    public Boolean getRegister() {
        return register;
    }

    public void setRegister(Boolean register) {
        this.register = register;
    }

    public Map<String, String> getTags() {
        return tags;
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
    }

    public Map<String, String> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, String> metadata) {
        this.metadata = metadata;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", DiscoveryConfig.class.getSimpleName() + "[", "]")
                .add("id='" + id + "'")
                .add("prefix='" + prefix + "'")
                .add("protocol='" + protocol + "'")
                .add("address='" + address + "'")
                .add("port=" + port)
                .add("token='" + token + "'")
                .add("username='" + username + "'")
                .add("password='" + password + "'")
                .add("delay=" + delay)
                .add("wait=" + wait)
                .add("register=" + register)
                .add("tags=" + tags)
                .add("parameters=" + parameters)
                .toString();
    }
}
