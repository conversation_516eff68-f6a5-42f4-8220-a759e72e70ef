package cn.huolala.arch.hermes.api.config;

import cn.huolala.arch.hermes.api.config.support.AbstractConfig;
import cn.huolala.arch.hermes.api.config.support.Parameter;

import java.util.StringJoiner;

import static cn.huolala.arch.hermes.common.constants.ThreadConstants.ALIVE_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.CORE_THREADS_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.QUEUES_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.THREADPOOL_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.THREADS_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.THREAD_NAME_KEY;

/**
 * ExecutorConfig
 *
 * @see java.util.concurrent.Executor
 */
public class ExecutorConfig extends AbstractConfig {
    private static final long serialVersionUID = 8620054031238545221L;

    /**
     * Thread pool type: fixed/cached
     */
    @Parameter(THREADPOOL_KEY)
    private String threadpool;

    /**
     * Thread pool name
     */
    @Parameter(THREAD_NAME_KEY)
    private String threadname;

    /**
     * Thread pool core thread size
     */
    @Parameter(CORE_THREADS_KEY)
    private Integer corethreads;

    /**
     * Thread pool size (fixed size)
     */
    @Parameter(THREADS_KEY)
    private Integer threads;

    /**
     * Thread pool keepAliveTime, default unit TimeUnit.MILLISECONDS
     */
    @Parameter(ALIVE_KEY)
    private Integer alive;

    /**
     * Thread pool's queue length
     */
    @Parameter(QUEUES_KEY)
    private Integer queues;

    public String getThreadpool() {
        return threadpool;
    }

    public void setThreadpool(String threadpool) {
        this.threadpool = threadpool;
    }

    public String getThreadname() {
        return threadname;
    }

    public void setThreadname(String threadname) {
        this.threadname = threadname;
    }

    public Integer getCorethreads() {
        return corethreads;
    }

    public void setCorethreads(Integer corethreads) {
        this.corethreads = corethreads;
    }

    public Integer getThreads() {
        return threads;
    }

    public void setThreads(Integer threads) {
        this.threads = threads;
    }

    public Integer getAlive() {
        return alive;
    }

    public void setAlive(Integer alive) {
        this.alive = alive;
    }

    public Integer getQueues() {
        return queues;
    }

    public void setQueues(Integer queues) {
        this.queues = queues;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ExecutorConfig.class.getSimpleName() + "[", "]")
                .add("id='" + id + "'")
                .add("prefix='" + prefix + "'")
                .add("threadpool='" + threadpool + "'")
                .add("threadname='" + threadname + "'")
                .add("corethreads=" + corethreads)
                .add("threads=" + threads)
                .add("alive=" + alive)
                .add("queues=" + queues)
                .toString();
    }
}
