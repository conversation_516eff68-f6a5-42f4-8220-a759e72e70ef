package cn.huolala.arch.hermes.api.config;

import java.util.Map;

import cn.huolala.arch.hermes.api.config.support.AbstractConfig;

/**
 * Direct connect
 */
public class HostPortsConfig extends AbstractConfig {

    private static final long serialVersionUID = 1L;

    private Map<String, HostPort> hostPorts;

    public Map<String, HostPort> getHostPorts() {
        return hostPorts;
    }

    public void setHostPorts(Map<String, HostPort> hostPorts) {
        this.hostPorts = hostPorts;
    }

    public static class HostPort {

        /**
         * host
         */
        private String host;

        /**
         * port
         */
        private Integer port;

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public Integer getPort() {
            return port;
        }

        public void setPort(Integer port) {
            this.port = port;
        }
    }
}
