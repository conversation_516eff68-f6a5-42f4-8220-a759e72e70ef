package cn.huolala.arch.hermes.api.config;

import cn.huolala.arch.hermes.api.annotation.Method;
import cn.huolala.arch.hermes.api.annotation.Param;
import cn.huolala.arch.hermes.api.config.support.AbstractMethodConfig;
import cn.huolala.arch.hermes.api.fallback.FallbackFactory;
import cn.huolala.arch.hermes.common.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

/**
 * MethodConfig
 */
public class MethodConfig extends AbstractMethodConfig {
    private static final long serialVersionUID = -4704332365611248758L;

    /**
     * The method name
     */
    private String name;

    /**
     * The method parameters desc
     */
    private String paramDesc;

    /**
     * The method parameter names
     *
     * @see Param
     */
    private List<String> paramNames;

    /**
     * The method path
     */
    private String path;

    /**
     * original method path
     */
    private String rawPath;

    /**
     * The customized commandKey (optional)
     */
    private String commandKey;

    /**
     * The method description
     */
    private String description;

    /**
     * The fallback factory
     */
    private FallbackFactory<?> fallbackFactory;

    /**
     * The method extension attributes
     */
    private Map<String, Object> attributes;

    /**
     * Is oneWay invoke
     */
    private Boolean oneWay;

    public MethodConfig() {
    }

    public MethodConfig(Method method, FallbackFactory<?> factory) {
        this(method, method.value(), method.paramDesc(), factory, new HashMap<>());
    }

    public MethodConfig(Method method, String name, String paramDesc, FallbackFactory<?> factory, Map<String, Object> attributes) {
        this(name, paramDesc, factory, method.timeout(), method.connectionTimeout(), method.path(), method.commandKey(), method.description(), method.loadbalance(), method.oneWay(), new ArrayList<>(), attributes);

    }

    public MethodConfig(String name, String paramDesc, FallbackFactory<?> factory, Integer timeout, Integer connectionTimeout, String path,
                        String commandKey, String description, String loadbalance, boolean oneWay, List<String> paramNames, Map<String, Object> attributes) {
        super();
        if (StringUtils.isEmpty(name)) {
            throw new IllegalArgumentException("MethodConfig name is required");
        }

        this.name = name;
        this.paramDesc = paramDesc;
        this.fallbackFactory = factory;

        this.timeout = timeout;
        this.connectionTimeout = connectionTimeout;
        this.path = path;

        if (StringUtils.isNotEmpty(commandKey)) {
            this.commandKey = commandKey;
        }
        if (StringUtils.isNotEmpty(description)) {
            this.description = description;
        }
        if (StringUtils.isNotEmpty(loadbalance)) {
            this.loadbalance = loadbalance;
        }
        this.oneWay = oneWay;
        this.paramNames = paramNames;
        this.attributes = attributes;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParamDesc() {
        return paramDesc;
    }

    public void setParamDesc(String paramDesc) {
        this.paramDesc = paramDesc;
    }

    public List<String> getParamNames() {
        return paramNames;
    }

    public void setParamNames(List<String> paramNames) {
        this.paramNames = paramNames;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getRawPath() {
        return rawPath;
    }

    public void setRawPath(String rawPath) {
        this.rawPath = rawPath;
    }

    public String getCommandKey() {
        return commandKey;
    }

    public void setCommandKey(String commandKey) {
        this.commandKey = commandKey;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public FallbackFactory<?> getFallbackFactory() {
        return fallbackFactory;
    }

    public void setFallbackFactory(FallbackFactory<?> fallbackFactory) {
        this.fallbackFactory = fallbackFactory;
    }

    public Map<String, Object> getAttributes() {
        return attributes;
    }

    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }

    public Boolean getOneWay() {
        return oneWay;
    }

    public void setOneWay(Boolean oneWay) {
        this.oneWay = oneWay;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", MethodConfig.class.getSimpleName() + "[", "]")
                .add("id='" + id + "'")
                .add("prefix='" + prefix + "'")
                .add("name='" + name + "'")
                .add("paramDesc='" + paramDesc + "'")
                .add("paramNames='" + paramNames + "'")
                .add("path='" + path + "'")
                .add("commandKey='" + commandKey + "'")
                .add("timeout=" + timeout)
                .add("connectionTimeout=" + connectionTimeout)
                .add("loadbalance='" + loadbalance + "'")
                .add("description='" + description + "'")
                .add("parameters=" + parameters)
                .add("oneWay=" + oneWay)
                .toString();
    }
}
