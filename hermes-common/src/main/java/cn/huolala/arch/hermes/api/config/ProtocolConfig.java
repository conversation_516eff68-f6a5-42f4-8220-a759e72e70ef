package cn.huolala.arch.hermes.api.config;

import static cn.huolala.arch.hermes.common.constants.Constants.JSONRPC_PROTOCOL;
import static cn.huolala.arch.hermes.common.constants.Constants.DISPATCHER_KEY;

import java.util.StringJoiner;

import cn.huolala.arch.hermes.api.config.support.AbstractConfig;
import cn.huolala.arch.hermes.api.config.support.Parameter;
import cn.huolala.arch.hermes.common.util.StringUtils;

/**
 * Protocol Config
 */
public class ProtocolConfig extends AbstractConfig {
    private static final long serialVersionUID = 5136314215880544505L;

    /**
     * Protocol name
     */
    private String name;

    /**
     * Service ip address (when there are multiple network cards available)
     */
    private String host;

    /**
     * Service port
     */
    private Integer port;

    /**
     * Thread dispatch mode
     */
    @Parameter(DISPATCHER_KEY)
    private String dispatcher;

    /**
     * Thread pool config
     */
    private ExecutorConfig executor;

    /**
     * Max acceptable connections
     */
    @Parameter
    private Integer accepts;

    /**
     * Payload max length
     */
    @Parameter
    private Integer payload;

    @Override
    public void checkAndInitConfig() throws IllegalStateException {
        if (StringUtils.isEmpty(name)) {
            name = JSONRPC_PROTOCOL;
        }
        
        this.loadParameters();

        if (executor != null) {
            mergeParameters(loadParameters(executor));
        }
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getDispatcher() {
        return dispatcher;
    }

    public void setDispatcher(String dispatcher) {
        this.dispatcher = dispatcher;
    }

    public ExecutorConfig getExecutor() {
        return executor;
    }

    public void setExecutor(ExecutorConfig executor) {
        this.executor = executor;
    }

    public Integer getAccepts() {
        return accepts;
    }

    public void setAccepts(Integer accepts) {
        this.accepts = accepts;
    }

    public Integer getPayload() {
        return payload;
    }

    public void setPayload(Integer payload) {
        this.payload = payload;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ProtocolConfig.class.getSimpleName() + "[", "]")
                .add("id='" + id + "'")
                .add("prefix='" + prefix + "'")
                .add("name='" + name + "'")
                .add("host='" + host + "'")
                .add("port=" + port)
                .add("dispatcher='" + dispatcher + "'")
                .add("accepts=" + accepts)
                .add("payload=" + payload)
                .add("executor=" + executor)
                .add("parameters=" + parameters)
                .toString();
    }
}
