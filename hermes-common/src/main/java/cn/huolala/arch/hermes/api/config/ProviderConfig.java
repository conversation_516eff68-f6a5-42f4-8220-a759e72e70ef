package cn.huolala.arch.hermes.api.config;

import java.util.List;
import java.util.Set;
import java.util.StringJoiner;

import cn.huolala.arch.hermes.api.config.support.AbstractServiceConfig;
import cn.huolala.arch.hermes.api.config.support.Parameter;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;

import static cn.huolala.arch.hermes.common.constants.Constants.SERVICE_FILTER_KEY;

/**
 * The service provider default configuration
 */
public class ProviderConfig extends AbstractServiceConfig {
    private static final long serialVersionUID = 275020571836134822L;

    /**
     * The scan packages
     */
    private Set<String> scanBasePackages;

    /**
     * Warm up period
     */
    private Integer warmup;

    /**
     * The discovery list the service will register to (Not open to the outside world)
     */
    private List<DiscoveryConfig> discoveries;

    @Parameter
    private Boolean validation;

    /**
     * Composite
     * only jsonrpc protocol supported
     */
    @Deprecated
    private Set<String> interceptors;

    /**
     * Composite
     * only jsonrpc protocol supported
     */
    @Deprecated
    private String errorResolver;

    /**
     * Whether to export access logs to logs
     */
    @Parameter
    private Boolean accesslog;

    /**
     * if there are more than one, you can use commas to separate them
     */
    @Parameter(SERVICE_FILTER_KEY)
    private String filter;

    @Override
    public void checkAndInitConfig() throws IllegalStateException {
        super.checkAndInitConfig();

        if (CollectionUtils.isEmpty(discoveries)) {
            throw new IllegalStateException("ProviderConfig discoveries is required");
        }
        if (validation == null) {
            validation = false;
        }
        if (accesslog == null) {
            accesslog = false;
        }
        try {
            if (StringUtils.isNotBlank(errorResolver)) {
                Class.forName(errorResolver);
            }
        } catch (ClassNotFoundException e) {
            throw new IllegalStateException("Failed to find errorResolver class:" + errorResolver, e);
        }

        if (CollectionUtils.isNotEmpty(interceptors)) {
            for (String interceptor : interceptors) {
                try {
                    Class.forName(interceptor);
                } catch (ClassNotFoundException e) {
                    throw new IllegalStateException("Failed to find interceptor class:" + interceptor, e);
                }
            }
        }
        loadParameters();
    }

    public Integer getWarmup() {
        return warmup;
    }

    public void setWarmup(Integer warmup) {
        this.warmup = warmup;
    }

    public List<DiscoveryConfig> getDiscoveries() {
        return discoveries;
    }

    public void setDiscoveries(List<DiscoveryConfig> discoveries) {
        this.discoveries = discoveries;
    }

    public Set<String> getInterceptors() {
        return interceptors;
    }

    public void setInterceptors(Set<String> interceptors) {
        this.interceptors = interceptors;
    }

    public String getErrorResolver() {
        return errorResolver;
    }

    public void setErrorResolver(String errorResolver) {
        this.errorResolver = errorResolver;
    }

    public Boolean getValidation() {
        return validation;
    }

    public void setValidation(Boolean validation) {
        this.validation = validation;
    }

    public Boolean getAccesslog() {
        return accesslog;
    }

    public void setAccesslog(Boolean accesslog) {
        this.accesslog = accesslog;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public Set<String> getScanBasePackages() {
        return scanBasePackages;
    }

    public void setScanBasePackages(Set<String> scanBasePackages) {
        this.scanBasePackages = scanBasePackages;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ProviderConfig.class.getSimpleName() + "[", "]")
                .add("id='" + id + "'")
                .add("prefix='" + prefix + "'")
                .add("timeout=" + timeout)
                .add("connectionTimeout=" + connectionTimeout)
                .add("loadbalance='" + loadbalance + "'")
                .add("warmup=" + warmup)
                .add("accesslog=" + accesslog)
                .add("filter=" + filter)
                .add("discoveries=" + discoveries)
                .add("parameters=" + parameters)
                .add("scanBasePackages=" + scanBasePackages)
                .toString();
    }
}
