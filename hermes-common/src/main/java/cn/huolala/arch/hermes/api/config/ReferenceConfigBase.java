package cn.huolala.arch.hermes.api.config;

import static cn.huolala.arch.hermes.common.constants.Constants.SEMICOLON_SPLIT_PATTERN;
import static java.lang.Thread.currentThread;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.huolala.arch.hermes.api.config.support.AbstractReferenceConfig;
import cn.huolala.arch.hermes.api.fallback.FallbackFactory;
import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.support.function.ThrowableConsumer;
import cn.huolala.arch.hermes.common.tool.Assert;
import cn.huolala.arch.hermes.common.url.URLStrParser;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;

/**
 * ReferenceConfig Base
 *
 * @param <T> type
 */
public abstract class ReferenceConfigBase<T> extends AbstractReferenceConfig {
    private static final long serialVersionUID = -8941740078459234718L;

    /**
     * The consumer config
     */
    protected ConsumerConfig consumer;

    /**
     * The remote application name
     */
    protected String application;

    /**
     * The interface name
     */
    protected String interfaceName;

    /**
     * The interface class of the reference service
     */
    protected Class<? super T> interfaceClass;

    /**
     * The ref service path
     */
    protected String path;

    /**
     * The ref service version
     */
    protected String version;

    /**
     * The url for peer-to-peer invocation
     */
    protected String url;

    /**
     * The fallback factory
     */
    protected FallbackFactory<?> fallbackFactory;

    /**
     * The method configuration
     */
    private List<MethodConfig> methods;

    /**
     * Consumer protocol
     */
    protected ConsumerProtocolConfig protocol;

    /**
     * Consumer protocol name
     */
    protected String protocolName;

    @Override
    public void checkAndInitConfig() throws IllegalStateException {
        if (consumer == null) {
            consumer = getConfigManager().getConsumer().orElseThrow(() -> new IllegalStateException("ConsumerConfig is required"));
        }

        Assert.notEmpty(application, new IllegalStateException("remote application is required"));

        checkProtocol();
        checkInterface();
    }

    public void checkProtocol() {
        if (StringUtils.isNotBlank(url)) {
            protocolName = SEMICOLON_SPLIT_PATTERN.splitAsStream(this.url)
                    .filter(StringUtils::isNotEmpty)
                    .map(URLStrParser::parseDecodedStr)
                    .findFirst()
                    .map(URL::getProtocol)
                    .orElse(protocolName);
        }
        Optional<ConsumerProtocolConfig> optional;
        if (StringUtils.isNotEmpty(protocolName)) {
            optional = getConfigManager().getConsumerProtocol(protocolName);
        } else {
            optional = Optional.ofNullable(getConfigManager().getConsumerProtocols())
                    .filter(CollectionUtils::isNotEmpty).map(configs -> configs.iterator().next());
        }
        protocol = optional.orElseThrow(() -> new IllegalStateException("protocol is required"));
    }

    public void checkInterface() {
        Assert.notNull(interfaceClass, new IllegalStateException("interface is required"));
    }

    public void setInterface(String interfaceName) {
        this.setInterfaceName(interfaceName);
    }

    public void setInterface(Class<? super T> interfaceClass) {
        this.setInterfaceClass(interfaceClass);
    }

    public ConsumerConfig getConsumer() {
        return consumer;
    }

    public void setConsumer(ConsumerConfig consumer) {
        this.consumer = consumer;
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getInterfaceName() {
        return interfaceName;
    }

    @SuppressWarnings("unchecked")
    public void setInterfaceName(String interfaceName) {
        ThrowableConsumer.execute(interfaceName, name -> {
            this.interfaceName = name;
            if (name != null && name.length() > 0) {
                this.interfaceClass = (Class<? super T>) Class.forName(name, true, currentThread().getContextClassLoader());
            }
        });
    }

    public Class<? super T> getInterfaceClass() {
        return interfaceClass;
    }

    public void setInterfaceClass(Class<? super T> interfaceClass) {
        if (interfaceClass == null || !interfaceClass.isInterface()) {
            throw new IllegalStateException("The interface class " + interfaceClass + " is not a interface");
        }
        this.interfaceClass = interfaceClass;
        this.interfaceName = interfaceClass.getName();
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public FallbackFactory<?> getFallbackFactory() {
        return fallbackFactory;
    }

    public void setFallbackFactory(FallbackFactory<?> fallbackFactory) {
        this.fallbackFactory = fallbackFactory;
    }

    public List<MethodConfig> getMethods() {
        return methods;
    }

    public MethodConfig getMethodConfig(String methodName, String paramDesc) {
        return getMethodConfigOptional(methodName, paramDesc)
                .orElseThrow(() -> new IllegalStateException("MethodConfig is required"));

    }

    public Optional<MethodConfig> getMethodConfigOptional(String methodName, String paramDesc) {
        return Optional.ofNullable(getMethods())
                .map(this.methodConfigBy(methodName, paramDesc));
    }

    private Function<List<MethodConfig>, MethodConfig> methodConfigBy(String methodName, String paramDesc) {
        return methods -> {
            List<MethodConfig> methodList = methods.stream()
                    .filter(m -> StringUtils.equals(m.getName(), methodName)
                            && (StringUtils.isEmpty(m.getParamDesc()) || StringUtils.equals(m.getParamDesc(), paramDesc)))
                    .collect(Collectors.toList());

            if (methodList.isEmpty()) {
                return null;
            } else if (methodList.size() == 1) {
                return methodList.get(0);
            } else {
                return methodList.stream().filter(m -> StringUtils.equals(m.getParamDesc(), paramDesc)).findFirst().orElse(null);
            }
        };
    }

    public void setMethods(List<MethodConfig> methods) {
        this.methods = methods;
    }

    public ConsumerProtocolConfig getProtocol() {
        return protocol;
    }

    public void setProtocol(ConsumerProtocolConfig protocol) {
        this.protocol = protocol;
    }

    public String getProtocolName() {
        return protocolName;
    }

    public void setProtocolName(String protocolName) {
        this.protocolName = protocolName;
    }

    public abstract T get();

    public abstract void destroy();
}
