package cn.huolala.arch.hermes.api.config;

import static java.lang.Thread.currentThread;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import cn.huolala.arch.hermes.api.config.support.AbstractServiceConfig;
import cn.huolala.arch.hermes.common.support.function.ThrowableConsumer;
import cn.huolala.arch.hermes.common.tool.Assert;

/**
 * ServiceConfigBase
 *
 * @param <T> type
 */
public abstract class ServiceConfigBase<T> extends AbstractServiceConfig {
    private static final long serialVersionUID = 8293003441741965347L;
    
    /**
     * The provider protocols
     */
    protected List<ProtocolConfig> protocols;

    /**
     * The provider config
     */
    protected ProviderConfig provider;

    /**
     * The interface name
     */
    protected String interfaceName;

    /**
     * The interface class of the exported service
     */
    protected Class<? super T> interfaceClass;

    /**
     * The reference of the interface implementation
     */
    protected T ref;
    

    @Override
    public void checkAndInitConfig() throws IllegalStateException {
        if (provider == null) {
            provider = getConfigManager().getProvider()
                    .orElseThrow(() -> new IllegalStateException("ProviderConfig is required"));
        }

        checkProtocol();
        checkInterfaceAndRef();
    }

    public void checkProtocol() {
        protocols = Optional.of(getConfigManager().getProtocols())
                .map(ArrayList::new)
                .orElseThrow(() -> new IllegalStateException("protocol is required"));
    }

    public void checkInterfaceAndRef() {
        Assert.notNull(interfaceClass, new IllegalStateException("interface is required"));
        Assert.notNull(ref, new IllegalStateException("ref is required"));

        // reference should not be null, and is the implementation of the given interface
        if (!interfaceClass.isInstance(ref)) {
            throw new IllegalStateException("The class " + ref.getClass().getName()
                    + " unimplemented interface " + interfaceClass);
        }
    }

    public void setInterface(String interfaceName) {
        this.setInterfaceName(interfaceName);
    }

    public void setInterface(Class<? super T> interfaceClass) {
        this.setInterfaceClass(interfaceClass);
    }

    public ProviderConfig getProvider() {
        return provider;
    }

    public void setProvider(ProviderConfig provider) {
        this.provider = provider;
    }

    public String getInterfaceName() {
        return interfaceName;
    }

    @SuppressWarnings("unchecked")
    public void setInterfaceName(String interfaceName) {
        ThrowableConsumer.execute(interfaceName, name -> {
            this.interfaceName = name;
            if (name != null && name.length() > 0) {
                this.interfaceClass = (Class<? super T>) Class.forName(name, true, currentThread().getContextClassLoader());
            }
        });
    }

    public Class<? super T> getInterfaceClass() {
        return interfaceClass;
    }

    public void setInterfaceClass(Class<? super T> interfaceClass) {
        if (interfaceClass == null || !interfaceClass.isInterface()) {
            throw new IllegalStateException("The interface class " + interfaceClass + " is not a interface");
        }
        this.interfaceClass = interfaceClass;
        this.interfaceName = interfaceClass.getName();
    }

    public T getRef() {
        return ref;
    }

    public void setRef(T ref) {
        this.ref = ref;
    }
    
    public List<ProtocolConfig> getProtocols() {
        return protocols;
    }

    public void setProtocols(List<ProtocolConfig> protocols) {
        this.protocols = protocols;
    }

    public abstract void export();

    public abstract void unExport();

}
