package cn.huolala.arch.hermes.api.config.governance;

import java.util.Set;

import cn.huolala.arch.hermes.api.config.support.AbstractConfig;
import cn.huolala.arch.hermes.common.util.CollectionUtils;

public class CircuitBreakerConfig extends AbstractConfig {
    
    private static final long serialVersionUID = 2691179099024663677L;
    
    private Set<String> recordExceptions;

    public Set<String> getRecordExceptions() {
        return recordExceptions;
    }

    public void setRecordExceptions(Set<String> recordExceptions) {
        this.recordExceptions = recordExceptions;
    }
    
    @Override
    public void checkAndInitConfig() throws IllegalStateException {
        if (CollectionUtils.isNotEmpty(recordExceptions)) {
            for (String ex : recordExceptions) {
                Class<?> clazz = null;
                try {
                    clazz = Class.forName(ex);
                } catch (ClassNotFoundException e) {
                    throw new IllegalStateException("Failed to find circuitbreaker exception class: " + ex, e);
                }
                if (!Throwable.class.isAssignableFrom(clazz)) {
                    throw new IllegalStateException("Invalid circuitbreaker exception class: " + ex);
                }
            }
        }
    }
    
    @Override
    public String toString() {
        return "CircuitbreakerConfig [recordExceptions=" + recordExceptions + "]";
    }
    
}
