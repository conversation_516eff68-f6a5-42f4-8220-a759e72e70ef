package cn.huolala.arch.hermes.api.config.governance;

import java.util.Objects;

import cn.huolala.arch.hermes.api.config.support.AbstractConfig;

/**
 *  The service governance config
 */
public class GovernanceConfig extends AbstractConfig {
    
    private static final long serialVersionUID = 1101239099024113677L;
    
    /**
     * The circuitbreaker config
     */
    private CircuitBreakerConfig circuitbreaker;

    public CircuitBreakerConfig getCircuitbreaker() {
        return circuitbreaker;
    }

    public void setCircuitbreaker(CircuitBreakerConfig circuitbreaker) {
        this.circuitbreaker = circuitbreaker;
    }
    
    @Override
    public void checkAndInitConfig() throws IllegalStateException {
        if (!Objects.isNull(circuitbreaker)) {
            circuitbreaker.checkAndInitConfig();
        }
    }

    @Override
    public String toString() {
        return "GovernanceConfig [circuitbreaker=" + circuitbreaker + "]";
    }
}
