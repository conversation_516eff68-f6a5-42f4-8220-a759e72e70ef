package cn.huolala.arch.hermes.api.config.support;

import cn.huolala.arch.hermes.api.config.ApplicationConfig;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.context.ConfigManager;
import cn.huolala.arch.hermes.common.support.Marker;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static cn.huolala.arch.hermes.common.constants.Constants.COMMA_SEPARATOR_CHAR;

/**
 * Utility methods and public methods for parsing config
 */
public abstract class AbstractConfig implements Serializable, Marker {
    private static final long serialVersionUID = -2541512605815684342L;

    /**
     * tag name cache, speed up get tag name frequently
     */
    private static final Map<Class<?>, String> tagNameCache = new ConcurrentHashMap<>();

    /**
     * The suffix container
     */
    private static final String[] SUFFIXES = new String[]{"Config", "Bean", "ConfigBase"};

    protected String id;

    protected String prefix;

    /**
     * The customized parameters
     */
    protected Map<String, String> parameters;

    protected static ApplicationConfig getApplicationConfig() {
        return ApplicationContext.getApplicationConfig();
    }

    protected static ConfigManager getConfigManager() {
        return ApplicationContext.getConfigManager();
    }

    public static String getTagName(Class<?> cls) {
        return tagNameCache.computeIfAbsent(cls, key -> {
            String tag = key.getSimpleName();
            for (String suffix : SUFFIXES) {
                if (tag.endsWith(suffix)) {
                    tag = tag.substring(0, tag.length() - suffix.length());
                    break;
                }
            }
            return StringUtils.camelToSplitName(tag, "-");
        });
    }

    /**
     * load Parameters
     *
     * @see Parameter
     */
    public static Map<String, String> loadParameters(Object config) {
        Field[] fields = config.getClass().getDeclaredFields();
        if (fields.length == 0) {
            return CollectionUtils.emptyHashMap();
        }

        try {
            Map<String, String> result = new HashMap<>();
            for (Field field : fields) {
                if (field.isAnnotationPresent(Parameter.class)) {
                    Parameter parameter = field.getAnnotation(Parameter.class);
                    String[] keys = parameter.value();
                    if (keys == null || keys.length == 0) {
                        keys = new String[]{field.getName()};
                    }

                    ReflectUtils.makeAccessible(field);
                    String value = getParameterString(field.get(config));
                    if (StringUtils.isNotEmpty(value)) {
                        for (String key : keys) {
                            result.put(key, value);
                        }
                    }
                }
            }
            return result;
        } catch (IllegalAccessException e) {
            throw new IllegalStateException(e);
        }
    }

    protected static String getParameterString(Object value) {
        if (value == null) {
            return null;
        }

        String str = null;
        if (value instanceof Collection) {
            str = ((Collection<?>) value).stream()
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .collect(Collectors.joining(String.valueOf(COMMA_SEPARATOR_CHAR)));
        } else if (value instanceof Map) {
            str = ((Map<?, ?>) value).entrySet().stream()
                    .filter(kv -> kv.getKey() instanceof String)
                    .map(kv -> kv.getKey() + (kv.getValue() == null ? "" : "=" + kv.getValue()))
                    .collect(Collectors.joining(String.valueOf(COMMA_SEPARATOR_CHAR)));
        }

        return str != null ? str : value.toString();
    }

    /**
     * fail fast check and init config
     */
    public void checkAndInitConfig() throws IllegalStateException {
    }

    /**
     * load Parameters
     *
     * @see Parameter
     */
    protected void loadParameters() {
        Map<String, String> loadParameters = loadParameters(this);
        if (CollectionUtils.isNotEmptyMap(loadParameters)) {
            this.parameters = Optional.ofNullable(this.parameters).orElse(new HashMap<>());
            this.parameters.putAll(loadParameters);
        }
    }

    /**
     * merge AbstractConfig parameters to self parameters
     */
    public void mergeParameters(AbstractConfig config) {
        mergeParameters(config.getParameters());
    }

    /**
     * merge parameters to self parameters
     */
    public void mergeParameters(Map<String, String> parameters) {
        if (CollectionUtils.isNotEmptyMap(parameters)) {
            this.parameters = Optional.ofNullable(this.parameters).orElse(new HashMap<>());
            this.parameters.putAll(parameters);
        }
    }

    /**
     * Add {@link AbstractConfig instance} into {@link ConfigManager}
     * <p>
     * Current method will invoke by Spring or Java EE container automatically, or should be triggered manually
     */
    @PostConstruct
    public void addIntoConfigManager() {
        getConfigManager().addConfig(this);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public Map<String, String> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, String> parameters) {
        this.parameters = parameters;
    }
}