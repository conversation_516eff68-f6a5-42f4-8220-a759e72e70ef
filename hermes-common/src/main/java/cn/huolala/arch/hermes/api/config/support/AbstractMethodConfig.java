package cn.huolala.arch.hermes.api.config.support;

/**
 * AbstractMethodConfig
 */
public abstract class AbstractMethodConfig extends AbstractConfig {
    private static final long serialVersionUID = -1805444872298144286L;

    /**
     * The timeout for remote invocation in milliseconds
     */
    protected Integer timeout;
    
    /**
     * The connect timout in milliseconds
     */
    protected Integer connectionTimeout;
    
    /**
     * The load balance
     */
    protected String loadbalance;

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public Integer getConnectionTimeout() {
        return connectionTimeout;
    }

    public void setConnectionTimeout(Integer connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }

    public String getLoadbalance() {
        return loadbalance;
    }

    public void setLoadbalance(String loadbalance) {
        this.loadbalance = loadbalance;
    }
}
