package cn.huolala.arch.hermes.api.config.support;

/**
 * AbstractReferenceConfig
 */
public abstract class AbstractReferenceConfig extends AbstractMethodConfig {
    private static final long serialVersionUID = -2865552534159870162L;

    /**
     * Cluster type
     */
    protected String cluster;

    /**
     * 应用分组
     */
    protected String group;

    public String getCluster() {
        return cluster;
    }

    public void setCluster(String cluster) {
        this.cluster = cluster;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }
}
