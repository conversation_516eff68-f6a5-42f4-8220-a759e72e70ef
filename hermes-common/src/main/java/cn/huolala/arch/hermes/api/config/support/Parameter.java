package cn.huolala.arch.hermes.api.config.support;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Parameter
 *
 * @see AbstractConfig#loadParameters(Object)
 * @see AbstractConfig#loadParameters()
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface Parameter {
    /**
     * Parameter keys, default field name
     */
    String[] value() default {};
}