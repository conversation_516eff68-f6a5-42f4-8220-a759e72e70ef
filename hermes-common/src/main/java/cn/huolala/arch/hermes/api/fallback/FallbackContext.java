package cn.huolala.arch.hermes.api.fallback;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.spec.classification.ApiStability;

/**
 * Fallback Context
 */
@ApiAudience.Public
@ApiStability.Stable
public interface FallbackContext {
    /**
     * get commandKey
     */
    String getCommandKey();

    /**
     * get the interface name
     */
    String getServiceName();

    /**
     * get method name.
     */
    String getMethodName();

    /**
     * get method arguments
     */
    Object[] getArguments();

    /**
     * get parameter types
     */
    Class<?>[] getParameterTypes();

    /**
     * get return type
     */
    Class<?> getReturnType();

    /**
     * get exception
     */
    Throwable getThrowable();
}
