package cn.huolala.arch.hermes.api.http;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static cn.huolala.arch.hermes.api.http.ParamsMode.STANDARD;

/**
 * HttpMethod
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@Inherited
public @interface HttpMethod {
    /**
     * @see RequestMethod
     */
    RequestMethod method() default RequestMethod.GET;

    /**
     * @see ParamsMode
     */
    ParamsMode paramsMode() default STANDARD;

    /**
     * @see ResultMode
     */
    ResultMode resultMode() default ResultMode.OBJECT;

    /**
     * complete result
     */
    boolean completeResult() default false;

    /**
     * fixed params append to url
     */
    boolean fixedParamsToUrl() default false;
}
