package cn.huolala.arch.hermes.api.tool;

import com.google.common.util.concurrent.AbstractFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;

import javax.annotation.Nonnull;
import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.function.Consumer;

import static com.google.common.base.Preconditions.checkNotNull;

/**
 * MoreFutures provides additional utility methods for working with {@link ListenableFuture}, on top of those provided
 * by Guava's {@link Futures} class.
 */
public final class MoreFutures {
    private MoreFutures() {
        // prevent instantiation
    }

    /**
     * Registers separate success and failure callbacks to be run when the {@code Future}'s
     * computation is {@linkplain java.util.concurrent.Future#isDone() complete} or, if the
     * computation is already complete, immediately.
     *
     * @see Futures#addCallback(ListenableFuture, FutureCallback, Executor)
     * @param future The future attach the callback to.
     * @param success A {@link Consumer} to execute when the future succeeds.
     * @param failure A {@link Consumer} to execute when the future fails.
     * @param executor The executor to run {@code callback} when the future completes.
     */
    public static <V> void addCallback(
            @Nonnull final ListenableFuture<V> future,
            @Nonnull final Consumer<V> success,
            @Nonnull final Consumer<Throwable> failure,
            @Nonnull final Executor executor) {
        checkNotNull(future, "future");
        checkNotNull(success, "success");
        checkNotNull(failure, "failure");
        checkNotNull(executor, "executor");

        FutureCallback<V> futureCallback = new FutureCallback<V>() {
            @Override
            public void onSuccess(V result) {
                success.accept(result);
            }

            @Override
            public void onFailure(Throwable t) {
                failure.accept(t);
            }
        };
        Futures.addCallback(future, futureCallback, executor);
    }

    /**
     * Registers a success callback to be run when the {@code Future}'s
     * computation is {@linkplain java.util.concurrent.Future#isDone() complete} or, if the
     * computation is already complete, immediately.
     *
     * @see Futures#addCallback(ListenableFuture, FutureCallback, Executor)
     * @param future The future attach the callback to.
     * @param success A {@link Consumer} to execute when the future succeeds.
     * @param executor The executor to run {@code callback} when the future completes.
     */
    public static <V> void onSuccess(@Nonnull final ListenableFuture<V> future,
                                     @Nonnull final Consumer<V> success,
                                     @Nonnull final Executor executor) {
        checkNotNull(future, "future");
        checkNotNull(success, "success");
        checkNotNull(executor, "executor");

        addCallback(future, success, throwable -> { }, executor);
    }

    /**
     * Registers a failure callback to be run when the {@code Future}'s
     * computation is {@linkplain java.util.concurrent.Future#isDone() complete} or, if the
     * computation is already complete, immediately.
     *
     * @see Futures#addCallback(ListenableFuture, FutureCallback, Executor)
     * @param future The future attach the callback to.
     * @param failure A {@link Consumer} to execute when the future fails.
     * @param executor The executor to run {@code callback} when the future completes.
     */
    public static <V> void onFailure(@Nonnull final ListenableFuture<V> future,
                                     @Nonnull final Consumer<Throwable> failure,
                                     @Nonnull final Executor executor) {
        checkNotNull(future, "future");
        checkNotNull(failure, "failure");
        checkNotNull(executor, "executor");

        addCallback(future, v -> { }, failure, executor);
    }

    /**
     * Converts a Guava {@link ListenableFuture} into a JDK {@link CompletableFuture}, preserving value, exception,
     * and cancellation propagation.
     *
     * <p>The resulting {@link CompletableFuture} acts on the same {@link Executor} as the provided {@link ListenableFuture}.
     * @param listenableFuture A {@link ListenableFuture} to adapt.
     * @return A new {@link CompletableFuture}.
     */
    public static <V> CompletableFuture<V> toCompletableFuture(@Nonnull final ListenableFuture<V> listenableFuture) {
        checkNotNull(listenableFuture, "listenableFuture");

        // Setup backward cancellation propagation CF -> LF
        final CompletableFuture<V> completableFuture = new CompletableFuture<V>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return super.cancel(mayInterruptIfRunning) && listenableFuture.cancel(mayInterruptIfRunning);
            }

            // https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/CompletableFuture.html
            // CompletableFuture.cancel(bool) is the same as CompleteExceptionally(new CancellationException())
            @Override
            public boolean completeExceptionally(Throwable ex) {
                if (ex instanceof CancellationException) {
                    listenableFuture.cancel(true);
                }
                return super.completeExceptionally(ex);
            }
        };

        // Setup forward event propagation LF -> CF
        Runnable callbackRunnable = () -> {
            try {
                final V value = listenableFuture.get();
                completableFuture.complete(value);
            } catch (CancellationException ex) {
                // the ListenableFuture was cancelled
                completableFuture.cancel(true);
            } catch (ExecutionException ex) {
                // the ListenableFuture failed with a exception
                completableFuture.completeExceptionally(ex.getCause());
            } catch (RuntimeException | Error | InterruptedException ex) {
                // the ListenableFuture failed with a REALLY BAD exception
                // Won't happen since get() only called after completion
                completableFuture.completeExceptionally(ex);
            }

        };
        listenableFuture.addListener(callbackRunnable, MoreExecutors.directExecutor());

        return completableFuture;
    }

    /**
     * Converts a JDK {@link CompletableFuture} into a Guava {@link ListenableFuture}, preserving value, exception,
     * and cancellation propagation.
     *
     * <p>The resulting {@link ListenableFuture} acts on the same {@link Executor} as the provided {@link CompletableFuture}.
     * @param completableFuture A {@link CompletableFuture} to adapt.
     * @return A new {@link ListenableFuture}.
     */
    public static <V> ListenableFuture<V> fromCompletableFuture(@Nonnull final CompletableFuture<V> completableFuture) {
        checkNotNull(completableFuture, "completableFuture");

        // Setup backward cancellation propagation LF -> CF
        Settable<V> listenableFuture = new Settable<V>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return super.cancel(mayInterruptIfRunning) && completableFuture.cancel(mayInterruptIfRunning);
            }

            @Override
            public boolean setException(Throwable ex) {
                if (ex instanceof CancellationException) {
                    completableFuture.cancel(true);
                }
                return super.setException(ex);
            }
        };

        // Setup forward event propagation CF -> LF
        completableFuture.whenComplete((value, ex) -> {
            if (value != null) {
                listenableFuture.set(value);
            } else {
                if (ex instanceof CancellationException) {
                    listenableFuture.cancel(true);
                } else {
                    listenableFuture.setException(ex);
                }
            }
        });

        return listenableFuture;
    }

    /**
     * A private helper class to assist fromCompletableFuture().
     * @param <V> The Settable future's type.
     */
    private static class Settable<V> extends AbstractFuture<V> {
        @Override
        public boolean set(V value) {
            return super.set(value);
        }

        @Override
        public boolean setException(Throwable throwable) {
            return super.setException(throwable);
        }
    }
}
