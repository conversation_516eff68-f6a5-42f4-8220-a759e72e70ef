package cn.huolala.arch.hermes.common.bytecode;

import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.shaded.javassist.ClassPath;

import java.io.InputStream;
import java.lang.ref.WeakReference;
import java.net.URL;

/**
 * A class search-path representing a class loader.
 *
 * <p>It is used for obtaining a class file from the given
 * class loader by <code>getResourceAsStream()</code>.
 * The <code>LoaderClassPath</code> refers to the class loader through
 * <code>WeakReference</code>.  If the class loader is garbage collected,
 * the other search paths are examined.
 *
 * <p>The given class loader must have both <code>getResourceAsStream()</code>
 * and <code>getResource()</code>.
 */
public class CustomizedLoaderClassPath implements ClassPath {
    private WeakReference<ClassLoader> clRef;

    /**
     * Creates a search path representing a class loader.
     */
    public CustomizedLoaderClassPath(ClassLoader cl) {
        clRef = new WeakReference<>(cl);
    }

    public String toString() {
        ClassLoader cl = null;
        if (clRef != null) {
            cl = clRef.get();
        }

        return cl == null ? "<null>" : cl.toString();
    }

    /**
     * Obtains a class file from the class loader.
     * This method calls <code>getResourceAsStream(String)</code>
     * on the class loader.
     */
    public InputStream openClassfile(String classname) {
        String cname = classname.replace('.', '/') + ".class";
        ClassLoader cl = clRef.get();
        if (cl == null) {
            // not found
            return null;
        } else {
            InputStream result;
            if (classname.startsWith(Constants.PACKAGE_NAME) && cl != this.getClass().getClassLoader()) {
                result = this.getClass().getClassLoader().getResourceAsStream(cname);
                return result != null ? result : cl.getResourceAsStream(cname);
            } else {
                result = cl.getResourceAsStream(cname);
                if (result == null && cl != this.getClass().getClassLoader()) {
                    return this.getClass().getClassLoader().getResourceAsStream(cname);
                }
                return result;
            }
        }
    }

    /**
     * Obtains the URL of the specified class file.
     * This method calls <code>getResource(String)</code>
     * on the class loader.
     *
     * @return null if the class file could not be found.
     */
    public URL find(String classname) {
        String cname = classname.replace('.', '/') + ".class";
        ClassLoader cl = clRef.get();
        if (cl == null) {
            // not found
            return null;
        } else {
            URL url;
            if (classname.startsWith(Constants.PACKAGE_NAME) && cl != this.getClass().getClassLoader()) {
                url = this.getClass().getClassLoader().getResource(cname);
                return url != null ? url : cl.getResource(cname);
            } else {
                url = cl.getResource(cname);
                if (url == null && cl != this.getClass().getClassLoader()) {
                    return this.getClass().getClassLoader().getResource(cname);
                }
                return url;
            }
        }
    }

    /**
     * Closes this class path.
     */
    public void close() {
        clRef = null;
    }
}
