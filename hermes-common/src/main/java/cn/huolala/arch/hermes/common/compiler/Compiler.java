package cn.huolala.arch.hermes.common.compiler;

import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.common.compiler.javassist.JavassistCompiler;

/**
 * Compiler
 */
@SPI(JavassistCompiler.NAME)
public interface Compiler {

    /**
     * Compile java source code.
     *
     * @param code        Java source code
     * @param classLoader classloader
     * @return Compiled class
     */
    Class<?> compile(String code, ClassLoader classLoader);
}
