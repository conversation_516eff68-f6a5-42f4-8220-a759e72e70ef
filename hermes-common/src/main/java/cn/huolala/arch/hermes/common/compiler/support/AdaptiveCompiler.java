package cn.huolala.arch.hermes.common.compiler.support;

import cn.huolala.arch.hermes.common.compiler.Compiler;
import cn.huolala.arch.hermes.common.extension.Adaptive;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.util.StringUtils;

import java.util.Objects;

/**
 * AdaptiveCompiler. (Singleton, ThreadSafe)
 */
@Adaptive
public class AdaptiveCompiler implements Compiler {
    private static volatile String defaultCompiler;

    public static void setDefaultCompiler(String compiler) {
        defaultCompiler = compiler;
    }

    @Override
    public Class<?> compile(String code, ClassLoader classLoader) {
        ExtensionLoader<Compiler> loader = ExtensionLoader
                .getExtensionLoader(Compiler.class);

        Compiler compiler;
        String name = defaultCompiler;
        if (StringUtils.isNotEmpty(name)) {
            compiler = loader.getExtension(name);
        } else {
            compiler = loader.getDefaultExtension();
        }

        return Objects.requireNonNull(compiler).compile(code, classLoader);
    }

}
