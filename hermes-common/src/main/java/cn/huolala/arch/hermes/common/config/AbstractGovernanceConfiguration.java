package cn.huolala.arch.hermes.common.config;

import cn.huolala.arch.hermes.common.config.dynamic.DynamicConfiguration;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigEvent;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigEventType;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigurationListener;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.rule.AbstractGovernanceRule;
import cn.huolala.arch.hermes.common.rule.RouterConstants;
import cn.huolala.arch.hermes.common.tool.Pair;

import java.util.Collections;
import java.util.Optional;
import java.util.Set;


public abstract class AbstractGovernanceConfiguration implements ConfigurationListener {

    private static final Logger logger = LoggerFactory.getLogger(AbstractGovernanceRule.class);

    protected static final String GLOBAL_NAMESPACE = RouterConstants.Namespace.GLOBAL_GOVERNANCE.getValue();
    protected static final String APP_NAMESPACE = RouterConstants.Namespace.GOVERNANCE.getValue();

    protected void startListen() {
        // add rule config listener
        Pair<Pair<Set<String>, Set<String>>, Pair<Set<String>, Set<String>>> interestedRuleKeys = interestedRuleKeys();
        Pair<Set<String>, Set<String>> globalRuleKeys = interestedRuleKeys.getLeft();
        Pair<Set<String>, Set<String>> appRuleKeys = interestedRuleKeys.getRight();

        DynamicConfiguration dynamicConfiguration = DynamicConfiguration.getDynamicConfiguration();
        dynamicConfiguration.addListener(this, GLOBAL_NAMESPACE, globalRuleKeys.getLeft(), globalRuleKeys.getRight(), true);
        dynamicConfiguration.addListener(this, APP_NAMESPACE, appRuleKeys.getLeft(), appRuleKeys.getRight(), true);
    }

    @Override
    public void onEvent(ConfigEvent event) {
        logger.info(getClass().getSimpleName() + " Governance config changed, event:" + event);

        String group = event.getGroup();

        if (GLOBAL_NAMESPACE.equals(group)) {
            onGlobalConfigChange(event);
        } else if (APP_NAMESPACE.equals(group)) {
            onAppConfigChange(event);
        }

        onRuleChange(event);
    }

    protected boolean processEnabled(ConfigEvent event, boolean defaultEnable) {
        if (event.getChangeType() == ConfigEventType.DELETED) {
            return defaultEnable;
        } else {
            String content = event.getContent();
            if (Boolean.TRUE.toString().equals(content) || Boolean.FALSE.toString().equals(content)) {
                return Boolean.parseBoolean(content);
            } else {
                return defaultEnable;
            }
        }
    }

    /**
     * real Global ConfigKey
     */
    protected Optional<String> realGlobalConfigKey(String key) {
        String prefix = globalRuleKeyPrefix();
        return Optional.ofNullable(key)
                .filter(v -> v.startsWith(prefix))
                .map(v -> v.substring(prefix.length()));
    }

    /**
     * real App ConfigKey
     */
    protected Optional<String> realAppConfigKey(String key) {
        String prefix = appRuleKeyPrefix();
        return Optional.ofNullable(key)
                .filter(v -> v.startsWith(prefix))
                .map(v -> v.substring(prefix.length()));
    }

    /**
     * get interested governance rule keys
     *
     * @return [global[keys, keyPrefixes], app[keys, keyPrefixes]]
     */
    protected Pair<Pair<Set<String>, Set<String>>, Pair<Set<String>, Set<String>>> interestedRuleKeys() {
        Set<String> globalKeyPrefixes = Collections.singleton(globalRuleKeyPrefix());
        Set<String> appKeyPrefixes = Collections.singleton(appRuleKeyPrefix());

        return Pair.of(Pair.of(Collections.emptySet(), globalKeyPrefixes),
                Pair.of(Collections.emptySet(), appKeyPrefixes));
    }

    protected String globalRuleKeyPrefix() {
        return ruleKeyPrefix() + ".global.";
    }

    protected String appRuleKeyPrefix() {
        return ruleKeyPrefix() + ".app.";
    }

    protected abstract void onGlobalConfigChange(ConfigEvent event);

    protected abstract void onAppConfigChange(ConfigEvent event);

    /**
     * on all rule config change
     *
     * @param event config change event
     */
    protected abstract void onRuleChange(ConfigEvent event);

    /**
     * interested governance rule prefix
     */
    protected abstract String ruleKeyPrefix();

}
