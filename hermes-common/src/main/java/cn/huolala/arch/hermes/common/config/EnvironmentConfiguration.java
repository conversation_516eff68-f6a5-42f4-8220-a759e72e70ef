package cn.huolala.arch.hermes.common.config;

import cn.huolala.arch.hermes.common.util.StringUtils;

/**
 * Configuration from system environment
 */
public class EnvironmentConfiguration implements Configuration {

    @Override
    public Object getInternalProperty(String key) {
        String value = System.getenv(key);
        if (StringUtils.isEmpty(value)) {
            value = System.getenv(StringUtils.toOSStyleKey(key));
        }
        return value;
    }

}