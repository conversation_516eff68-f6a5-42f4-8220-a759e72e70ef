package cn.huolala.arch.hermes.common.config;


import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.util.ConfigUtils;

import java.util.Properties;

/**
 * Configuration from system properties and properties file
 * @see Constants#DEFAULT_PROPERTIES;
 */
public class PropertiesConfiguration implements Configuration {
    private static volatile Properties properties;

    private static Properties getProperties() {
        if (properties == null) {
            synchronized (PropertiesConfiguration.class) {
                if (properties == null) {
                    String path = System.getProperty(Constants.PROPERTIES_KEY);
                    if (path == null || path.length() == 0) {
                        path = System.getenv(Constants.PROPERTIES_KEY);
                        if (path == null || path.length() == 0) {
                            path = Constants.DEFAULT_PROPERTIES;
                        }
                    }
                    properties = ConfigUtils.loadProperties(path, false, true);
                }
            }
        }
        return properties;
    }


    public PropertiesConfiguration() {
        // load the default properties once
        PropertiesConfiguration.getProperties();
    }

    @Override
    public Object getInternalProperty(String key) {
        String value = System.getProperty(key);
        if (value != null && value.length() > 0) {
            return value;
        }
        Properties properties = PropertiesConfiguration.getProperties();
        return ConfigUtils.replaceProperty(properties.getProperty(key), properties);
    }
}
