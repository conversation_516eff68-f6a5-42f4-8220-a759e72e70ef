package cn.huolala.arch.hermes.common.config.dynamic;

import cn.huolala.arch.hermes.common.URL;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static cn.huolala.arch.hermes.common.constants.Constants.DEFAULT_KEY;

/**
 * Abstract {@link DynamicConfigurationFactory} implementation with cache ability
 */
public abstract class AbstractDynamicConfigurationFactory implements DynamicConfigurationFactory {

    private final Map<String, DynamicConfiguration> dynamicConfigurations = new ConcurrentHashMap<>();

    @Override
    public final DynamicConfiguration getDynamicConfiguration(URL url) {
        String key = url == null ? DEFAULT_KEY : url.toServiceString();
        return dynamicConfigurations.computeIfAbsent(key, k -> createDynamicConfiguration(url));
    }

    protected abstract DynamicConfiguration createDynamicConfiguration(URL url);
}
