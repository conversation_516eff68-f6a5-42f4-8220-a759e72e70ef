package cn.huolala.arch.hermes.common.config.dynamic;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.config.Configuration;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigurationListener;
import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.exception.ConfigException;

import java.util.Collections;
import java.util.Set;
import java.util.SortedSet;

/**
 * Dynamic Configuration
 * <br/>
 * From the use scenario internally inside framework, there are mainly three kinds of methods:
 * <ol>
 * <li>{@link #getProperties(String, String, long)}, get configuration file from Config Center at start up.</li>
 * <li>{@link #addListener(ConfigurationListener, String, Set, Set)}/ {@link #removeListener(ConfigurationListener, String)}
 * , add or remove listeners for governance rules or config items that need to watch.</li>
 * <li>{@link #getProperty(String, Object)}, get a single config item.</li>
 * <li>{@link #getConfig(String, String, long)}, get the specified config</li>
 * </ol>
 */
public interface DynamicConfiguration extends Configuration, AutoCloseable {

    String DEFAULT_GROUP = Constants.NAME;

    /**
     * get default DynamicConfiguration instance
     */
    static DynamicConfiguration getDynamicConfiguration() {
        return ApplicationContext.getEnvironment().getDynamicConfiguration();
    }

    /**
     * {@link #addListener(ConfigurationListener, String, Set, Set)}
     *
     * @see #getDefaultGroup()
     */
    default void addListener(ConfigurationListener listener, String group) {
        addListener(listener, group, Collections.emptySet(), Collections.emptySet(), false);
    }

    /**
     * {@link #addListener(ConfigurationListener, String, Set, Set)}
     */
    default void addListener(ConfigurationListener listener, String group, String key) {
        addListener(listener, group, Collections.singleton(key), Collections.emptySet(), false);
    }

    /**
     * Register a configuration listener
     *
     * @param listener configuration listener
     * @param group the group where the key belongs to
     * @param keys the keys that the listener is interested in
     * @param keyPrefixes the key prefixes that the listener is interested in
     */
    void addListener(ConfigurationListener listener, String group, Set<String> keys, Set<String> keyPrefixes, boolean isManualTrigger);

    /**
     * Remove the configuration listener
     *
     * @param listener configuration listener
     * @param group  the group where the key belongs to
     */
    void removeListener(ConfigurationListener listener, String group);

    /**
     * Get the configuration mapped to the given key and the given group with {@link #getDefaultTimeout() the default
     * timeout}
     *
     * @param key the key to represent a configuration
     * @param group the group where the key belongs to
     * @return target configuration mapped to the given key and the given group
     */
    default String getConfig(String key, String group) {
        return getConfig(key, group, getDefaultTimeout());
    }

    /**
     * Get the configuration mapped to the given key and the given group. If the
     * configuration fails to fetch after timeout exceeds, ConfigException will be thrown.
     *
     * @param key the key to represent a configuration
     * @param group the group where the key belongs to
     * @param timeout timeout value for fetching the target config
     * @return target configuration mapped to the given key and the given group, ConfigException will be thrown
     * if timeout exceeds.
     */
    String getConfig(String key, String group, long timeout) throws ConfigException;

    /**
     * This method are mostly used to get a compound config file with {@link #getDefaultTimeout() the default timeout}
     */
    default String getProperties(String key, String group) throws ConfigException {
        return getProperties(key, group, getDefaultTimeout());
    }

    /**
     * This method are mostly used to get a compound config file
     */
    default String getProperties(String key, String group, long timeout) throws ConfigException {
        return getConfig(key, group, timeout);
    }

    /**
     * Publish Config mapped to the given key and the given group.
     *
     * @param key the key to represent a configuration
     * @param group the group where the key belongs to
     * @param content the content of configuration
     * @return <code>true</code> if success, or <code>false</code>
     * @throws UnsupportedOperationException If the under layer does not support
     */
    default boolean publishConfig(String key, String group, String content) throws UnsupportedOperationException {
        throw new UnsupportedOperationException();
    }

    /**
     * @param key the key to represent a configuration
     * @param group the group where the key belongs to
     * @return <code>true</code> if success, or <code>false</code>
     * @throws UnsupportedOperationException If the under layer does not support
     */
    default boolean removeConfig(String key, String group) throws UnsupportedOperationException {
        throw new UnsupportedOperationException();
    }

    /**
     * Get the config keys by the specified group
     *
     * @param group the specified group
     * @return the read-only non-null sorted {@link Set set} of config keys
     * @throws UnsupportedOperationException If the under layer does not support
     */
    default SortedSet<String> getConfigKeys(String group) throws UnsupportedOperationException {
        throw new UnsupportedOperationException();
    }

    /**
     * Get the default group for the operations
     *
     * @return The default value is {@link #DEFAULT_GROUP}
     */
    default String getDefaultGroup() {
        return DEFAULT_GROUP;
    }

    /**
     * Get the default timeout for the operations in milliseconds
     *
     * @return The default value is <code>-1L</code>
     */
    default long getDefaultTimeout() {
        return -1L;
    }

    /**
     * Close the configuration
     */
    @Override
    default void close() throws Exception {
        throw new UnsupportedOperationException();
    }

    /**
     * The format is '{interfaceName}:[version]:[group]'
     */
    static String getRuleKey(URL url) {
        return url.getColonSeparatedKey();
    }
}
