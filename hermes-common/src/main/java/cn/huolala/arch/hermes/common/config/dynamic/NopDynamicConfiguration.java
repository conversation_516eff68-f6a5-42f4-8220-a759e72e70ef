package cn.huolala.arch.hermes.common.config.dynamic;

import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigurationListener;
import cn.huolala.arch.hermes.common.exception.ConfigException;

import java.util.Set;

/**
 * If user does not specify a config center, it will default to this one
 */
public class NopDynamicConfiguration implements DynamicConfiguration {

    @Override
    public Object getInternalProperty(String key) {
        // no-op
        return null;
    }

    @Override
    public void addListener(ConfigurationListener listener, String group, Set<String> keys, Set<String> keyPrefixes, boolean isManualTrigger) {
        // no-op
    }

    @Override
    public void removeListener(ConfigurationListener listener, String group) {
        // no-op
    }

    @Override
    public String getConfig(String key, String group, long timeout) throws ConfigException {
        // no-op
        return null;
    }
}