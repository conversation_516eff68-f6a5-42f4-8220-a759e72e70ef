package cn.huolala.arch.hermes.common.config.dynamic.event;

import cn.huolala.arch.hermes.common.event.Event;

import java.util.Objects;

import static cn.huolala.arch.hermes.common.constants.Constants.COMMA_SEPARATOR;

/**
 * An event raised when the config changed, immutable.
 *
 * @see ConfigEventType
 */
public class ConfigEvent extends Event {
    private static final long serialVersionUID = -1678035374070569621L;

    private final String key;

    // or namespace
    private final String group;

    private final String content;

    private final ConfigEventType changeType;

    public ConfigEvent(String key, String group, String content) {
        this(key, group, content, ConfigEventType.MODIFIED);
    }

    public ConfigEvent(String key, String group, String content, ConfigEventType changeType) {
        super(key + COMMA_SEPARATOR + group);
        this.key = key;
        this.group = group;
        this.content = content;
        this.changeType = changeType;
    }

    public String getKey() {
        return key;
    }

    public String getGroup() {
        return group;
    }

    public String getContent() {
        return content;
    }

    public ConfigEventType getChangeType() {
        return changeType;
    }

    @Override
    public String toString() {
        return "ConfigEvent{"
                + "key='" + key + '\''
                + ", group='" + group + '\''
                + ", content='" + content + '\''
                + ", changeType=" + changeType
                + "} " + super.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ConfigEvent)) {
            return false;
        }
        ConfigEvent that = (ConfigEvent) o;
        return Objects.equals(getKey(), that.getKey())
                && Objects.equals(getGroup(), that.getGroup())
                && Objects.equals(getContent(), that.getContent())
                && getChangeType() == that.getChangeType();
    }

    @Override
    public int hashCode() {
        return Objects.hash(getKey(), getGroup(), getContent(), getChangeType());
    }
}
