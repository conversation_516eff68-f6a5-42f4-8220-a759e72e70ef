package cn.huolala.arch.hermes.common.constants;

import java.util.regex.Pattern;

/**
 * Common Constants
 */
public interface Constants extends ConstantsMarker {
    String NAME = "hermes";
    String PACKAGE_NAME = "cn.huolala.arch.hermes";
    String ALL = "*";

    String DEFAULT_PROPERTIES = NAME + ".properties";
    String PROPERTIES_KEY = DEFAULT_PROPERTIES + ".file";
    String HERMES_VERSION_KEY = "hermes.version";

    String APPLICATION_KEY = "application";
    String ORGANIZATION_KEY = "organization";
    String OWNER_KEY = "owner";
    String REMOTE_APPLICATION_KEY = "remote.application";
    String PROXY_KEY = "proxy";
    String SIDE_KEY = "side";
    String PROVIDER = "provider";
    String CONSUMER = "consumer";
    String ACCESS_LOG_KEY = "accesslog";
    String INVOKER_LOG_KEY = "invokerlog";

    String ENV_KEY = "env";
    String DEFAULT_ENV = "dev";

    String ADMIN_HOST_KEY = "admin.host";
    String HOSTPORTS_ENABLED_KEY = "hostports.enabled";

    String DEFAULT_KEY = "default";
    String DEFAULT_KEY_PREFIX = "default.";
    String UNDERLINE_SEPARATOR = "_";
    String REMOVE_VALUE_PREFIX = "-";

    String ANYHOST_KEY = "anyhost";
    String ANYHOST_VALUE = "0.0.0.0";
    String LOCALHOST_KEY = "localhost";
    String LOCALHOST_VALUE = "127.0.0.1";

    String CLUSTER_KEY = "cluster";
    String USERNAME_KEY = "username";
    String PASSWORD_KEY = "password";
    String HOST_KEY = "host";
    String PORT_KEY = "port";

    String BIND_IP_KEY = "bind.ip";
    String BIND_PORT_KEY = "bind.port";

    String METRICS_KEY_PREFIX = "hllci_";

    String MUTABLE_TAG_PREFIX = "hll";

    // keys
    String GROUP_KEY = "group";
    String PATH_KEY = "path";
    String INTERFACE_KEY = "interface";
    String INSTANCE_KEY = "instance";
    String METHODS_KEY = "methods";
    String VERSION_KEY = "version";
    String REVISION_KEY = "revision";
    String DEFAULT_VERSION = "1.0.0";
    String TIMESTAMP_KEY = "timestamp";
    String BACKUP_KEY = "backup";
    String DISPATCHER_KEY = "dispatcher";
    String LOADBALANCE_KEY = "loadbalance";
    String DELAY_KEY = "delay";
    String WAIT_KEY = "wait";
    String TOKEN_KEY = "token";
    String TAGS_KEY = "tags";
    String METADATA_KEY = "metadata";
    String PAYLOAD_KEY = "payload";
    String ACCEPTS_KEY = "accepts";
    String IDLE_TIMEOUT_KEY = "idle-timeout";
    String CONNECT_AT_START_KEY = "connect-at-start";
    String SERVICE_FILTRATE_TAG_KEY = "service.filtrate.tag";
    String SERVICE_FILTRATE_DISABLED = NAME + ".consumer.service-filter.disabled";
    String FALLBACK_KEY = "fallback";
    String URL_KEY = "url";
    String HTTPS_KEY = "https";
    String VERSION_TAG_KEY = "serviceVersion";
    String ENABLE_KEY = "enable";

    // protocol
    String PROTOCOL_KEY = "protocol";
    String DEFAULT_PROTOCOL = "grpc";
    String GRPC_PROTOCOL = "grpc";
    String JSONRPC_PROTOCOL = "jsonrpc";
    String HTTP_PROTOCOL = "http";
    String DISCOVERY_PROTOCOL = "discovery";
    String CONFIG_CENTER_PROTOCOL = "configcenter";
    String PROVIDER_PROTOCOL = "provider";
    String CONSUMER_PROTOCOL = "consumer";
    String PROTOCOL_HEADER_PREFIX = "x-hermes-";

    // filter & listener & interceptor
    String SERVICE_FILTER_KEY = "service.filter";
    String REFERENCE_FILTER_KEY = "reference.filter";

    // timeout
    String TIMEOUT_KEY = "timeout";
    String CONNECTION_TIMEOUT_KEY = "connection.timeout";
    int DEFAULT_TIMEOUT = 6000;
    int DEFAULT_CONNECTION_TIMEOUT = 1000;

    // config
    String CONFIG_NAMESPACE_KEY = "namespace";
    int CONFIG_GOVERNANCE_DEFAULT_TIMEOUT = 5000;

    // default values
    int DEFAULT_REGISTER_DELAY = 5000;
    int DEFAULT_SHUTDOWN_WAIT = 5000;

    // through pass
    String RPC_CONTEXT_PREFIX = "x-hll-";
    String RPC_CONTEXT_BASE_PREFIX = RPC_CONTEXT_PREFIX + "base-";
    String UP_STREAM_APP_ID = RPC_CONTEXT_BASE_PREFIX + "upstream-appid";

    String RPC_HYPHEN_PREFIX = "-";
    String HYPHEN_CLIENT_RELEASE_VERSION = RPC_HYPHEN_PREFIX + "client-release-version";

    String HYPHEN_CLIENT_ZONE = RPC_HYPHEN_PREFIX + "client-zone";

    String HYPHEN_CLIENT_GROUP = RPC_HYPHEN_PREFIX + "client-group";
    String HYPHEN_REQUEST_GROUP = RPC_HYPHEN_PREFIX + "request-group";

    String GRAY_VERSION_KEY = RPC_CONTEXT_PREFIX + "gray-version";

    //inner attachment
    String AUTH_TOKEN_ATTACHMENT = "auth-token";

    String ZONE_TAG = "hll.zone";

    String RELEASE_VERSION = "release.version";

    String GROUP_TAG = "hll.group";

    // proxy
    int MAX_PROXY_COUNT = 65535;

    // Patterns
    Pattern COMMA_SPLIT_PATTERN = Pattern.compile("\\s*[,]+\\s*");
    Pattern SEMICOLON_SPLIT_PATTERN = Pattern.compile("\\s*[;]+\\s*");

    char COMMA_SEPARATOR_CHAR = ',';

    String COMMA_SEPARATOR = ",";

    String UNKNOWN_APPLICATION_NAME = "unknown";

    //service filtrate tag
    String SERVICE_KEY = "service";
    String GRPC_SERVICE = GRPC_PROTOCOL;
    String JSON_RPC_SERVICE = "";

    //generic invoker cache
    String GENERIC_INVOKER_CACHE_SIZE_NAME = "generic.invoker.cache.size";
    int GENERIC_INVOKER_CACHE_SIZE = 1024;

    //contextPath tag
    String CONTEXT_PATH = "contextPath";
    String CONNECTION_TIMEOUT = "connectionTimeout";

    String NETWORK_IGNORED_INTERFACE = NAME + ".network.interface.ignored";
    String DEFAULT_NETWORK_IGNORED_INTERFACE = "docker0";

    String DYNAMIC_LOG_BASE_KEY = "dynamic.base.log.key";

    String DYNAMIC_LOG_LEVEL_KEY = "dynamic.level.log.key";

    String CONSUMER_APP_SERIALIZATION_KEY = "consumer.serialization.%s";
}
