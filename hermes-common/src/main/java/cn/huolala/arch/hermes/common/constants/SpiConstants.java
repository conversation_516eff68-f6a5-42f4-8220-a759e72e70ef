package cn.huolala.arch.hermes.common.constants;

/**
 * SPI Constants
 * @see cn.huolala.arch.hermes.common.extension.SPI
 */
public interface SpiConstants extends ConstantsMarker {
    int ORDER_STEP = 1000;

    String CLUSTER_DEFAULT = "failfast";

    String LOADBALANCE_DEFAULT = "roundrobin";

    String CONFIG_CENTER_DEFAULT = "apollo";

    String DISCOVERY_DEFAULT = "consul";

    String METADATA_DEFAULT = "admin";

    String PROTOCOL_DEFAULT = "grpc";

    String VALIDATION_DEFAULT = "validation";

    String DEFINITION_DEFAULT = "default";

    String DEFINITION_JSON_RPC = "jsonrpc";

    String REFERENCE_CONFIG_PROVIDER_DEFAULT = "spring";


    String FILTER_AUTH_CONSUMER = "authconsumer";

    String FILTER_AUTH_PROVIDER = "authprovider";

    String FILTER_CIRCUIT_BREAKER = "circuitbreaker";
    String FILTER_DEGRADE = "degrade";
    String FILTER_INVOKER_LOG = "invokerlog";
    String FILTER_ACCESS_LOG = "accesslog";

    String FILTER_RATELIMIT_CONSUMER = "consumer";
    String FILTER_RATELIMIT_PROVIDER = "provider";
    String FILTER_TIMEOUT = "timeout";

}
