package cn.huolala.arch.hermes.common.constants;

import static cn.huolala.arch.hermes.common.constants.Constants.NAME;

/**
 * Thread Constants
 */
public interface ThreadConstants extends ConstantsMarker {
    String THREADPOOL_KEY = "threadpool";

    String THREAD_NAME_KEY = "threadname";

    String CORE_THREADS_KEY = "corethreads";

    String THREADS_KEY = "threads";

    String QUEUES_KEY = "queues";

    String ALIVE_KEY = "alive";

    String DEFAULT_THREAD_NAME = NAME;

    int DEFAULT_CORE_THREADS = 0;

    int DEFAULT_THREADS = 200;

    int DEFAULT_QUEUES = 0;

    int DEFAULT_QUEUES_FIXED = -1;

    int DEFAULT_QUEUES_EAGER = -1;

    int DEFAULT_ALIVE = 60 * 1000;
}
