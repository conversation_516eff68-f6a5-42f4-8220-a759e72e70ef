package cn.huolala.arch.hermes.common.context;

import cn.huolala.arch.hermes.api.config.ApplicationConfig;
import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.context.lifecycle.Lifecycle;
import cn.huolala.arch.hermes.common.context.lifecycle.LifecycleExt;
import cn.huolala.arch.hermes.common.context.model.ConsumerModel;
import cn.huolala.arch.hermes.common.context.model.ProviderModel;
import cn.huolala.arch.hermes.common.context.shutdown.ShutdownHook;
import cn.huolala.arch.hermes.common.context.support.ContextAttributeProvider;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import static cn.huolala.arch.hermes.common.constants.Constants.ADMIN_HOST_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.CONTEXT_PATH;

/**
 * Application Context
 * <p>
 * Bootstrap and this class are at present designed to be singleton
 *
 * @see LifecycleExt
 * @see ConfigManager
 * @see Environment
 * @see ServiceRepository
 */
public final class ApplicationContext {
    private static final Logger logger = LoggerFactory.getLogger(ApplicationContext.class);

    private static final ExtensionLoader<LifecycleExt> EXT_LOADER = ExtensionLoader
            .getExtensionLoader(LifecycleExt.class);

    private static final ExtensionLoader<ContextAttributeProvider> ATTRIBUTE_PROVIDER = ExtensionLoader
            .getExtensionLoader(ContextAttributeProvider.class);

    private static final ShutdownHook SHUTDOWN_HOOK = new ShutdownHook("ShutdownHook");

    private static final Map<String, Object> ATTRIBUTES = new ConcurrentHashMap<>();

    private ApplicationContext() {
    }

    public static void start() {
        // start all ext
        EXT_LOADER.getSupportedExtensionInstances().forEach(Lifecycle::start);

        // load all AttributeProvider
        ATTRIBUTE_PROVIDER.getSupportedExtensionInstances()
                .forEach(provider -> Optional.ofNullable(provider.getAttributes())
                        .ifPresent(attrs -> attrs.forEach(ApplicationContext::addAttributeIfAbsent)));
    }

    public static void destroy() {
        // destroy all ext
        EXT_LOADER.getSupportedExtensionInstances().forEach(Lifecycle::destroy);
    }

    /**
     * get application name (appid)
     *
     * @see ApplicationConfig#getName()
     */
    public static String getName() {
        return getApplicationConfig().getName();
    }

    /**
     * LaLa admin server url
     */
    public static String getAdminUrl() {
        return ATTRIBUTES.getOrDefault(ADMIN_HOST_KEY, "").toString();
    }

    /**
     * server.servlet.context-path
     */
    public static String getContextPath() {
        return ATTRIBUTES.getOrDefault(CONTEXT_PATH, "").toString();
    }

    /**
     * getCustomizeObjectMapper
     *
     * @param appId
     * @return
     */
    public static Object getCustomizeObjectMapper(String appId) {
        if (StringUtils.isEmpty(appId)) {
            return null;
        }

        return ATTRIBUTES.get(String.format(Constants.CONSUMER_APP_SERIALIZATION_KEY, appId));
    }

    /**
     * getApplicationConfig
     *
     * @see ConfigManager#getApplication()
     */
    public static ApplicationConfig getApplicationConfig() {
        return getConfigManager().getApplication()
                .orElseThrow(() -> new IllegalStateException("No ApplicationConfig specified"));
    }

    public static ConfigManager getConfigManager() {
        return (ConfigManager) EXT_LOADER.getExtension(ConfigManager.NAME);
    }

    /**
     * getProviderModel
     *
     * @see ServiceRepository#lookupExportedService(String)
     */
    public static ProviderModel getProviderModel(String serviceKey) {
        return getServiceRepository().lookupExportedService(serviceKey);
    }

    /**
     * getConsumerModel
     *
     * @see ServiceRepository#lookupReferredService(String)
     */
    public static ConsumerModel getConsumerModel(String serviceKey) {
        return getServiceRepository().lookupReferredService(serviceKey);
    }

    public static ServiceRepository getServiceRepository() {
        return (ServiceRepository) EXT_LOADER.getExtension(ServiceRepository.NAME);
    }

    public static Environment getEnvironment() {
        return (Environment) EXT_LOADER.getExtension(Environment.NAME);
    }

    /**
     * get context Attributes
     *
     * @return notNull attributes map
     */
    public static Map<String, Object> getAttributes() {
        return ATTRIBUTES;
    }

    public static void addAttribute(String name, Object attr) {
        if (!Objects.isNull(attr)) {
            ATTRIBUTES.put(name, attr);
        }
    }

    public static void addAttributeIfAbsent(String name, Object attr) {
        if (!Objects.isNull(attr)) {
            ATTRIBUTES.putIfAbsent(name, attr);
        }
    }

}
