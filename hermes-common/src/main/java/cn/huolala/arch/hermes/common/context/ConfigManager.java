package cn.huolala.arch.hermes.common.context;

import java.util.Arrays;
import java.util.Collection;
import java.util.Optional;

import cn.huolala.arch.hermes.api.config.ApplicationConfig;
import cn.huolala.arch.hermes.api.config.ConfigCenterConfig;
import cn.huolala.arch.hermes.api.config.ConsumerConfig;
import cn.huolala.arch.hermes.api.config.ConsumerProtocolConfig;
import cn.huolala.arch.hermes.api.config.DiscoveryConfig;
import cn.huolala.arch.hermes.api.config.HostPortsConfig;
import cn.huolala.arch.hermes.api.config.ProtocolConfig;
import cn.huolala.arch.hermes.api.config.ProviderConfig;
import cn.huolala.arch.hermes.api.config.ReferenceConfigBase;
import cn.huolala.arch.hermes.api.config.ServiceConfigBase;
import cn.huolala.arch.hermes.api.config.support.AbstractConfig;
import cn.huolala.arch.hermes.common.context.support.CachedConfigManager;
import cn.huolala.arch.hermes.common.util.CollectionUtils;

/**
 * ConfigManager
 */
public class ConfigManager extends CachedConfigManager {
    public static final String NAME = "config";

    public static final Collection<Class<? extends AbstractConfig>> SUPPORTED_CONFIG_TYPES =
            Arrays.asList(
                    ApplicationConfig.class,
                    ConfigCenterConfig.class,
                    DiscoveryConfig.class,
                    ProtocolConfig.class,
                    ConsumerProtocolConfig.class,
                    ConsumerConfig.class,
                    ReferenceConfigBase.class,
                    ProviderConfig.class,
                    ServiceConfigBase.class,
                    HostPortsConfig.class
            );

    public ConfigManager() {
        super(SUPPORTED_CONFIG_TYPES);
    }

    public void setApplication(ApplicationConfig config) {
        addConfig(config);
    }

    public void setConfigCenter(ConfigCenterConfig config) {
        addConfig(config);
    }

    public void addDiscovery(DiscoveryConfig config) {
        addConfig(config);
    }

    public void addProtocol(ProtocolConfig config) {
        addConfig(config);
    }

    public void addConsumerProtocol(ConsumerProtocolConfig config) {
        addConfig(config);
    }

    public void addConsumer(ConsumerConfig config) {
        addConfig(config);
    }

    public void addReference(ReferenceConfigBase<?> config) {
        addConfig(config);
    }

    public void addProvider(ProviderConfig config) {
        addConfig(config);
    }

    public void addService(ServiceConfigBase<?> config) {
        addConfig(config);
    }

    public void addHostPorts(HostPortsConfig config) {
        addConfig(config);
    }

    public Optional<ApplicationConfig> getApplication() {
        return getConfig(ApplicationConfig.class);
    }

    public Optional<ConfigCenterConfig> getConfigCenter() {
        return getConfig(ConfigCenterConfig.class);
    }

    public Optional<DiscoveryConfig> getDiscovery() {
        return getConfig(DiscoveryConfig.class);
    }

    public Optional<ProtocolConfig> getProtocol(String protocolName) {
        Collection<ProtocolConfig> configs = getConfigs(ProtocolConfig.class);
        if (CollectionUtils.isEmpty(configs)) {
            return Optional.empty();
        }

        return configs.stream()
                .filter(config -> protocolName.equalsIgnoreCase(config.getName()))
                .findFirst();
    }

    public Collection<ProtocolConfig> getProtocols() {
        return getConfigs(ProtocolConfig.class);
    }

    public Optional<ConsumerProtocolConfig> getConsumerProtocol(String protocolName) {
        Collection<ConsumerProtocolConfig> configs = getConfigs(ConsumerProtocolConfig.class);
        if (CollectionUtils.isEmpty(configs)) {
            return Optional.empty();
        }

        return configs.stream()
                .filter(config -> protocolName.equalsIgnoreCase(config.getName()))
                .findFirst();
    }

    public Collection<ConsumerProtocolConfig> getConsumerProtocols() {
        return getConfigs(ConsumerProtocolConfig.class);
    }

    public Optional<ConsumerConfig> getConsumer() {
        return getConfig(ConsumerConfig.class);
    }

    public Collection<ReferenceConfigBase> getReferences() {
        return getConfigs(ReferenceConfigBase.class);
    }

    public Optional<ProviderConfig> getProvider() {
        return getConfig(ProviderConfig.class);
    }

    public Collection<ServiceConfigBase> getServices() {
        return getConfigs(ServiceConfigBase.class);
    }

    public Optional<HostPortsConfig> getHostPortConfig() {
        return getConfig(HostPortsConfig.class);
    }

}
