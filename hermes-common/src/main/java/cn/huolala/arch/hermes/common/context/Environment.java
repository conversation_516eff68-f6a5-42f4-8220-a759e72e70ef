package cn.huolala.arch.hermes.common.context;

import cn.huolala.arch.hermes.api.config.ConfigCenterConfig;
import cn.huolala.arch.hermes.common.config.GlobalConfiguration;
import cn.huolala.arch.hermes.common.config.Configuration;
import cn.huolala.arch.hermes.common.config.EnvironmentConfiguration;
import cn.huolala.arch.hermes.common.config.InmemoryConfiguration;
import cn.huolala.arch.hermes.common.config.PropertiesConfiguration;
import cn.huolala.arch.hermes.common.config.SystemConfiguration;
import cn.huolala.arch.hermes.common.config.dynamic.DynamicConfiguration;
import cn.huolala.arch.hermes.common.context.lifecycle.LifecycleAdapter;
import cn.huolala.arch.hermes.common.context.lifecycle.LifecycleExt;

/**
 * Environment(LifecycleExt)
 *
 * @see Configuration
 * @see DynamicConfiguration
 * @see ConfigCenterConfig
 */
public class Environment extends LifecycleAdapter implements LifecycleExt {
    public static final String NAME = "environment";

    private final PropertiesConfiguration propertiesConfiguration;
    private final SystemConfiguration systemConfiguration;
    private final EnvironmentConfiguration environmentConfiguration;
    private final InmemoryConfiguration externalConfiguration;

    private volatile GlobalConfiguration globalConfiguration;
    private volatile DynamicConfiguration dynamicConfiguration;

    public Environment() {
        this.propertiesConfiguration = new PropertiesConfiguration();
        this.systemConfiguration = new SystemConfiguration();
        this.environmentConfiguration = new EnvironmentConfiguration();
        this.externalConfiguration = new InmemoryConfiguration();
    }

    @Override
    public void destroy() throws IllegalStateException {
        this.externalConfiguration.clear();
        this.dynamicConfiguration = null;
    }

    /**
     * SystemConfiguration -> EnvironmentConfiguration -> ExternalConfiguration -> PropertiesConfiguration
     */
    public Configuration getConfiguration() {
        if (globalConfiguration == null) {
            synchronized (this) {
                if (globalConfiguration == null) {
                    globalConfiguration = new GlobalConfiguration();
                    globalConfiguration.addConfiguration(systemConfiguration);
                    globalConfiguration.addConfiguration(environmentConfiguration);
                    globalConfiguration.addConfiguration(externalConfiguration);
                    globalConfiguration.addConfiguration(propertiesConfiguration);
                }
            }
        }
        return globalConfiguration;
    }

    public DynamicConfiguration getDynamicConfiguration() {
        return this.dynamicConfiguration;
    }

    public void setDynamicConfiguration(DynamicConfiguration dynamicConfiguration) {
        this.dynamicConfiguration = dynamicConfiguration;
    }
}
