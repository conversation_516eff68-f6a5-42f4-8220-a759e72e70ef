package cn.huolala.arch.hermes.common.context;

import cn.huolala.arch.hermes.api.config.ReferenceConfigBase;
import cn.huolala.arch.hermes.api.config.ServiceConfigBase;
import cn.huolala.arch.hermes.common.context.lifecycle.LifecycleAdapter;
import cn.huolala.arch.hermes.common.context.lifecycle.LifecycleExt;
import cn.huolala.arch.hermes.common.context.model.ConsumerModel;
import cn.huolala.arch.hermes.common.context.model.ProviderModel;
import cn.huolala.arch.hermes.common.context.model.ServiceModel;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.service.BuiltinServiceDetector;
import cn.huolala.arch.hermes.common.util.CollectionUtils;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * ServiceRepository
 *
 * @see ServiceModel
 * @see ConsumerModel
 * @see ProviderModel
 */
public class ServiceRepository extends LifecycleAdapter implements LifecycleExt {
    public static final String NAME = "repository";

    // services
    private final ConcurrentMap<String, ServiceModel> services = new ConcurrentHashMap<>();

    // consumers
    private final ConcurrentMap<String, ConsumerModel> consumers = new ConcurrentHashMap<>();

    // providers
    private final ConcurrentMap<String, ProviderModel> providers = new ConcurrentHashMap<>();

    @Override
    public void start() throws IllegalStateException {
        super.start();

        Set<BuiltinServiceDetector> builtinServices
                = ExtensionLoader.getExtensionLoader(BuiltinServiceDetector.class).getSupportedExtensionInstances();
        if (CollectionUtils.isNotEmpty(builtinServices)) {
            for (BuiltinServiceDetector service : builtinServices) {
                registerService(service.getService());
            }
        }
    }

    @Override
    public void destroy() throws IllegalStateException {
        super.destroy();
        services.clear();
        consumers.clear();
        providers.clear();
    }

    public ServiceModel registerService(Class<?> interfaceClazz) {
        return services.computeIfAbsent(interfaceClazz.getName(), k -> new ServiceModel(interfaceClazz));
    }

    /**
     * <ul>
     *     <li>path's default value is the name of the interface</li>
     *     <li>services share the same interface but has different group/version can share the same path</li>
     * </ul>
     */
    public ServiceModel registerService(String path, Class<?> interfaceClass) {
        ServiceModel service = registerService(interfaceClass);
        if (!interfaceClass.getName().equals(path)) {
            services.putIfAbsent(path, service);
        }
        return service;
    }

    public void unregisterService(Class<?> interfaceClazz) {
        unregisterService(interfaceClazz.getName());
    }

    public void unregisterService(String path) {
        services.remove(path);
    }

    public void registerConsumer(String serviceKey,
                                 ServiceModel serviceModel,
                                 ReferenceConfigBase<?> referenceConfig,
                                 Object proxyObject) {

        consumers.computeIfAbsent(serviceKey, key ->
                new ConsumerModel(key, proxyObject, serviceModel, referenceConfig));
    }

    public void registerProvider(String serviceKey,
                                 Object serviceInstance,
                                 ServiceModel serviceModel,
                                 ServiceConfigBase<?> serviceConfig) {

        providers.computeIfAbsent(serviceKey, key ->
                new ProviderModel(key, serviceInstance, serviceModel, serviceConfig));
    }

    public ServiceModel lookupService(String interfaceName) {
        return services.get(interfaceName);
    }

    public ProviderModel lookupExportedService(String serviceKey) {
        return providers.get(serviceKey);
    }

    public ConsumerModel lookupReferredService(String serviceKey) {
        return consumers.get(serviceKey);
    }

}
