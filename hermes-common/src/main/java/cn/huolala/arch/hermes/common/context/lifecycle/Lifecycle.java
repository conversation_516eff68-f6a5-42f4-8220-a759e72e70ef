package cn.huolala.arch.hermes.common.context.lifecycle;

/**
 * The Lifecycle of component
 */
public interface Lifecycle {

    /**
     * Initialize the component before {@link #start() start}
     */
    void initialize() throws IllegalStateException;

    /**
     * Start the component
     */
    void start() throws IllegalStateException;

    /**
     * Destroy the component
     */
    void destroy() throws IllegalStateException;
}
