package cn.huolala.arch.hermes.common.context.model;

import cn.huolala.arch.hermes.api.config.ReferenceConfigBase;
import cn.huolala.arch.hermes.common.tool.Assert;
import cn.huolala.arch.hermes.common.util.CommandKeyUtils;

import java.lang.reflect.Method;
import java.util.Optional;

import static cn.huolala.arch.hermes.common.constants.Constants.ALL;

/**
 * ConsumerModel
 */
public class ConsumerModel {
    private final String serviceKey;
    private final ServiceModel serviceModel;
    private final ReferenceConfigBase<?> referenceConfig;

    private Object proxyObject;

    public ConsumerModel(String serviceKey,
                         Object proxyObject,
                         ServiceModel serviceModel,
                         ReferenceConfigBase<?> referenceConfig) {
        Assert.notEmpty(serviceKey, "Service key can't be null or blank");

        this.serviceKey = serviceKey;
        this.proxyObject = proxyObject;
        this.serviceModel = serviceModel;
        this.referenceConfig = referenceConfig;
    }

    /**
     * Get remote application name
     */
    public String getRemoteApplication() {
        return this.referenceConfig.getApplication();
    }


    public String getCommandKey(Method method) {
        return buildCommandKey(getRemoteApplication(), serviceModel.getCommandKeyServiceName(),
                getMethodModel(method).getMethodName());
    }

    public String getServiceCommandKey() {
        return buildCommandKey(getRemoteApplication(), serviceModel.getCommandKeyServiceName(), ALL);
    }

    public String getAppCommandKey() {
        return buildCommandKey(getRemoteApplication(), ALL, ALL);
    }

    protected String buildCommandKey(String remoteApplication, String serviceName, String methodName) {
        return CommandKeyUtils.buildInConsumerSide(remoteApplication, serviceName, methodName);
    }

    /**
     * @see ServiceModel#getMethod(Method)
     */
    public MethodModel getMethodModel(Method method) {
        return Optional.ofNullable(serviceModel)
                .map(s -> s.getMethod(method))
                .orElseThrow(() -> new IllegalStateException(method.getName() + "'s MethodModel can't be null"));
    }

    public void setProxyObject(Object proxyObject) {
        this.proxyObject = proxyObject;
    }

    public String getServiceKey() {
        return serviceKey;
    }

    public ServiceModel getServiceModel() {
        return serviceModel;
    }

    public ReferenceConfigBase<?> getReferenceConfig() {
        return referenceConfig;
    }

    public Object getProxyObject() {
        return proxyObject;
    }

}
