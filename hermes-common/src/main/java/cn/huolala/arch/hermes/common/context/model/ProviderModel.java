package cn.huolala.arch.hermes.common.context.model;

import cn.huolala.arch.hermes.api.config.ServiceConfigBase;
import cn.huolala.arch.hermes.common.tool.Assert;
import cn.huolala.arch.hermes.common.util.CommandKeyUtils;

import java.lang.reflect.Method;
import java.util.Optional;

import static cn.huolala.arch.hermes.common.constants.Constants.ALL;


/**
 * ProviderModel
 */
public class ProviderModel {
    private final String serviceKey;
    private final Object serviceInstance;
    private final ServiceModel serviceModel;
    private final ServiceConfigBase<?> serviceConfig;

    public ProviderModel(String serviceKey,
                         Object serviceInstance,
                         ServiceModel serviceModel,
                         ServiceConfigBase<?> serviceConfig) {
        Assert.notEmpty(serviceKey, "Service key can't be null or blank");

        this.serviceKey = serviceKey;
        this.serviceInstance = serviceInstance;
        this.serviceModel = serviceModel;
        this.serviceConfig = serviceConfig;
    }

    public String getServiceKey() {
        return serviceKey;
    }

    public Object getServiceInstance() {
        return serviceInstance;
    }

    public ServiceModel getServiceModel() {
        return serviceModel;
    }

    public ServiceConfigBase<?> getServiceConfig() {
        return serviceConfig;
    }

    public String getCommandKey(Method method) {
        return buildCommandKey(serviceModel.getCommandKeyServiceName(), getMethodModel(method).getMethodName());
    }

    public String getServiceCommandKey() {
        return buildCommandKey(serviceModel.getCommandKeyServiceName(), ALL);
    }

    public String getAppCommandKey() {
        return buildCommandKey(ALL, ALL);
    }

    protected String buildCommandKey(String serviceName, String methodName) {
        return CommandKeyUtils.buildInProviderSide(serviceName, methodName);
    }

    /**
     * @see ServiceModel#getMethod(Method)
     */
    public MethodModel getMethodModel(Method method) {
        return Optional.ofNullable(serviceModel)
                .map(s -> s.getMethod(method))
                .orElseThrow(() -> new IllegalStateException(method.getName() + "'s MethodModel can't be null"));
    }
}
