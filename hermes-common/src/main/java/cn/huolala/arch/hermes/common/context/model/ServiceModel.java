package cn.huolala.arch.hermes.common.context.model;

import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.huolala.arch.hermes.common.util.StringUtils.removeEnd;

/**
 * ServiceModel
 */
public class ServiceModel {
    private static final String REDUNDANT_SERVICE_SUFFIX = "Service$Interface";

    private final String serviceName;
    private final Class<?> serviceInterfaceClass;

    private final Map<String, List<MethodModel>> methods = new HashMap<>();
    // desc: methodName
    private final Map<String, Map<String, MethodModel>> descToMethods = new HashMap<>();

    public ServiceModel(Class<?> interfaceClass) {
        this.serviceInterfaceClass = interfaceClass;
        this.serviceName = interfaceClass.getName();
        initMethods();
    }

    public static String buildServiceKey(String path, String group, String version) {
        StringBuilder buf = new StringBuilder();
        if (StringUtils.isNotEmpty(group)) {
            buf.append(group).append("/");
        }
        buf.append(path);
        if (StringUtils.isNotEmpty(version)) {
            buf.append(":").append(version);
        }
        return buf.toString();
    }

    private void initMethods() {
        Method[] methodsToExport = this.serviceInterfaceClass.getMethods();
        for (Method method : methodsToExport) {
            ReflectUtils.makeAccessible(method);

            methods.computeIfAbsent(method.getName(), k -> new ArrayList<>(1))
                    .add(new MethodModel(method));
        }

        methods.forEach((methodName, methodList) -> {
            Map<String, MethodModel> descMap = descToMethods.computeIfAbsent(methodName, k -> new HashMap<>());
            methodList.forEach(methodModel -> descMap.put(methodModel.getParamDesc(), methodModel));
        });
    }

    public List<MethodModel> getMethods(String methodName) {
        return methods.get(methodName);
    }

    public MethodModel getMethod(String methodName, String params) {
        Map<String, MethodModel> methods = descToMethods.get(methodName);
        return CollectionUtils.isNotEmptyMap(methods) ? methods.get(params) : null;
    }

    public MethodModel getMethod(String methodName, Class<?>[] paramTypes) {
        List<MethodModel> methodModels = methods.get(methodName);
        if (CollectionUtils.isNotEmpty(methodModels)) {
            return methodModels.stream()
                    .filter(descriptor -> Arrays.equals(paramTypes, descriptor.getParameterClasses()))
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }

    public MethodModel getMethod(Method method) {
        return getMethod(method.getName(), method.getParameterTypes());
    }

    public String getServiceName() {
        return serviceName;
    }

    public String getCommandKeyServiceName() {
        return removeEnd(serviceName, REDUNDANT_SERVICE_SUFFIX);
    }

    public Class<?> getServiceInterfaceClass() {
        return serviceInterfaceClass;
    }
}
