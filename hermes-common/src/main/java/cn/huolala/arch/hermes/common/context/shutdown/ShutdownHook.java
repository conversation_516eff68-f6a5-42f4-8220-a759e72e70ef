package cn.huolala.arch.hermes.common.context.shutdown;

import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * The shutdown hook thread to do the cleanup stuff.
 * This is a singleton in order to ensure there is only one shutdown hook registered.
 */
public class ShutdownHook extends Thread {
    private static final Logger logger = LoggerFactory.getLogger(ShutdownHook.class);

    private final AtomicBoolean registered = new AtomicBoolean(false);

    private final AtomicBoolean destroyed = new AtomicBoolean(false);

    public ShutdownHook(String name) {
        super(name);
    }

    @Override
    public void run() {
        if (destroyed.compareAndSet(false, true)) {
            if (logger.isInfoEnabled()) {
                logger.info("Run shutdown hook now.");
            }

            doDestroy();
        }
    }

    private void doDestroy() {
        ShutdownHookCallbacks.getInstance().callback();
    }

    /**
     * Register the ShutdownHook
     */
    public void register() {
        if (registered.compareAndSet(false, true)) {
            try {
                Runtime.getRuntime().addShutdownHook(this);
            } catch (Exception e) {
                logger.warn("register shutdown hook failed: " + e.getMessage(), e);
            }
        }
    }

    /**
     * Unregister the ShutdownHook
     */
    public void unregister() {
        if (registered.compareAndSet(true, false)) {
            try {
                Runtime.getRuntime().removeShutdownHook(this);
            } catch (Exception e) {
                logger.warn("unregister shutdown hook failed: " + e.getMessage(), e);
            }
        }
    }

    public boolean isRegistered() {
        return registered.get();
    }

    public boolean isDestroyed() {
        return destroyed.get();
    }

}
