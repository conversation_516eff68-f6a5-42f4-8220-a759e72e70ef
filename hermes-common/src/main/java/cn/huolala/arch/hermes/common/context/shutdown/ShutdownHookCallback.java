package cn.huolala.arch.hermes.common.context.shutdown;

import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.common.support.Prioritized;

/**
 * A callback interface invoked when application is stopped.
 * <br/>
 * Note: This class is not directly related to Java ShutdownHook.
 */
@SPI
public interface ShutdownHookCallback extends Prioritized {
    /**
     * Callback execution
     *
     * @throws Throwable if met with some errors
     */
    void callback() throws Throwable;
}
