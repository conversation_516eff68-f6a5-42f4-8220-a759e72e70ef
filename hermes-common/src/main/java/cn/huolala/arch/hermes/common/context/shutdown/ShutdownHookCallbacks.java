package cn.huolala.arch.hermes.common.context.shutdown;

import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.support.function.ThrowableAction;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

import static java.util.Collections.sort;

/**
 * To compose {@link ShutdownHookCallback} class to manipulate one and more {@link ShutdownHookCallback} instances
 */
public final class ShutdownHookCallbacks {
    private static final ShutdownHookCallbacks INSTANCE = new ShutdownHookCallbacks();

    private final List<ShutdownHookCallback> callbacks = new LinkedList<>();

    public static ShutdownHookCallbacks getInstance() {
        return INSTANCE;
    }

    private ShutdownHookCallbacks() {
        loadCallbacks();
    }

    private void loadCallbacks() {
        ExtensionLoader<ShutdownHookCallback> loader = ExtensionLoader.getExtensionLoader(ShutdownHookCallback.class);
        loader.getSupportedExtensionInstances().forEach(this::addCallback);
    }

    public ShutdownHookCallbacks addCallback(ShutdownHookCallback callback) {
        synchronized (this) {
            this.callbacks.add(callback);
            sort(this.callbacks);
        }
        return this;
    }

    public Collection<ShutdownHookCallback> getCallbacks() {
        synchronized (this) {
            return this.callbacks;
        }
    }

    public void clear() {
        synchronized (this) {
            callbacks.clear();
        }
    }

    public void callback() {
        getCallbacks().forEach(callback -> ThrowableAction.execute(callback::callback));
    }

}
