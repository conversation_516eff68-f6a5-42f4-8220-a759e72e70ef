package cn.huolala.arch.hermes.common.context.support;

import cn.huolala.arch.hermes.api.config.ApplicationConfig;
import cn.huolala.arch.hermes.api.config.ConfigCenterConfig;
import cn.huolala.arch.hermes.api.config.ConsumerConfig;
import cn.huolala.arch.hermes.api.config.DiscoveryConfig;
import cn.huolala.arch.hermes.api.config.HostPortsConfig;
import cn.huolala.arch.hermes.api.config.ProviderConfig;
import cn.huolala.arch.hermes.api.config.ReferenceConfigBase;
import cn.huolala.arch.hermes.api.config.ServiceConfigBase;
import cn.huolala.arch.hermes.api.config.support.AbstractConfig;
import cn.huolala.arch.hermes.common.context.lifecycle.LifecycleAdapter;
import cn.huolala.arch.hermes.common.context.lifecycle.LifecycleExt;
import cn.huolala.arch.hermes.common.tool.ConcurrentHashSet;
import cn.huolala.arch.hermes.common.util.CollectionUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.huolala.arch.hermes.api.config.support.AbstractConfig.getTagName;

/**
 * Abstract Cached ConfigManager
 */
public abstract class CachedConfigManager extends LifecycleAdapter implements LifecycleExt {
    private static final Set<Class<? extends AbstractConfig>> UNIQUE_CONFIG_TYPES;

    private final Map<String, Map<String, AbstractConfig>> configsCache;
    private final Map<String, AtomicInteger> configIdIndexes;
    private final Collection<Class<? extends AbstractConfig>> supportedConfigTypes;

    static {
        UNIQUE_CONFIG_TYPES = new ConcurrentHashSet<>();
        UNIQUE_CONFIG_TYPES.add(ApplicationConfig.class);
        UNIQUE_CONFIG_TYPES.add(ConfigCenterConfig.class);
        UNIQUE_CONFIG_TYPES.add(DiscoveryConfig.class);
        UNIQUE_CONFIG_TYPES.add(ConsumerConfig.class);
        UNIQUE_CONFIG_TYPES.add(ProviderConfig.class);
        UNIQUE_CONFIG_TYPES.add(HostPortsConfig.class);
    }

    public CachedConfigManager(Collection<Class<? extends AbstractConfig>> supportedConfigTypes) {
        this.configsCache = new ConcurrentHashMap<>();
        this.configIdIndexes = new ConcurrentHashMap<>();

        this.supportedConfigTypes = supportedConfigTypes;
    }

    /**
     * Get config instance by id
     */
    @SuppressWarnings("unchecked")
    public <T extends AbstractConfig> Optional<T> getConfig(Class<T> cls, String id) {
        T config = (T) configsCache.getOrDefault(getTagName(cls), CollectionUtils.emptyHashMap()).get(id);
        return Optional.ofNullable(config);
    }

    public <T extends AbstractConfig> Optional<T> getConfig(Class<T> cls) {
        Map<String, T> configsMap = getConfigsMap(cls);
        if (configsMap.isEmpty()) {
            return Optional.empty();
        } else {
            T config = configsMap.values().iterator().next();
            return Optional.ofNullable(config);
        }
    }

    @SuppressWarnings("unchecked")
    public <C extends AbstractConfig> Collection<C> getConfigs(Class<C> cls) {
        return (Collection<C>) configsCache.getOrDefault(getTagName(cls), CollectionUtils.emptyHashMap()).values();
    }

    @SuppressWarnings("unchecked")
    public <C extends AbstractConfig> Map<String, C> getConfigsMap(Class<C> cls) {
        return (Map<String, C>) configsCache.getOrDefault(getTagName(cls), CollectionUtils.emptyHashMap());
    }

    /**
     * Add the {@link AbstractConfig config}
     */
    public void addConfig(AbstractConfig config) throws IllegalArgumentException {
        if (config != null) {
            if (!isSupportConfigType(config.getClass())) {
                throw new IllegalArgumentException("Unsupported config type: " + config);
            }

            Map<String, AbstractConfig> configsMap = configsCache
                    .computeIfAbsent(getTagName(config.getClass()), type -> new ConcurrentHashMap<>());

            // fast check duplicated equivalent config before write lock
            if (!(config instanceof ReferenceConfigBase || config instanceof ServiceConfigBase)) {
                for (AbstractConfig value : configsMap.values()) {
                    if (value.equals(config)) {
                        return;
                    }
                }
            }

            //noinspection SynchronizationOnLocalVariableOrMethodParameter
            synchronized (configsMap) {
                addIfAbsent(config, configsMap);
            }
        }
    }

    /**
     * In some scenario, we may need to add and remove ServiceConfig or ReferenceConfig dynamically.
     */
    public boolean removeConfig(AbstractConfig config) {
        if (config == null) {
            return false;
        }

        Map<String, AbstractConfig> configs = configsCache.get(getTagName(config.getClass()));
        return CollectionUtils.isNotEmptyMap(configs)
                && configs.values().removeIf(c -> config == c);
    }

    protected <C extends AbstractConfig> C addIfAbsent(C config, Map<String, C> configsMap) {

        if (config == null || configsMap == null) {
            return config;
        }

        Optional<C> uniqueConfig = checkUniqueConfig(configsMap, config);
        if (uniqueConfig.isPresent()) {
            return uniqueConfig.get();
        }

        String key = config.getId();
        if (key == null) {
            do {
                // generate key if id is not set
                key = generateConfigId(config);
            } while (configsMap.containsKey(key));
        }

        // override existed config if any
        configsMap.put(key, config);
        return config;
    }

    protected <C extends AbstractConfig> String generateConfigId(C config) {
        String tagName = getTagName(config.getClass());
        int idx = configIdIndexes.computeIfAbsent(tagName, clazz -> new AtomicInteger(0)).incrementAndGet();
        return tagName + "#" + idx;
    }

    protected <C extends AbstractConfig> Optional<C> checkUniqueConfig(Map<String, C> configsMap, C config) {
        if (configsMap.size() > 0 && isUniqueConfig(config)) {
            C old = configsMap.values().iterator().next();
            return Optional.of(old);
        } else {
            return Optional.empty();
        }
    }

    protected boolean isSupportConfigType(Class<? extends AbstractConfig> type) {
        for (Class<? extends AbstractConfig> supportedConfigType : supportedConfigTypes) {
            if (supportedConfigType.isAssignableFrom(type)) {
                return true;
            }
        }
        return false;
    }

    protected boolean isUniqueConfig(AbstractConfig config) {
        if (UNIQUE_CONFIG_TYPES.contains(config.getClass())) {
            return true;
        }
        return UNIQUE_CONFIG_TYPES.stream()
                .anyMatch(type -> type.isAssignableFrom(config.getClass()));
    }
}
