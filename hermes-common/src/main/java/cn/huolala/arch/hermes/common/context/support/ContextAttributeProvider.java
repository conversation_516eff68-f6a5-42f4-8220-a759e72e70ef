package cn.huolala.arch.hermes.common.context.support;

import cn.huolala.arch.hermes.common.extension.SPI;

import java.util.Map;

/**
 * Context Attribute Provider
 *
 * @see cn.huolala.arch.hermes.common.context.ApplicationContext#addAttribute(String, Object)
 */
@SPI
public interface ContextAttributeProvider {
    /**
     * get attributes for ApplicationContext(IfAbsent)
     *
     * @return null or empty will be ignored
     */
    Map<String, Object> getAttributes();
}
