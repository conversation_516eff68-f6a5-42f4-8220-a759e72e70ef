package cn.huolala.arch.hermes.common.event;

import cn.huolala.arch.hermes.common.extension.ExtensionLoader;

import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executor;
import java.util.function.Consumer;

import static cn.huolala.arch.hermes.common.event.EventListener.findEventType;

/**
 * The abstract {@link EventDispatcher} providers the common implementation.
 *
 * @see EventDispatcher
 * @see EventListener
 * @see Event
 */
@SuppressWarnings({"unchecked", "rawtypes"})
public abstract class AbstractEventDispatcher implements EventDispatcher {
    private final Object mutex = new Object();

    private final ConcurrentMap<Class<? extends Event>, List<EventListener>> listenersCache;

    private final Executor executor;

    /**
     * Constructor with an instance of {@link Executor}
     *
     * @param executor {@link Executor}
     */
    public AbstractEventDispatcher(Executor executor) {
        this.executor = executor;
        this.listenersCache = new ConcurrentHashMap<>();
        this.loadEventListenerInstances();
    }

    @Override
    public void dispatch(Event event, boolean async) {
        Executor executor = async ? this.executor : Runnable::run;

        executor.execute(() -> listenersCache.entrySet().stream()
                .filter(entry -> entry.getKey().isAssignableFrom(event.getClass()))
                .map(Map.Entry::getValue)
                .flatMap(Collection::stream)
                .sorted()
                .forEach(listener -> listener.onEvent(event)));
    }

    @Override
    public void addEventListener(EventListener<?> listener) {
        doInListener(listener, listeners -> {
            if (!listeners.contains(listener)) {
                listeners.add(listener);
            }
        });
    }

    @Override
    public void removeEventListener(EventListener<?> listener) {
        doInListener(listener, listeners -> listeners.remove(listener));
    }

    protected void doInListener(EventListener<?> listener, Consumer<Collection<EventListener>> consumer) {
        Class<? extends Event> eventType = findEventType(listener);
        if (eventType != null) {
            synchronized (mutex) {
                List<EventListener> listeners = listenersCache.computeIfAbsent(eventType, e -> new LinkedList<>());
                consumer.accept(listeners);
                Collections.sort(listeners);
            }
        }
    }

    /**
     * Load the instances of {@link EventListener event listeners}
     */
    protected void loadEventListenerInstances() {
        @SuppressWarnings("rawtypes")
        ExtensionLoader<EventListener> loader = ExtensionLoader.getExtensionLoader(EventListener.class);
        loader.getSupportedExtensionInstances().forEach(this::addEventListener);
    }
}
