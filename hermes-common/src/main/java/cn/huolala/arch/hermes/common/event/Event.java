package cn.huolala.arch.hermes.common.event;

import cn.huolala.arch.hermes.common.support.Marker;

import java.util.EventObject;

/**
 * Base EventObject
 */
public abstract class Event extends EventObject implements Marker {
    private static final long serialVersionUID = 8627843884729502357L;

    private final long timestamp;

    /**
     * Constructs a prototypical Event.
     *
     * @param source The object on which the Event initially occurred.
     * @throws IllegalArgumentException if source is null.
     */
    public Event(Object source) {
        super(source);
        this.timestamp = System.currentTimeMillis();
    }

    public long getTimestamp() {
        return timestamp;
    }
}
