package cn.huolala.arch.hermes.common.event;

import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.extension.SPI;

/**
 * {@link Event} Dispatcher
 */
@SPI(ParallelEventDispatcher.NAME)
public interface EventDispatcher {
    /**
     * Dispatch(default async) event to the registered {@link EventListener event listeners}
     */
    default void dispatch(Event event) {
        dispatch(event, true);
    }

    /**
     * Dispatch event to the registered {@link EventListener event listeners}
     */
    void dispatch(Event event, boolean async);

    /**
     * Add a {@link EventListener}
     */
    void addEventListener(EventListener<?> listener);

    /**
     * Remove a {@link EventListener}
     */
    void removeEventListener(EventListener<?> listener);

    /**
     * The default extension of {@link EventDispatcher}
     */
    static EventDispatcher getDefaultExtension() {
        return ExtensionLoader.getExtensionLoader(EventDispatcher.class).getDefaultExtension();
    }
}
