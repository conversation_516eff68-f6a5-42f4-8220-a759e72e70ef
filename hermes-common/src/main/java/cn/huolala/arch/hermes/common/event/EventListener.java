package cn.huolala.arch.hermes.common.event;

import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.common.support.Marker;
import cn.huolala.arch.hermes.common.support.Prioritized;
import cn.huolala.arch.hermes.common.util.ReflectUtils;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * Hermes base EventListener
 *
 * @param <E> event type
 */
@SPI
@FunctionalInterface
public interface EventListener<E extends Event> extends java.util.EventListener, Prioritized, Marker {
    /**
     * Handle event
     *
     * @param event a {@link Event Event}
     */
    void onEvent(E event);

    /**
     * Find the type {@link Event event} from the specified {@link EventListener}
     *
     * @return Nullable
     */
    static Class<? extends Event> findEventType(EventListener<?> listener) {
        return findEventType(listener.getClass());
    }

    /**
     * Find the {@link Class type} {@link Event event} from the specified {@link EventListener}
     *
     * @return Nullable
     */
    static Class<? extends Event> findEventType(Class<?> listenerClass) {
        Class<? extends Event> eventType = null;

        if (listenerClass != null && EventListener.class.isAssignableFrom(listenerClass)) {
            Optional<? extends Class<? extends Event>> eventTypeOpt = ReflectUtils.getParameterizedTypes(listenerClass)
                    .stream()
                    .map(EventListener::findEventType)
                    .filter(Objects::nonNull)
                    .findFirst();

            eventType = eventTypeOpt.isPresent() ? eventTypeOpt.get() : findEventType(listenerClass.getSuperclass());
        }

        return eventType;
    }

    /**
     * Find the type {@link Event event} from the specified {@link ParameterizedType} presents a class of {@link EventListener}
     *
     * @return Nullable
     */
    @SuppressWarnings("unchecked")
    static Class<? extends Event> findEventType(ParameterizedType parameterizedType) {
        Class<? extends Event> eventType = null;

        Type rawType = parameterizedType.getRawType();
        if (rawType instanceof Class && EventListener.class.isAssignableFrom((Class<?>) rawType)) {
            eventType = (Class<? extends Event>) Arrays.stream(parameterizedType.getActualTypeArguments())
                    .filter(typeArgument -> typeArgument instanceof Class)
                    .map(typeArgument -> (Class<?>) typeArgument)
                    .filter(Event.class::isAssignableFrom)
                    .findFirst()
                    .orElse(null);
        }

        return eventType;
    }
}
