package cn.huolala.arch.hermes.common.event;

import io.micrometer.core.instrument.MeterRegistry;

import java.util.function.Consumer;

/**
 * Metrics Event
 */
public class MetricsEvent extends Event implements Consumer<MeterRegistry> {
    private static final long serialVersionUID = -941322178909277490L;

    private final Consumer<MeterRegistry> metricsConsumer;

    public MetricsEvent(Consumer<MeterRegistry> metricsConsumer) {
        super(metricsConsumer);
        this.metricsConsumer = metricsConsumer;
    }

    public Consumer<MeterRegistry> getMetricsConsumer() {
        return metricsConsumer;
    }

    @Override
    public void accept(MeterRegistry meterRegistry) {
        this.metricsConsumer.accept(meterRegistry);
    }
}
