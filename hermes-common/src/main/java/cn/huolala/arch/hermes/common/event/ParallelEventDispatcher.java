package cn.huolala.arch.hermes.common.event;

import java.util.concurrent.ForkJoinPool;

/**
 * Parallel {@link EventDispatcher} implementation uses {@link ForkJoinPool#commonPool()}
 *
 * @see ForkJoinPool#commonPool()
 */
public class ParallelEventDispatcher extends AbstractEventDispatcher {
    public static final String NAME = "parallel";

    public ParallelEventDispatcher() {
        super(ForkJoinPool.commonPool());
    }
}
