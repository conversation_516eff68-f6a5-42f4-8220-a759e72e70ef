package cn.huolala.arch.hermes.common.extension;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Identifies injectable constructors, methods, and fields of your SPI implementation
 *
 * @see SPI
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Inject {
    /**
     * If true, and the appropriate binding is not found, the Injector will skip injection rather than produce an error
     */
    boolean optional() default false;
}
