package cn.huolala.arch.hermes.common.extension;

import cn.huolala.arch.hermes.common.support.Prioritized;

/**
 * SPI loading strategy
 *
 * @see SPI
 */
public interface LoadingStrategy extends Prioritized {
    String directory();

    default boolean preferExtensionClassLoader() {
        return false;
    }

    /**
     * Indicates current {@link LoadingStrategy} supports overriding other lower prioritized instances or not.
     *
     * @return if supports, return <code>true</code>, or <code>false</code>
     */
    default boolean overridden() {
        return false;
    }

    default String[] excludedPackages() {
        return null;
    }
}
