package cn.huolala.arch.hermes.common.extension.strategy;

import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.extension.LoadingStrategy;

/**
 * Default {@link LoadingStrategy}
 */
public class DefaultLoadingStrategy implements LoadingStrategy {

    @Override
    public String directory() {
        return "META-INF/" + Constants.NAME + "/";
    }

    @Override
    public boolean overridden() {
        return true;
    }

    @Override
    public int getPriority() {
        return NORMAL_PRIORITY;
    }

}