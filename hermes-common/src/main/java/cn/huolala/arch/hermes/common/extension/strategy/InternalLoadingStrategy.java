package cn.huolala.arch.hermes.common.extension.strategy;

import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.extension.LoadingStrategy;

/**
 * Internal {@link LoadingStrategy}
 */
public class InternalLoadingStrategy implements LoadingStrategy {

    @Override
    public String directory() {
        return "META-INF/" + Constants.NAME + "/internal/";
    }

    @Override
    public int getPriority() {
        return MAX_PRIORITY;
    }
}
