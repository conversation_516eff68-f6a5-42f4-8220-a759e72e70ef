package cn.huolala.arch.hermes.common.extension.strategy;

import cn.huolala.arch.hermes.common.extension.LoadingStrategy;
import cn.huolala.arch.hermes.common.support.Prioritized;

/**
 * Services {@link LoadingStrategy}
 */
public class ServicesLoadingStrategy implements LoadingStrategy {

    @Override
    public String directory() {
        return "META-INF/services/";
    }

    @Override
    public boolean overridden() {
        return true;
    }

    @Override
    public int getPriority() {
        return Prioritized.MIN_PRIORITY;
    }

}
