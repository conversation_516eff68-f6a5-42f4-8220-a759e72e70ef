package cn.huolala.arch.hermes.common.extension.support;

import cn.huolala.arch.hermes.common.extension.Activate;

import java.util.Comparator;

/**
 * Activate Comparator
 *
 * @see Activate
 */
public class ActivateComparator implements Comparator<Class<?>> {
    public static final Comparator<Class<?>> COMPARATOR = new ActivateComparator();

    @Override
    public int compare(Class<?> o1, Class<?> o2) {
        if (o1 == null && o2 == null) {
            return 0;
        } else if (o1 == null) {
            return -1;
        } else if (o2 == null) {
            return 1;
        } else if (o1.equals(o2)) {
            return 0;
        }

        int order1 = parseOrder(o1);
        int order2 = parseOrder(o2);

        // In order to avoid the problem of inconsistency between the loading order of two filters
        // in different loading scenarios without specifying the order attribute of the filter,
        // when the order is the same, compare its filterName
        if (order1 > order2) {
            return 1;
        } else if (order1 == order2) {
            return o1.getSimpleName().compareTo(o2.getSimpleName()) > 0 ? 1 : -1;
        } else {
            return -1;
        }
    }

    private int parseOrder(Class<?> clazz) {
        if (clazz.isAnnotationPresent(Activate.class)) {
            return clazz.getAnnotation(Activate.class).order();
        } else {
            return 0;
        }
    }
}
