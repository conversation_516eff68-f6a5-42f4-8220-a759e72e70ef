package cn.huolala.arch.hermes.common.extension.support;

import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.common.extension.Wrapper;

import java.util.Comparator;

/**
 * Wrapper Comparator
 *
 * @see Wrapper
 * @see Activate
 */
public class WrapperComparator implements Comparator<Class<?>> {

    public static final Comparator<Class<?>> COMPARATOR = new WrapperComparator();

    @Override
    public int compare(Class<?> o1, Class<?> o2) {
        if (o1 == null && o2 == null) {
            return 0;
        } else if (o1 == null) {
            return -1;
        } else if (o2 == null) {
            return 1;
        } else if (o1.equals(o2)) {
            return 0;
        }

        int order1 = parseOrder(o1);
        int order2 = parseOrder(o2);

        // never return 0 even if n1 equals n2,
        // otherwise, o1 and o2 will override each other in collection like HashSet
        return order1 > order2 ? 1 : -1;
    }

    private int parseOrder(Class<?> clazz) {
        if (clazz.isAnnotationPresent(Activate.class)) {
            return clazz.getAnnotation(Activate.class).order();
        } else {
            return 0;
        }
    }
}