package cn.huolala.arch.hermes.common.logger.slf4j;

import cn.huolala.arch.hermes.common.logger.Level;
import cn.huolala.arch.hermes.common.logger.support.Log4j2Level;
import cn.huolala.arch.hermes.common.logger.support.Log4jLevel;
import cn.huolala.arch.hermes.common.logger.support.LogBackLevel;
import org.slf4j.Logger;
import org.slf4j.impl.StaticLoggerBinder;

import java.util.function.Consumer;


public interface Slf4jLevelAdapter {

    String LOG4J_LOGGER_FACTORY = "org.slf4j.impl.Log4jLoggerFactory";
    String LOG4J2_LOGGER_FACTORY = "org.apache.logging.slf4j.Log4jLoggerFactory";
    String LOGBACK_LOGGER_FACTORY = "ch.qos.logback.classic.util.ContextSelectorStaticBinder";


    static Consumer<Level> determine(Logger logger) {
        String logFramework = StaticLoggerBinder.getSingleton().getLoggerFactoryClassStr();
        if (LOGBACK_LOGGER_FACTORY.equals(logFramework)) {
            return new LogBackLevel(logger);
        }
        if (LOG4J_LOGGER_FACTORY.equals(logFramework)) {
            return new Log4jLevel(logger);
        }
        if (LOG4J2_LOGGER_FACTORY.equals(logFramework)) {
            return new Log4j2Level(logger);
        }
        return null;
    }


}
