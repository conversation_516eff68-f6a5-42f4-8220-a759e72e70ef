package cn.huolala.arch.hermes.common.logger.support;

import cn.huolala.arch.hermes.common.logger.Level;
import org.apache.logging.log4j.core.config.LoggerConfig;
import org.slf4j.Logger;

import java.util.function.Consumer;

public class Log4j2Level implements Consumer<Level> {

    private final Logger logger;

    public Log4j2Level(Logger logger) {
        this.logger = logger;
    }


    @Override
    public void accept(Level level) {
        org.apache.logging.log4j.core.LoggerContext loggerContext = (org.apache.logging.log4j.core.LoggerContext) org.apache.logging.log4j.LogManager.getContext(false);
        LoggerConfig loggerConfig = loggerContext.getConfiguration().getLoggers().get(this.logger.getName());
        if (loggerConfig != null) {
            loggerConfig.setLevel(toLog4j2Level(level));
            loggerContext.updateLoggers();
        }
    }


    private static org.apache.logging.log4j.Level toLog4j2Level(Level level) {
        if (level == Level.ALL) {
            return org.apache.logging.log4j.Level.ALL;
        }
        if (level == Level.TRACE) {
            return org.apache.logging.log4j.Level.TRACE;
        }
        if (level == Level.DEBUG) {
            return org.apache.logging.log4j.Level.DEBUG;
        }
        if (level == Level.INFO) {
            return org.apache.logging.log4j.Level.INFO;
        }
        if (level == Level.WARN) {
            return org.apache.logging.log4j.Level.WARN;
        }
        if (level == Level.ERROR) {
            return org.apache.logging.log4j.Level.ERROR;
        }
        return org.apache.logging.log4j.Level.OFF;
    }
}
