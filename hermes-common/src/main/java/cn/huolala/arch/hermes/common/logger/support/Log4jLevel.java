package cn.huolala.arch.hermes.common.logger.support;

import cn.huolala.arch.hermes.common.logger.Level;
import org.apache.log4j.LogManager;
import org.slf4j.Logger;

import java.util.function.Consumer;

public class Log4jLevel implements Consumer<Level> {

    private final Logger logger;

    public Log4jLevel(Logger logger) {
        this.logger = logger;
    }


    @Override
    public void accept(Level level) {
        org.apache.log4j.Logger logger = LogManager.getLogger(this.logger.getName());
        logger.setLevel(toLog4jLevel(level));
    }


    private static org.apache.log4j.Level toLog4jLevel(Level level) {
        if (level == Level.ALL) {
            return org.apache.log4j.Level.ALL;
        }
        if (level == Level.TRACE) {
            return org.apache.log4j.Level.TRACE;
        }
        if (level == Level.DEBUG) {
            return org.apache.log4j.Level.DEBUG;
        }
        if (level == Level.INFO) {
            return org.apache.log4j.Level.INFO;
        }
        if (level == Level.WARN) {
            return org.apache.log4j.Level.WARN;
        }
        if (level == Level.ERROR) {
            return org.apache.log4j.Level.ERROR;
        }
        return org.apache.log4j.Level.OFF;
    }

}
