package cn.huolala.arch.hermes.common.logger.support;

import cn.huolala.arch.hermes.common.logger.Level;
import org.slf4j.Logger;

import java.util.function.Consumer;

public class LogBackLevel implements Consumer<Level> {

    private final Logger logger;

    public LogBackLevel(Logger logger) {
        this.logger = logger;
    }


    @Override
    public void accept(Level level) {
        ch.qos.logback.classic.Logger logger = (ch.qos.logback.classic.Logger) this.logger;
        logger.setLevel(toLogBackLevel(level));
    }


    private static ch.qos.logback.classic.Level toLogBackLevel(Level level) {
        if (level == Level.ALL) {
            return ch.qos.logback.classic.Level.ALL;
        }
        if (level == Level.TRACE) {
            return ch.qos.logback.classic.Level.TRACE;
        }
        if (level == Level.DEBUG) {
            return ch.qos.logback.classic.Level.DEBUG;
        }
        if (level == Level.INFO) {
            return ch.qos.logback.classic.Level.INFO;
        }
        if (level == Level.WARN) {
            return ch.qos.logback.classic.Level.WARN;
        }
        if (level == Level.ERROR) {
            return ch.qos.logback.classic.Level.ERROR;
        }
        return ch.qos.logback.classic.Level.OFF;
    }

}
