package cn.huolala.arch.hermes.common.metadata;

import java.util.HashMap;
import java.util.Map;

public abstract class CacheableMetadataSupplier implements MetadataSupplier {

    protected boolean initialized;

    protected Map<String, Object> cacheConfig = new HashMap();

    @Override
    public Map<String, Object> get() {

        if (!initialized) {
            cacheConfig.putAll(getCacheableMetadata());
            initialized = true;
        }
        return cacheConfig;
    }

    public abstract Map<String, Object> getCacheableMetadata();

}
