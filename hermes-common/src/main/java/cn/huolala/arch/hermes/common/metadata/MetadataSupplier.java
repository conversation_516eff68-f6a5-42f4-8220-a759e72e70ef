package cn.huolala.arch.hermes.common.metadata;

import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.common.support.function.ThrowableSupplier;
import cn.huolala.arch.hermes.common.util.StringUtils;

import java.util.Map;
import java.util.Optional;

@SPI
public interface MetadataSupplier extends ThrowableSupplier<Map<String, Object>> {

    String suffix = "MetadataSupplier";

    default String getCode() {

        return Optional.ofNullable(this.getClass().getSimpleName())//
                .filter(str -> str.length() != 0)//
                .map(str -> str.endsWith(suffix) ? str.substring(0, str.length() - suffix.length()) : str)//
                .map(str -> StringUtils.camelToSplitName(str, "-"))//
                .orElse(StringUtils.EMPTY_STRING);
    }

    String getDesc();

}
