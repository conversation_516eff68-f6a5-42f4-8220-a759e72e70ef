package cn.huolala.arch.hermes.common.rule;

import cn.huolala.arch.hermes.common.config.AbstractGovernanceConfiguration;
import cn.huolala.arch.hermes.common.tool.Pair;

import java.util.Objects;
import java.util.Set;


public abstract class AbstractGovernanceRule extends AbstractGovernanceConfiguration {

    protected static final String GLOBAL_NAMESPACE = RouterConstants.Namespace.GLOBAL_GOVERNANCE.getValue();
    protected static final String APP_NAMESPACE = RouterConstants.Namespace.GOVERNANCE.getValue();

    protected RuleRepository ruleRepository;

    protected final boolean defaultGlobalEnable;
    protected final boolean defaultAppEnable;

    public AbstractGovernanceRule(boolean defaultGlobalEnable, boolean defaultAppEnable) {
        this.defaultGlobalEnable = defaultGlobalEnable;
        this.defaultAppEnable = defaultAppEnable;
        this.ruleRepository = RuleRepository.getDefaultExtension();
    }

    @Override
    protected void startListen() {

        if (Objects.isNull(ruleRepository)) {
            this.ruleRepository = RuleRepository.getDefaultExtension();
        }

        // add rule config listener
        Pair<Pair<Set<String>, Set<String>>, Pair<Set<String>, Set<String>>> interestedRuleKeys = interestedRuleKeys();
        Pair<Set<String>, Set<String>> globalRuleKeys = interestedRuleKeys.getLeft();
        Pair<Set<String>, Set<String>> appRuleKeys = interestedRuleKeys.getRight();

        ruleRepository.addListener(this, GLOBAL_NAMESPACE, globalRuleKeys.getLeft(), globalRuleKeys.getRight(), true);
        ruleRepository.addListener(this, APP_NAMESPACE, appRuleKeys.getLeft(), appRuleKeys.getRight(), true);
    }

}
