package cn.huolala.arch.hermes.common.rule;

import cn.huolala.arch.hermes.common.config.dynamic.DynamicConfiguration;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigurationListener;
import cn.huolala.arch.hermes.common.exception.ConfigException;

import java.util.Set;

/**
 * Default RuleRepository
 */
public class DefaultRuleRepository implements RuleRepository {
    public static final String NAME = "default";

    private final DynamicConfiguration dynamicConfiguration = DynamicConfiguration.getDynamicConfiguration();

    @Override
    public void addListener(ConfigurationListener listener, String group, Set<String> keys, Set<String> keyPrefixes, boolean isManualTrigger) {
        dynamicConfiguration.addListener(listener, group, keys, keyPrefixes, isManualTrigger);
    }

    @Override
    public void addListener(ConfigurationListener listener, String group) {
        dynamicConfiguration.addListener(listener, group);
    }

    @Override
    public void removeListener(ConfigurationListener listener, String group) {
        dynamicConfiguration.removeListener(listener, group);
    }

    @Override
    public String getRule(String key, String group, long timeout) throws ConfigException {
        return dynamicConfiguration.getConfig(key, group, timeout);
    }
}
