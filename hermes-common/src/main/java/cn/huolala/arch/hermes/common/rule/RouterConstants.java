package cn.huolala.arch.hermes.common.rule;

import cn.huolala.arch.hermes.common.constants.ConstantsMarker;

import static cn.huolala.arch.hermes.common.constants.Constants.RELEASE_VERSION;
import static cn.huolala.arch.hermes.common.constants.SpiConstants.ORDER_STEP;

/**
 * Governance Constants
 */
public interface RouterConstants extends ConstantsMarker {
    /**
     * key for router type
     */
    String ROUTER_KEY = "router";
    String RULE_ENABLE_KEY = "enabled";
    String RULE_COMMANDS_KEY = "commands";

    /**
     * 2.10.2版本对应的1.0.3版本插件已经废弃使用
     * 1.0.2及以下版本插件还依赖该常量
     */
    String RULE_LEVEL_HIT_KEY = "GovernanceRuleLevelHit";

    String RELEASE_VERSION_KEY = RELEASE_VERSION;

    String RELEASE_VERSION_DEFAULT_VALUE = "default";

    // router
    int ROUTER_MAX_ORDER = -20000;
    int ROUTER_ORDER_STEP = ORDER_STEP;

    int ROUTER_ORDER_VERSION = ROUTER_MAX_ORDER + ROUTER_ORDER_STEP;
    int ROUTER_ORDER_GRAY = ROUTER_MAX_ORDER + ROUTER_ORDER_STEP * 2;

    int ROUTER_ORDER_AZ = ROUTER_MAX_ORDER + ROUTER_ORDER_STEP * 3;

    int ROUTER_ORDER_GROUP = ROUTER_MAX_ORDER + ROUTER_ORDER_STEP * 4;

    /**
     * Governance Namespace
     */
    enum Namespace {
        GOVERNANCE("governance.configurator"),
        GLOBAL_GOVERNANCE("global.governance.configurator"),
        GLOBAL_GOVERNANCE_TRAFFIC("global.governance.traffic"),
        GLOBAL_GOVERNANCE_GRAY("global.governance.gray-versions"),
        GLOBAL_GOVERNANCE_ZONE("global.governance.multi-zones");

        private final String value;

        Namespace(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    enum RuleLevel {
        GLOBAL, APP, APP_COMMANDKEY, SERVICE_COMMANDKEY, METHOD_COMMANDKEY;
    }

}
