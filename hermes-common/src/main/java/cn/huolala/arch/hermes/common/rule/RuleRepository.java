package cn.huolala.arch.hermes.common.rule;

import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigurationListener;
import cn.huolala.arch.hermes.common.exception.ConfigException;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.extension.SPI;

import java.util.Collections;
import java.util.Set;

import static cn.huolala.arch.hermes.common.constants.Constants.CONFIG_GOVERNANCE_DEFAULT_TIMEOUT;

/**
 * Rule Repository via ConfigurationListener
 *
 * @see ConfigurationListener
 */
@SPI(DefaultRuleRepository.NAME)
public interface RuleRepository {

    /**
     * Register a configuration listener for governance purpose<br/>
     *
     * The listener only works for service governance purpose, so the target group would always be the value user
     * specifies at startup or by default. This method will only register listener, which means it will not
     * trigger a notification that contains the current value.
     */
    void addListener(ConfigurationListener listener, String group, Set<String> keys, Set<String> keyPrefixes, boolean isManualTrigger);

    /**
     * Stops one listener from listening to value changes in the specified key.
     */
    void removeListener(ConfigurationListener listener, String group);

    /**
     * {@link #addListener(ConfigurationListener, String, Set, Set)}
     */
    default void addListener(ConfigurationListener listener, String group, Set<String> keys) {
        addListener(listener, group, keys, Collections.emptySet(), false);
    }

    /**
     * {@link #addListener(ConfigurationListener, String, Set, Set)}
     */
    default void addListener(ConfigurationListener listener, String group) {
        addListener(listener, group, Collections.emptySet(), Collections.emptySet(), false);
    }
    
    /**
     * {@link #addListener(ConfigurationListener, String, Set, Set)}
     */
    default void addListener(ConfigurationListener listener, String group, boolean isManualTrigger) {
        addListener(listener, group, Collections.emptySet(), Collections.emptySet(), isManualTrigger);
    }


    /**
     * Get the governance rule mapped to the given key and the given group. If the
     * rule fails to return after timeout exceeds, ConfigException will be thrown.
     */
    String getRule(String key, String group, long timeout) throws ConfigException;

    /**
     * Get the governance rule mapped to the given key and the given group
     */
    default String getRule(String key, String group) throws ConfigException {
        return getRule(key, group, CONFIG_GOVERNANCE_DEFAULT_TIMEOUT);
    }

    static RuleRepository getDefaultExtension() {
        return ExtensionLoader.getExtensionLoader(RuleRepository.class).getDefaultExtension();
    }
}
