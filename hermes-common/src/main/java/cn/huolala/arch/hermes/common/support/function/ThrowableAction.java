package cn.huolala.arch.hermes.common.support.function;

import java.util.function.Function;

/**
 * A function interface for action with {@link Throwable}
 *
 * @see Function
 * @see Throwable
 */
@FunctionalInterface
public interface ThrowableAction {

    /**
     * Executes the action
     *
     * @throws Throwable if met with error
     */
    void execute() throws Throwable;

    /**
     * {@link #execute(ThrowableAction, boolean)}
     */
    static void execute(ThrowableAction action) throws RuntimeException {
        execute(action, false);
    }

    /**
     * Executes {@link ThrowableAction}
     *
     * @param action {@link ThrowableAction}
     * @param ignore ignore exception
     * @throws RuntimeException wrap {@link Exception} to {@link RuntimeException}
     */
    static void execute(ThrowableAction action, boolean ignore) throws RuntimeException {
        try {
            action.execute();
        } catch (Throwable e) {
            if (!ignore) {
                throw new RuntimeException(e);
            }
        }
    }
}
