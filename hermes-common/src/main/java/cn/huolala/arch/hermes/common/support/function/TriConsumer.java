package cn.huolala.arch.hermes.common.support.function;

/**
 * Represents an operation that accepts three input arguments and returns no result
 *
 * @param <F> first input type
 * @param <S> second input type
 * @param <T> third input type
 */
@FunctionalInterface
public interface TriConsumer<F, S, T> {
    /**
     * Performs this operation on the given arguments.
     *
     * @param f the first input argument
     * @param s the second input argument
     * @param t the third input argument
     */
    void accept(F f, S s, T t);
}
