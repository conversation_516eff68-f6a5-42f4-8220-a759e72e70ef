package cn.huolala.arch.hermes.common.thread;

import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.common.thread.support.DefaultExecutorRepository;

import java.util.concurrent.ScheduledExecutorService;

/**
 * Executor Repository
 */
@SPI(DefaultExecutorRepository.NAME)
public interface ExecutorRepository {

    ScheduledExecutorService getBootstrapExecutor();

    /**
     * Destroy all executors that are not in shutdown state
     */
    void destroy();
}
