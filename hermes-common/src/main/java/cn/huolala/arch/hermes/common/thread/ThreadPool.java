package cn.huolala.arch.hermes.common.thread;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.extension.Adaptive;
import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.common.thread.pool.eager.EagerThreadPool;

import java.util.concurrent.ExecutorService;

import static cn.huolala.arch.hermes.common.constants.ThreadConstants.THREADPOOL_KEY;

/**
 * ThreadPool
 */
@SPI(EagerThreadPool.NAME)
public interface ThreadPool {
    /**
     * get concurrent ExecutorService
     */
    @Adaptive({THREADPOOL_KEY})
    ExecutorService getExecutor(URL url);
}
