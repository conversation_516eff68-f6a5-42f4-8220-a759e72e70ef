package cn.huolala.arch.hermes.common.thread.pool.cached;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.thread.NamedThreadFactory;
import cn.huolala.arch.hermes.common.thread.ThreadPool;
import cn.huolala.arch.hermes.common.thread.support.AbortPolicyWithReport;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static cn.huolala.arch.hermes.common.constants.ThreadConstants.ALIVE_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.CORE_THREADS_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_ALIVE;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_CORE_THREADS;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_QUEUES;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_THREAD_NAME;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.QUEUES_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.THREADS_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.THREAD_NAME_KEY;

/**
 * This thread pool is self-tuned. Thread will be recycled after idle for one minute, and new thread will be created for
 * the upcoming request.
 *
 * @see java.util.concurrent.Executors#newCachedThreadPool()
 */
public class CachedThreadPool implements ThreadPool {

    public static final String NAME = "cached";

    @Override
    public ExecutorService getExecutor(URL url) {
        String name = url.getParameter(THREAD_NAME_KEY, DEFAULT_THREAD_NAME + '-' + NAME);
        int cores = url.getParameter(CORE_THREADS_KEY, DEFAULT_CORE_THREADS);
        int threads = url.getParameter(THREADS_KEY, Integer.MAX_VALUE);
        int queues = url.getParameter(QUEUES_KEY, DEFAULT_QUEUES);
        int alive = url.getParameter(ALIVE_KEY, DEFAULT_ALIVE);

        return new ThreadPoolExecutor(cores, threads,
                alive, TimeUnit.MILLISECONDS,
                queues == 0 ? new SynchronousQueue<>() : (queues < 0 ? new LinkedBlockingQueue<>() : new LinkedBlockingQueue<>(queues)),
                new NamedThreadFactory(name, true),
                new AbortPolicyWithReport(name, url));
    }

}
