package cn.huolala.arch.hermes.common.thread.pool.eager;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.thread.NamedThreadFactory;
import cn.huolala.arch.hermes.common.thread.ThreadPool;
import cn.huolala.arch.hermes.common.thread.support.AbortPolicyWithReport;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

import static cn.huolala.arch.hermes.common.constants.ThreadConstants.ALIVE_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.CORE_THREADS_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_ALIVE;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_CORE_THREADS;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_QUEUES_EAGER;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_THREADS;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_THREAD_NAME;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.QUEUES_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.THREADS_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.THREAD_NAME_KEY;

/**
 * When the core threads are all in busy, create new thread instead of putting task into blocking queue.
 */
public class EagerThreadPool implements ThreadPool {

    public static final String NAME = "eager";

    @Override
    public ExecutorService getExecutor(URL url) {
        String name = url.getParameter(THREAD_NAME_KEY, DEFAULT_THREAD_NAME + '-' + NAME);
        int cores = url.getParameter(CORE_THREADS_KEY, DEFAULT_CORE_THREADS);
        int threads = url.getParameter(THREADS_KEY, DEFAULT_THREADS);
        int queues = url.getParameter(QUEUES_KEY, DEFAULT_QUEUES_EAGER);
        int alive = url.getParameter(ALIVE_KEY, DEFAULT_ALIVE);

        // init queue and executor
        TaskQueue taskQueue = queues == 0 ? new TaskQueue(1) : (queues < 0 ? new TaskQueue() : new TaskQueue(queues));
        EagerThreadPoolExecutor executor = new EagerThreadPoolExecutor(cores, threads,
                alive, TimeUnit.MILLISECONDS,
                taskQueue,
                new NamedThreadFactory(name, true),
                new AbortPolicyWithReport(name, url));
        taskQueue.setExecutor(executor);
        return executor;
    }

}
