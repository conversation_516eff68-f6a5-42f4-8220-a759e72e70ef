package cn.huolala.arch.hermes.common.thread.pool.fixed;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.thread.NamedThreadFactory;
import cn.huolala.arch.hermes.common.thread.ThreadPool;
import cn.huolala.arch.hermes.common.thread.support.AbortPolicyWithReport;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_QUEUES_FIXED;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_THREADS;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_THREAD_NAME;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.QUEUES_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.THREADS_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.THREAD_NAME_KEY;

/**
 * Creates a thread pool that reuses a fixed number of threads
 *
 * @see java.util.concurrent.Executors#newFixedThreadPool(int)
 */
public class FixedThreadPool implements ThreadPool {

    public static final String NAME = "fixed";

    @Override
    public ExecutorService getExecutor(URL url) {
        String name = url.getParameter(THREAD_NAME_KEY, DEFAULT_THREAD_NAME + '-' + NAME);
        int threads = url.getParameter(THREADS_KEY, DEFAULT_THREADS);
        int queues = url.getParameter(QUEUES_KEY, DEFAULT_QUEUES_FIXED);

        return new ThreadPoolExecutor(threads, threads,
                0, TimeUnit.MILLISECONDS,
                queues == 0 ? new SynchronousQueue<>() : (queues < 0 ? new LinkedBlockingQueue<>() : new LinkedBlockingQueue<>(queues)),
                new NamedThreadFactory(name, true),
                new AbortPolicyWithReport(name, url));
    }

}
