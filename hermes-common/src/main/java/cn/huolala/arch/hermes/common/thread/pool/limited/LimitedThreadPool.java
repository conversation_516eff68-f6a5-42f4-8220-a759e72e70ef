package cn.huolala.arch.hermes.common.thread.pool.limited;


import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.thread.NamedThreadFactory;
import cn.huolala.arch.hermes.common.thread.ThreadPool;
import cn.huolala.arch.hermes.common.thread.support.AbortPolicyWithReport;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static cn.huolala.arch.hermes.common.constants.ThreadConstants.CORE_THREADS_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_CORE_THREADS;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_QUEUES;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_THREADS;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.DEFAULT_THREAD_NAME;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.QUEUES_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.THREADS_KEY;
import static cn.huolala.arch.hermes.common.constants.ThreadConstants.THREAD_NAME_KEY;

/**
 * Creates a thread pool that creates new threads as needed until limits reaches. This thread pool will not shrink
 * automatically.
 */
public class LimitedThreadPool implements ThreadPool {

    public static final String NAME = "limited";

    @Override
    public ExecutorService getExecutor(URL url) {
        String name = url.getParameter(THREAD_NAME_KEY, DEFAULT_THREAD_NAME + '-' + NAME);
        int cores = url.getParameter(CORE_THREADS_KEY, DEFAULT_CORE_THREADS);
        int threads = url.getParameter(THREADS_KEY, DEFAULT_THREADS);
        int queues = url.getParameter(QUEUES_KEY, DEFAULT_QUEUES);

        return new ThreadPoolExecutor(cores, threads,
                Long.MAX_VALUE, TimeUnit.MILLISECONDS,
                queues == 0 ? new SynchronousQueue<>() : (queues < 0 ? new LinkedBlockingQueue<>() : new LinkedBlockingQueue<>(queues)),
                new NamedThreadFactory(name, true),
                new AbortPolicyWithReport(name, url));
    }

}
