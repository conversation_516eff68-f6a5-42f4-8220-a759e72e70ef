package cn.huolala.arch.hermes.common.thread.support;

import cn.huolala.arch.hermes.common.thread.ExecutorRepository;
import cn.huolala.arch.hermes.common.thread.NamedThreadFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

/**
 * Default ExecutorRepository
 */
public class DefaultExecutorRepository implements ExecutorRepository {
    public static final String NAME = "default";

    private final ScheduledExecutorService bootstrapExecutor;

    public DefaultExecutorRepository() {
        bootstrapExecutor = Executors
                .newScheduledThreadPool(1, new NamedThreadFactory("Bootstrap-scheduler"));
    }

    @Override
    public ScheduledExecutorService getBootstrapExecutor() {
        return bootstrapExecutor;
    }

    @Override
    public void destroy() {
        bootstrapExecutor.shutdownNow();
    }
}