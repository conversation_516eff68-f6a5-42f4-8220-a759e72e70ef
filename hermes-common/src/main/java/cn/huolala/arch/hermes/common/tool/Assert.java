package cn.huolala.arch.hermes.common.tool;

import cn.huolala.arch.hermes.common.util.StringUtils;

/**
 * Assertion utility class that assists in validating arguments.
 */
public final class Assert {

    private Assert() {
    }

    public static void notNull(Object obj, String message) {
        if (obj == null) {
            throw new IllegalArgumentException(message);
        }
    }

    public static void notEmpty(String str, String message) {
        if (StringUtils.isEmpty(str)) {
            throw new IllegalArgumentException(message);
        }
    }

    public static void notNull(Object obj, RuntimeException exception) {
        if (obj == null) {
            throw exception;
        }
    }

    public static void notEmpty(String str, RuntimeException exception) {
        if (StringUtils.isEmpty(str)) {
            throw exception;
        }
    }
}