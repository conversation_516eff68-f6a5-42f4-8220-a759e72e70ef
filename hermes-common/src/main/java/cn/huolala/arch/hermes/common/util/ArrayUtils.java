package cn.huolala.arch.hermes.common.util;

import java.lang.reflect.Array;

/**
 * Array Utils
 */
public final class ArrayUtils {

    private ArrayUtils() {
    }

    /**
     * <p>Checks if the array is null or empty. <p/>
     *
     * @param array th array to check
     * @return {@code true} if the array is null or empty.
     */
    public static boolean isEmpty(final Object[] array) {
        return array == null || array.length == 0;
    }

    /**
     * <p>Checks if the array is not null or empty. <p/>
     *
     * @param array th array to check
     * @return {@code true} if the array is not null or empty.
     */
    public static boolean isNotEmpty(final Object[] array) {
        return !isEmpty(array);
    }

    public static boolean contains(final String[] array, String valueToFind) {
        return indexOf(array, valueToFind, 0) != -1;
    }

    public static int indexOf(String[] array, String valueToFind, int startIndex) {
        if (!isEmpty(array) && valueToFind != null) {
            if (startIndex < 0) {
                startIndex = 0;
            }

            for (int i = startIndex; i < array.length; ++i) {
                if (valueToFind.equals(array[i])) {
                    return i;
                }
            }

        }
        return -1;
    }

    public static int length(Object array) throws IllegalArgumentException {
        return null == array ? 0 : Array.getLength(array);
    }

    public static <T> T[] append(T[] buffer, T... newElements) {
        return isEmpty(buffer) ? newElements : insert(buffer, buffer.length, newElements);
    }
    
    /**
     * <p>Adds all the elements of the given arrays into a new array.</p>
     * <p>The new array contains all of the element of <code>array1</code> followed
     * by all of the elements <code>array2</code>. When an array is returned, it is always
     * a new array.</p>
     *
     * <pre>
     * ArrayUtils.addAll(null, null)     = null
     * ArrayUtils.addAll(array1, null)   = cloned copy of array1
     * ArrayUtils.addAll(null, array2)   = cloned copy of array2
     * ArrayUtils.addAll([], [])         = []
     * ArrayUtils.addAll([null], [null]) = [null, null]
     * ArrayUtils.addAll(["a", "b", "c"], ["1", "2", "3"]) = ["a", "b", "c", "1", "2", "3"]
     * </pre>
     *
     * @param array1  the first array whose elements are added to the new array, may be <code>null</code>
     * @param array2  the second array whose elements are added to the new array, may be <code>null</code>
     * @return The new array, <code>null</code> if <code>null</code> array inputs. 
     *      The type of the new array is the type of the first array.
     * @since 2.1
     */
    public static Object[] addAll(Object[] array1, Object[] array2) {
        if (array1 == null) {
            return clone(array2);
        } else if (array2 == null) {
            return clone(array1);
        }
        Object[] joinedArray = (Object[]) Array.newInstance(array1.getClass().getComponentType(),
                                                            array1.length + array2.length);
        System.arraycopy(array1, 0, joinedArray, 0, array1.length);
        System.arraycopy(array2, 0, joinedArray, array1.length, array2.length);
        return joinedArray;
    }
    
    /**
     * <p>Shallow clones an array returning a typecast result and handling
     * <code>null</code>.</p>
     *
     * <p>The objects in the array are not cloned, thus there is no special
     * handling for multi-dimensional arrays.</p>
     * 
     * <p>This method returns <code>null</code> for a <code>null</code> input array.</p>
     * 
     * @param array  the array to shallow clone, may be <code>null</code>
     * @return the cloned array, <code>null</code> if <code>null</code> input
     */
    public static Object[] clone(Object[] array) {
        if (array == null) {
            return null;
        }
        return (Object[]) array.clone();
    }


    public static <T> T[] insert(T[] array, int index, T... newElements) {
        if (isEmpty(newElements)) {
            return array;
        } else if (isEmpty(array)) {
            return newElements;
        } else {
            int len = length(array);
            if (index < 0) {
                index = index % len + len;
            }

            T[] result = newArray(array.getClass().getComponentType(), Math.max(len, index) + newElements.length);
            System.arraycopy(array, 0, result, 0, Math.min(len, index));
            System.arraycopy(newElements, 0, result, index, newElements.length);
            if (index < len) {
                System.arraycopy(array, index, result, index + newElements.length, len - index);
            }

            return result;
        }
    }


    public static <T> T[] newArray(Class<?> componentType, int newSize) {
        return (T[])Array.newInstance(componentType, newSize);
    }

}
