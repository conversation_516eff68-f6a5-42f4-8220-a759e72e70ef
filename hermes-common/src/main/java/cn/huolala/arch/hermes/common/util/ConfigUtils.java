package cn.huolala.arch.hermes.common.util;

import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Config Utils
 */
public final class ConfigUtils {
    private static final Logger logger = LoggerFactory.getLogger(ConfigUtils.class);

    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\s*\\{?\\s*([\\._0-9a-zA-Z]+)\\s*\\}?");

    private ConfigUtils() {
    }

    public static boolean isNotEmpty(String value) {
        return !isEmpty(value);
    }

    public static boolean isEmpty(String value) {
        return StringUtils.isEmpty(value)
                || "false".equalsIgnoreCase(value)
                || "0".equalsIgnoreCase(value)
                || "null".equalsIgnoreCase(value)
                || "N/A".equalsIgnoreCase(value);
    }

    public static boolean isDefault(String value) {
        return "true".equalsIgnoreCase(value)
                || "default".equalsIgnoreCase(value);
    }

    /**
     * replace property via expression
     * <br/>
     * replaceProperty("1${a.b.c}2${a.b.c}3", Collections.singletonMap("a.b.c", "ABC")) => 1ABC2ABC3
     */
    public static String replaceProperty(String expression, Map<?, ?> params) {
        if (expression == null || expression.length() == 0 || expression.indexOf('$') < 0) {
            return expression;
        }
        StringBuffer sb = new StringBuffer();
        Matcher matcher = VARIABLE_PATTERN.matcher(expression);
        while (matcher.find()) {
            String key = matcher.group(1);
            String value = System.getProperty(key);
            if (value == null) {
                value = Optional.ofNullable(params)
                        .map(map -> map.get(key))
                        .map(Object::toString)
                        .orElse("");
            }
            matcher.appendReplacement(sb, Matcher.quoteReplacement(value));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * System environment -> System properties
     * @param key key
     * @return value
     */
    public static String getSystemProperty(String key) {
        String value = System.getenv(key);
        if (StringUtils.isEmpty(value)) {
            value = System.getProperty(key);
        }
        return value;
    }

    /**
     * loadProperties
     * @see #loadProperties(String, boolean, boolean)
     */
    public static Properties loadProperties(String fileName) {
        return loadProperties(fileName, false, false);
    }

    /**
     * loadProperties
     * @see #loadProperties(String, boolean, boolean)
     */
    public static Properties loadProperties(String fileName, boolean allowMultiFile) {
        return loadProperties(fileName, allowMultiFile, false);
    }

    /**
     * Load properties file to {@link Properties} from class path.
     *
     * @param fileName properties file name. for example: <code>foo.properties</code>, <code>METE-INF/conf/foo.properties</code>
     * @param allowMultiFile if <code>false</code>, throw {@link IllegalStateException} when found multi file on the class path.
     * @param optional is optional. if <code>false</code>, log warn when properties config file not found
     * @return loaded {@link Properties} content. <ul>
     * <li>return empty Properties if no file found.
     * <li>merge multi properties file if found multi file
     * </ul>
     * @throws IllegalStateException not allow multi-file, but multi-file exist on class path.
     */
    public static Properties loadProperties(String fileName, boolean allowMultiFile, boolean optional) {
        Properties properties = new Properties();
        // add scene judgement in windows environment
        if (checkFileNameExist(fileName)) {
            try (FileInputStream input = new FileInputStream(fileName)) {
                properties.load(input);
            } catch (Throwable e) {
                logger.warn("Failed to load " + fileName + " file from " + fileName + "(ignore this file): " + e.getMessage(), e);
            }
            return properties;
        }

        List<URL> urlList = Collections.emptyList();
        try {
            urlList = Collections.list(ClassUtils.getClassLoader().getResources(fileName));
        } catch (Throwable t) {
            logger.warn("Fail to load " + fileName + " file: " + t.getMessage(), t);
        }

        if (!urlList.isEmpty()) {
            if (!allowMultiFile) {
                if (urlList.size() > 1) {
                    logger.warn(String.format("only 1 %s file is expected, but %d properties files found on class path: %s",
                            fileName, urlList.size(), urlList));
                }

                try {
                    // fall back to use method getResourceAsStream
                    properties.load(ClassUtils.getClassLoader().getResourceAsStream(fileName));
                } catch (Throwable e) {
                    logger.warn("Failed to load " + fileName + " file from " + fileName + "(ignore this file): " + e.getMessage(), e);
                }
            } else {
                // allowMultiFile
                logger.info("load " + fileName + " properties file from " + urlList);

                for (URL url : urlList) {
                    try (InputStream input = url.openStream()) {
                        if (input != null) {
                            Properties p = new Properties();
                            p.load(input);
                            properties.putAll(p);
                        }
                    } catch (Throwable e) {
                        logger.warn("Failed to load " + fileName + " file from " + url + "(ignore this file): " + e.getMessage(), e);
                    }
                }
            }
        } else if (!optional) {
            logger.warn("No " + fileName + " found on the class path.");
        }

        return properties;
    }

    /**
     * check if the fileName can be found in filesystem
     */
    private static boolean checkFileNameExist(String fileName) {
        File file = new File(fileName);
        return file.exists();
    }
}
