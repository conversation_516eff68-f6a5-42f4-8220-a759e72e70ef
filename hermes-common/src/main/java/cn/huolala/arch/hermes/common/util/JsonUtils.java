package cn.huolala.arch.hermes.common.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.io.InputStream;

/**
 * Json Utils
 */
public final class JsonUtils {
    private static final ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    public static ObjectMapper getObjectMapper() {
        return objectMapper;
    }

    public static String toJSONString(Object obj) throws IOException {
        return objectMapper.writeValueAsString(obj);
    }

    public static <T> T parse(String json, Class<T> clazz) throws IOException {
        return objectMapper.readValue(json, clazz);
    }

    public static <T> T parse(String json, TypeReference<T> type) throws IOException {
        return objectMapper.readValue(json, type);
    }

    public static String serialize(Object obj) throws JsonProcessingException {
        return objectMapper.writeValueAsString(obj);
    }

    public static String serializeIgnoreException(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException ignore) {
        }
        return "";
    }

    public static <T> T serialize(Object obj, TypeReference<T> type) throws JsonProcessingException {
        return objectMapper.convertValue(obj, type);
    }

    public static <T> T deserialize(String jsonText, TypeReference<T> type) throws JsonProcessingException {
        return objectMapper.readValue(jsonText, type);
    }

    public static <T> T deserialize(String jsonText, Class<T> beanClass) throws JsonProcessingException {
        return objectMapper.readValue(jsonText, beanClass);
    }

    public static <T> T deserialize(InputStream inputStream, Class<T> beanClass) throws IOException {
        return objectMapper.readValue(inputStream, beanClass);
    }

    public static <T> T deserialize(InputStream inputStream, TypeReference<T> type) throws IOException {
        return objectMapper.readValue(inputStream, type);
    }

    public static JsonNode deserialize(String jsonText) throws JsonProcessingException {
        return objectMapper.readTree(jsonText);
    }

    private JsonUtils() {
    }
}
