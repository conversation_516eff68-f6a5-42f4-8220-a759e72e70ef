package cn.huolala.arch.hermes.common.util;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.logger.support.FailsafeLogger;
import cn.huolala.arch.hermes.common.url.URLStrParser;

import java.io.IOException;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.Enumeration;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

import static cn.huolala.arch.hermes.common.constants.Constants.ANYHOST_VALUE;
import static cn.huolala.arch.hermes.common.constants.Constants.COMMA_SEPARATOR_CHAR;
import static cn.huolala.arch.hermes.common.constants.Constants.DEFAULT_NETWORK_IGNORED_INTERFACE;
import static cn.huolala.arch.hermes.common.constants.Constants.LOCALHOST_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.LOCALHOST_VALUE;
import static cn.huolala.arch.hermes.common.constants.Constants.NETWORK_IGNORED_INTERFACE;
import static java.util.Collections.emptyList;

/**
 * Net Utils
 */
public final class NetUtils {
    private static final Pattern LOCAL_IP_PATTERN = Pattern.compile("127(\\.\\d{1,3}){3}$");
    private static final Pattern IP_PATTERN = Pattern.compile("\\d{1,3}(\\.\\d{1,3}){3,5}$");

    private static volatile String hostAddress;
    private static volatile InetAddress localAddress = null;

    private static Logger logger;

    static {
        logger = LoggerFactory.getLogger(NetUtils.class);
        if (logger instanceof FailsafeLogger) {
            logger = ((FailsafeLogger) logger).getLogger();
        }
    }

    private NetUtils() {
    }

    public static boolean isLocalHost(String host) {
        return host != null
                && (LOCAL_IP_PATTERN.matcher(host).matches()
                || host.equalsIgnoreCase(LOCALHOST_KEY));
    }

    public static boolean isInvalidLocalHost(String host) {
        return host == null
                || host.length() == 0
                || host.equalsIgnoreCase(LOCALHOST_KEY)
                || host.equals(ANYHOST_VALUE)
                || host.startsWith("127.");
    }

    /**
     * getIpByHost
     * @return ip address or hostName if UnknownHostException
     */
    public static String getIpByHost(String hostName) {
        try {
            return InetAddress.getByName(hostName).getHostAddress();
        } catch (UnknownHostException e) {
            return hostName;
        }
    }

    public static String filterLocalHost(String host) {
        if (host == null || host.length() == 0) {
            return host;
        }
        if (host.contains("://")) {
            URL u = URLStrParser.parseDecodedStr(host);
            if (NetUtils.isInvalidLocalHost(u.getHost())) {
                return u.setHost(NetUtils.getLocalHost()).toFullString();
            }
        } else if (host.contains(":")) {
            int i = host.lastIndexOf(':');
            if (NetUtils.isInvalidLocalHost(host.substring(0, i))) {
                return NetUtils.getLocalHost() + host.substring(i);
            }
        } else {
            if (NetUtils.isInvalidLocalHost(host)) {
                return NetUtils.getLocalHost();
            }
        }
        return host;
    }

    public static String getLocalHost() {
        if (hostAddress != null) {
            return hostAddress;
        }

        InetAddress address = getLocalAddress();
        if (address != null) {
            hostAddress = address.getHostAddress();
            return hostAddress;
        }
        return LOCALHOST_VALUE;
    }

    /**
     * Find first valid IP from local network card
     *
     * @return first valid local IP
     */
    public static InetAddress getLocalAddress() {
        if (localAddress != null) {
            return localAddress;
        }
        InetAddress localAddress = getLocalAddress0();
        NetUtils.localAddress = localAddress;
        return localAddress;
    }

    private static Optional<InetAddress> toValidAddress(InetAddress address) {
        if (address instanceof Inet6Address) {
            Inet6Address v6Address = (Inet6Address) address;
            if (isPreferIPV6Address()) {
                return Optional.ofNullable(normalizeV6Address(v6Address));
            }
        }
        if (isValidV4Address(address)) {
            return Optional.of(address);
        }
        return Optional.empty();
    }

    /**
     * normalize the ipv6 Address, convert scope name to scope id.
     * e.g.
     * convert
     * fe80:0:0:0:894:aeec:f37d:23e1%en0
     * to
     * fe80:0:0:0:894:aeec:f37d:23e1%5
     * <p>
     * The %5 after ipv6 address is called scope id.
     * see java doc of {@link Inet6Address} for more details.
     *
     * @param address the input address
     * @return the normalized address, with scope id converted to int
     */
    private static InetAddress normalizeV6Address(Inet6Address address) {
        String addr = address.getHostAddress();
        int i = addr.lastIndexOf('%');
        if (i > 0) {
            try {
                return InetAddress.getByName(addr.substring(0, i) + '%' + address.getScopeId());
            } catch (UnknownHostException e) {
                // ignore
                logger.debug("Unknown IPV6 address: ", e);
            }
        }
        return address;
    }

    /**
     * Check if an ipv6 address
     *
     * @return true if it is reachable
     */
    private static boolean isPreferIPV6Address() {
        return Boolean.getBoolean("java.net.preferIPv6Addresses");
    }

    private static boolean isValidV4Address(InetAddress address) {
        if (address == null || address.isLoopbackAddress()) {
            return false;
        }

        String name = address.getHostAddress();
        return name != null && IP_PATTERN.matcher(name).matches()
                && !ANYHOST_VALUE.equals(name) && !LOCALHOST_VALUE.equals(name);
    }

    private static InetAddress getLocalAddress0() {
        InetAddress localAddress = null;

        // choose the {@link NetworkInterface} first
        try {
            NetworkInterface networkInterface = findNetworkInterface();
            Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
            while (addresses.hasMoreElements()) {
                Optional<InetAddress> addressOp = toValidAddress(addresses.nextElement());
                if (addressOp.isPresent()) {
                    try {
                        if (addressOp.get().isReachable(100)) {
                            return addressOp.get();
                        }
                    } catch (IOException ignored) {
                    }
                }
            }
        } catch (Throwable e) {
            logger.warn(e);
        }

        try {
            localAddress = InetAddress.getLocalHost();
            Optional<InetAddress> addressOp = toValidAddress(localAddress);
            if (addressOp.isPresent()) {
                return addressOp.get();
            }
        } catch (Throwable e) {
            logger.warn(e);
        }

        return localAddress;
    }

    /**
     * Get the valid {@link NetworkInterface network interfaces}
     *
     * @return the valid {@link NetworkInterface}s
     * @throws SocketException if an I/O error occurs.
     */
    private static List<NetworkInterface> getValidNetworkInterfaces() throws SocketException {
        List<NetworkInterface> validNetworkInterfaces = new LinkedList<>();
        Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
        while (interfaces.hasMoreElements()) {
            NetworkInterface networkInterface = interfaces.nextElement();
            if (ignoreNetworkInterface(networkInterface)) {
                // ignore
                continue;
            }
            validNetworkInterfaces.add(networkInterface);
        }
        return validNetworkInterfaces;
    }

    /**
     * Returns {@code true} if the specified {@link NetworkInterface} should be ignored with the given conditions.
     *
     * @param networkInterface the {@link NetworkInterface} to check
     * @return {@code true} if the specified {@link NetworkInterface} should be ignored, otherwise {@code false}
     * @throws SocketException if an I/O error occurs.
     */
    private static boolean ignoreNetworkInterface(NetworkInterface networkInterface) throws SocketException {
        if (networkInterface == null
                || networkInterface.isLoopback()
                || networkInterface.isVirtual()
                || !networkInterface.isUp()) {
            return true;
        }

        String ignoredInterfaces = System.getProperty(NETWORK_IGNORED_INTERFACE);
        if (StringUtils.isEmpty(ignoredInterfaces)) {
            ignoredInterfaces = DEFAULT_NETWORK_IGNORED_INTERFACE;
        }

        String networkInterfaceDisplayName = networkInterface.getDisplayName();
        if (StringUtils.isNotEmpty(networkInterfaceDisplayName)) {
            try {
                for (String ignoredInterface : ignoredInterfaces.split(String.valueOf(COMMA_SEPARATOR_CHAR))) {
                    String trimIgnoredInterface = ignoredInterface.trim();
                    if (networkInterfaceDisplayName.matches(trimIgnoredInterface)
                            || networkInterfaceDisplayName.equalsIgnoreCase(trimIgnoredInterface)) {
                        return true;
                    }
                }
            } catch (PatternSyntaxException e) {
                logger.warn("exception occurred: " + networkInterfaceDisplayName + " matches", e);
            }
        }

        return false;
    }

    /**
     * Get the suitable {@link NetworkInterface}
     *
     * @return If no {@link NetworkInterface} is available , return <code>null</code>
     */
    public static NetworkInterface findNetworkInterface() {

        List<NetworkInterface> validNetworkInterfaces = emptyList();
        try {
            validNetworkInterfaces = getValidNetworkInterfaces();
        } catch (Throwable e) {
            logger.warn(e);
        }

        // try to get the first one
        for (NetworkInterface networkInterface : validNetworkInterfaces) {
            Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
            while (addresses.hasMoreElements()) {
                Optional<InetAddress> addressOp = toValidAddress(addresses.nextElement());
                if (addressOp.isPresent()) {
                    try {
                        if (addressOp.get().isReachable(100)) {
                            return networkInterface;
                        }
                    } catch (IOException ignored) {
                    }
                }
            }
        }

        return CollectionUtils.first(validNetworkInterfaces);
    }


    public static boolean isValidIp(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return false;
        }
        return IP_PATTERN.matcher(ip).matches();
    }

    public static String getLocalHostName() {
        String name = null;
        final InetAddress localAddress = getLocalAddress();
        if (Objects.nonNull(localAddress)) {
            name = localAddress.getHostName();
            if (StringUtils.isEmpty(name)) {
                name = localAddress.getHostAddress();
            }
        }
        return name;
    }

    public static String getHostName() {
        try {
            return InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException ignore) {
        }
        try {
            Process process = Runtime.getRuntime().exec("hostname");
            return StringUtils.removeEnd(StringUtils.readFromInputStream(process.getInputStream()), StringUtils.LINE_FEED_STRING);
        } catch (IOException ignore) {
        }
        return StringUtils.EMPTY_STRING;
    }
}
