package cn.huolala.arch.hermes.common.util;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.url.URLBuilder;

import java.util.Optional;

/**
 * URLUtils
 */
public final class URLUtils {

    private URLUtils() {
    }

    public static URLBuilder newURLBuilder(String protocol) {
        return new URLBuilder().setProtocol(protocol);
    }

    public static URLBuilder newURLBuilder(URL url) {
        return URLBuilder.from(url);
    }

    public static URLBuilder newURLBuilder(String protocol, String address, Integer port) {
        URLBuilder builder = new URLBuilder();
        Optional.ofNullable(port).ifPresent(builder::setPort);

        return builder.setProtocol(protocol)
                .setAddress(address);
    }

}
