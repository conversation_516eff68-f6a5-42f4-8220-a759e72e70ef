package cn.huolala.arch.hermes.common.url;

import cn.huolala.arch.hermes.common.URL;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * URLStrParserTest
 */
public class URLStrParserTest {
    private static Set<String> testCases = new HashSet<>(16);
    private static Set<String> errorDecodedCases = new HashSet<>(8);
    private static Set<String> errorEncodedCases = new HashSet<>(8);

    static {
        testCases.add("rpc://***********");
        testCases.add("rpc://***********?");
        testCases.add("rpc://127.0.0.1?test=");
        testCases.add("rpc://127.0.0.1?test=中文测试");
        testCases.add("rpc://admin:admin123@************:28113/org.test.api.DemoService$Iface?anyhost=true&application=demo-service&generic=false&interface=org.test.api.DemoService$Iface&methods=orbCompare,checkText,checkPicture&pid=65557&revision=1.4.17&service.filter=bootMetrics&side=provider&status=server&threads=200&timestamp=1583136298859&version=1.0.0");
        // super long text test
        testCases.add("file:/path/to/file.txt");
        testCases.add("rpc://fe80:0:0:0:894:aeec:f37d:23e1%en0/path?abc=abc");

        errorDecodedCases.add("rpc:***********");
        errorDecodedCases.add("://***********");
        errorDecodedCases.add(":/***********");

        errorEncodedCases.add("rpc%3a%2f%2f************%3fabc%3");
        errorEncodedCases.add("rpc%3a***********%3fabc%3dabc");
        errorEncodedCases.add("%3a%2f%2f***********%3fabc%3dabc");
        errorEncodedCases.add("%3a%2f***********%3fabc%3dabc");
        errorEncodedCases.add("rpc%3a%2f%2f127.0.0.1%3ftest%3d%e2%96%b2%e2%96%bc%e2%97%80%e2%96%b6%e2%86%90%e2%86%91%e2%86%92%e2%86%93%e2%86%94%e2%86%95%e2%88%9e%c2%b1%e9%be%98%e9%9d%90%e9%bd%89%9%d%b");
    }

    @Test
    public void testParser() {
        testCases.forEach(testCase -> assertEquals(URLStrParser.parseDecodedStr(testCase), URLStrParser.parseEncodedStr(URL.encode(testCase))));

        errorEncodedCases.forEach(errorCase ->
                assertThrows(RuntimeException.class, () -> URLStrParser.parseEncodedStr(errorCase)));
        errorDecodedCases.forEach(errorCase ->
                assertThrows(RuntimeException.class, () -> URLStrParser.parseDecodedStr(errorCase)));
    }

    @Test
    public void testEmptyParamValue() {
        String testCase = "rpc://127.0.0.1?abc=123&test=";
        assertEquals(testCase, URLStrParser.parseDecodedStr(testCase).toString());
    }
}
