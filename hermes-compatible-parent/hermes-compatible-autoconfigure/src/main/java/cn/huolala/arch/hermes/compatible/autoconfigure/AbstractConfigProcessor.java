package cn.huolala.arch.hermes.compatible.autoconfigure;

import static cn.huolala.arch.hermes.config.spring.SpringConstants.COMPOSITE_PROPERTIES_KEY;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;

import cn.huolala.arch.hermes.config.spring.boot.ConfigProcessor;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;

import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.config.spring.boot.ConfigProperties;

public abstract class AbstractConfigProcessor implements ConfigProcessor {

    protected ConfigurableEnvironment environment;

    protected ConfigProperties configProperties;

    @Override
    public void process(ConfigurableEnvironment environment, ConfigProperties configProperties) {
        this.configProperties = configProperties;
        this.environment = environment;

        Map<String, Object> source = ((MapPropertySource) environment.getPropertySources()
                .get(COMPOSITE_PROPERTIES_KEY)).getSource();

        doProcess(source);
    }

    protected Optional<String> findFirstMatch(Collection<String> names) {
        return names.stream().map(environment::getProperty).filter(StringUtils::isNotBlank).findFirst();
    }

    protected abstract void doProcess(Map<String, Object> source);
}
