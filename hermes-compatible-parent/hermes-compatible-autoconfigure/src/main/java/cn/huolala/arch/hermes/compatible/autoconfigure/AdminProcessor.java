package cn.huolala.arch.hermes.compatible.autoconfigure;

import static cn.huolala.arch.hermes.common.constants.Constants.ADMIN_HOST_KEY;

import java.util.Map;

import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.support.Prioritized;

public class AdminProcessor extends AbstractConfigProcessor {

    @Override
    protected void doProcess(Map<String, Object> source) {
        ApplicationContext.addAttribute(ADMIN_HOST_KEY, environment.getProperty(ADMIN_HOST_KEY));
    }

    @Override
    public int getPriority() {
        return Prioritized.MAX_PRIORITY + 300;
    }
}
