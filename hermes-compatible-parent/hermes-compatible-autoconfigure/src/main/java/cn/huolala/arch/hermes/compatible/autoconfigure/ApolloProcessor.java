package cn.huolala.arch.hermes.compatible.autoconfigure;

import cn.huolala.arch.hermes.api.config.ApplicationConfig;
import cn.huolala.arch.hermes.api.config.ConfigCenterConfig;
import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.support.Prioritized;
import cn.huolala.arch.hermes.common.util.JsonUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.autoconfigure.http.HttpResult;
import cn.huolala.arch.hermes.compatible.autoconfigure.http.HttpUtils;
import cn.huolala.tech.meta.system.SystemUtil;

import com.ctrip.framework.apollo.core.signature.Signature;
import com.ctrip.framework.foundation.internals.provider.DefaultApplicationProvider;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.escape.Escaper;
import com.google.common.net.UrlEscapers;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import static cn.huolala.arch.hermes.common.constants.SpiConstants.CONFIG_CENTER_DEFAULT;
import static cn.huolala.arch.hermes.common.rule.RouterConstants.Namespace.GLOBAL_GOVERNANCE;
import static cn.huolala.arch.hermes.config.spring.SpringConstants.CONFIG_PREFIX;
import static java.util.Optional.ofNullable;

public class ApolloProcessor extends AbstractConfigProcessor {
    private static final Logger logger = LoggerFactory.getLogger(ApolloProcessor.class);
    private static final List<String> APOLLO_ADDRESS_CONFIGS = Arrays.asList("apollo.configService", "apollo.meta");
    private static final List<String> LOAD_NAMESPACES = Arrays.asList(GLOBAL_GOVERNANCE.getValue(), "application");
    private static final Joiner.MapJoiner MAP_JOINER = Joiner.on("&").withKeyValueSeparator("=");
    private static final Escaper pathEscaper = UrlEscapers.urlPathSegmentEscaper();
    private static final Escaper queryParamEscaper = UrlEscapers.urlFormParameterEscaper();
    private static final String APOLLO_PROTOCOL_PREFIX = "http://";

    private static final String APOLLO_CLUSTER_KEY = "apollo.cluster";

    private static final String APOLLO_LABEL_KEY = "apollo.label";

    private static final DefaultApplicationProvider appProvider = new DefaultApplicationProvider();
    private static AtomicBoolean appProviderInitialize = new AtomicBoolean();

    @Override
    public void doProcess(Map<String, Object> source) {
        ConfigCenterConfig configCenter = ofNullable(configProperties.getConfigCenter()).orElse(new ConfigCenterConfig());
        ApplicationConfig application = ofNullable(configProperties.getApplication()).orElse(new ApplicationConfig());
        String address = addressProcess(configCenter, source);
        if (StringUtils.isEmpty(address)) {
            logger.warn("Apollo address can't find，Unable to complete config compatible.");
            return;
        }
        address = StringUtils.addPrefixIfNot(address, APOLLO_PROTOCOL_PREFIX);

        String appId = application.getName();
        String cluster = SystemUtil.get(APOLLO_CLUSTER_KEY, Constants.DEFAULT_KEY);
        String label = SystemUtil.get(APOLLO_LABEL_KEY);

        // load config: global.governance.configurator + application(lala.soa.*)
        for (String namespace : LOAD_NAMESPACES) {
            String queryConfigUrl = assembleQueryConfigUrl(address, appId, cluster, namespace, label);
            Map<String, String> apolloConfig = loadApolloConfig(queryConfigUrl, appId);
            apolloConfig.entrySet().stream().filter(config -> StringUtils.isNotBlank(config.getKey())
                            && (config.getKey().startsWith(CONFIG_PREFIX) || GLOBAL_GOVERNANCE.getValue().equals(namespace)))
                    .forEach(entry -> source.put(entry.getKey(), getProperty(entry.getKey(), entry.getValue())));
        }
    }

    protected Map<String, String> loadApolloConfig(String configUrl, String appId) {
        Map<String, String> source = new HashMap<>();
        try {
            Map<String, String> headers = getSecretHeaders(configUrl, appId);
            HttpResult result = HttpUtils.doGet(configUrl, headers, null, null);
            if (result.getCode() == HttpStatus.SC_OK) {
                source = JsonUtils.parse(result.getBody(), Configurations.class).getConfigurations();
            } else {
                logger.error("获取配置信息失败，result结果：code={},body={}", result.getCode(), result.getBody());
            }
        } catch (Exception e) {
            logger.warn("Request {} exception，Compatible apollo config failed.", configUrl, e);
        }
        return source;
    }

    private String addressProcess(ConfigCenterConfig config, Map<String, Object> source) {
        if (isDefaultProtocol(config)) {
            findFirstMatch(APOLLO_ADDRESS_CONFIGS).ifPresent(v -> {
                config.setAddress(v);
                config.setPort(null);
            });
        }
        source.put(CONFIG_PREFIX + ".config-center.address", config.getAddress());
        source.put(CONFIG_PREFIX + ".config-center.port", config.getPort());

        String address = config.getAddress();
        Integer port = config.getPort();
        return port == null ? address : address + ":" + port;
    }

    private String getProperty(String key, String defaultValue) {
        String value = System.getProperty(key);
        if (value == null) {
            value = System.getenv(key);
        }
        return value == null ? defaultValue : value;
    }

    private boolean isDefaultProtocol(ConfigCenterConfig config) {
        return StringUtils.isBlank(config.getProtocol()) || config.getProtocol().equals(CONFIG_CENTER_DEFAULT);
    }


    private String assembleQueryConfigUrl(String uri, String appId, String clusterName, String namespace, String label) {

        String path = "configs/%s/%s/%s";

        List<String> pathParams = Lists.newArrayList(pathEscaper.escape(appId), pathEscaper.escape(clusterName), pathEscaper.escape(namespace));

        Map<String, String> queryParams = Maps.newHashMap();

        if (!Strings.isNullOrEmpty(label)) {
            queryParams.put("label", queryParamEscaper.escape(label));
        }

        String pathExpanded = String.format(path, pathParams.toArray());

        if (!queryParams.isEmpty()) {
            pathExpanded += "?" + MAP_JOINER.join(queryParams);
        }
        if (!uri.endsWith("/")) {
            uri += "/";
        }
        return uri + pathExpanded;
    }

    private Map<String, String> getSecretHeaders(String uri, String appId) {
        Map<String, String> headers = new HashMap<>();

        if (!appProviderInitialize.get()) {
            appProvider.initialize();
            appProviderInitialize.set(true);
        }

        String secret = appProvider.getAccessKeySecret();
        if (StringUtils.isNotBlank(secret)) {
            headers = Signature.buildHttpHeaders(uri, appId, secret);
        }

        return headers;
    }

    @Override
    public int getPriority() {
        return Prioritized.MAX_PRIORITY + 100;
    }

    protected static class Configurations {
        private String appId;

        private String cluster;

        private String namespaceName;

        private Map<String, String> configurations;

        private String releaseKey;

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getCluster() {
            return cluster;
        }

        public void setCluster(String cluster) {
            this.cluster = cluster;
        }

        public String getNamespaceName() {
            return namespaceName;
        }

        public void setNamespaceName(String namespaceName) {
            this.namespaceName = namespaceName;
        }

        public Map<String, String> getConfigurations() {
            return configurations;
        }

        public void setConfigurations(Map<String, String> configurations) {
            this.configurations = configurations;
        }

        public String getReleaseKey() {
            return releaseKey;
        }

        public void setReleaseKey(String releaseKey) {
            this.releaseKey = releaseKey;
        }
    }
}
