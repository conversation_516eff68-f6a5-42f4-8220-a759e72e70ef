package cn.huolala.arch.hermes.compatible.autoconfigure;

import cn.huolala.arch.hermes.api.config.ApplicationConfig;
import cn.huolala.arch.hermes.common.support.Prioritized;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.tech.meta.system.SystemUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static cn.huolala.arch.hermes.config.spring.SpringConstants.CONFIG_PREFIX;

public class ApplicationProcessor extends AbstractConfigProcessor {

    private static final List<String> APPLICATION_NAMES = Arrays.asList("hll.app.id", "app.id", "lala.soa.application.name", "spring.application.name");
    private static final List<String> ENV_NAMES = Arrays.asList("hll.env", "lala.soa.application.environment", "env");
    private static final String APP_PREFIX = CONFIG_PREFIX + ".application.";

    @Override
    public void doProcess(Map<String, Object> source) {
        ApplicationConfig application = configProperties.getApplication();
        findFirstMatch(APPLICATION_NAMES).ifPresent(name -> source.put(APP_PREFIX + "name", name));
        findFirstMatch(ENV_NAMES).ifPresent(env -> source.put(APP_PREFIX + "environment", env));

        if (application == null || StringUtils.isBlank(application.getReleaseVersion())) {
            source.put(APP_PREFIX + "release-version", SystemUtil.getHllEnvInfo().getReleaseVersion());
        }

        Optional.ofNullable(SystemUtil.getHllEnvInfo().getZone())
                .filter(StringUtils::isNotBlank)
                .ifPresent(zone -> source.put(APP_PREFIX + "zone", zone));

        Optional.ofNullable(SystemUtil.getHllEnvInfo().getGroup())
                .filter(StringUtils::isNotBlank)
                .ifPresent(group -> source.put(APP_PREFIX + "group", group));
    }

    @Override
    public int getPriority() {
        return Prioritized.MAX_PRIORITY;
    }
}
