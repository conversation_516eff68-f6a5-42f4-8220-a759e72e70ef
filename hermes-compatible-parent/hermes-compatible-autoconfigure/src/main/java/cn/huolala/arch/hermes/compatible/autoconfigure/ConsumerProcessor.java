package cn.huolala.arch.hermes.compatible.autoconfigure;

import static cn.huolala.arch.hermes.config.spring.SpringConstants.CONFIG_PREFIX;

import java.util.Map;

import cn.huolala.arch.hermes.api.config.ConsumerConfig;
import cn.huolala.arch.hermes.common.support.Prioritized;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.bind.BindContext;
import org.springframework.boot.context.properties.bind.BindHandler;
import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.context.properties.source.ConfigurationPropertyName;

public class ConsumerProcessor extends AbstractConfigProcessor {

    private static final Logger logger = LoggerFactory.getLogger(ConsumerProcessor.class);

    private static final String CONSUMER_PREFIX = CONFIG_PREFIX + ".consumer";

    @Override
    protected void doProcess(Map<String, Object> source) {
        ConsumerConfig consumer = configProperties.getConsumer();
        ConsumerTimeoutProperties timeoutProperties = Binder.get(environment).bindOrCreate(CONSUMER_PREFIX,
                Bindable.of(ConsumerTimeoutProperties.class), new BindHandler() {
                    @Override
                    public Object onFailure(ConfigurationPropertyName name, Bindable<?> t, BindContext context, Exception e) {
                        logger.debug("Application soa timeout config composite failed. config name：{}, failed tip：{}", name, e.getMessage());
                        return null;
                    }
                });
        timeoutProperties = timeoutProperties != null ? timeoutProperties : new ConsumerTimeoutProperties();

        if (consumer == null || consumer.getConnectionTimeout() == null) {
            source.put(CONSUMER_PREFIX + ".connectionTimeout", timeoutProperties.getConnectionTimeoutMillis());
        }
        if (consumer == null || consumer.getTimeout() == null) {
            source.put(CONSUMER_PREFIX + ".timeout", timeoutProperties.getReadTimeoutMillis());
        }
    }

    @Override
    public int getPriority() {
        return Prioritized.MAX_PRIORITY + 400;
    }

    private static class ConsumerTimeoutProperties {

        private Integer connectionTimeoutMillis;

        private Integer readTimeoutMillis;

        public Integer getConnectionTimeoutMillis() {
            return connectionTimeoutMillis;
        }

        public void setConnectionTimeoutMillis(Integer connectionTimeoutMillis) {
            this.connectionTimeoutMillis = connectionTimeoutMillis;
        }

        public Integer getReadTimeoutMillis() {
            return readTimeoutMillis;
        }

        public void setReadTimeoutMillis(Integer readTimeoutMillis) {
            this.readTimeoutMillis = readTimeoutMillis;
        }
    }
}
