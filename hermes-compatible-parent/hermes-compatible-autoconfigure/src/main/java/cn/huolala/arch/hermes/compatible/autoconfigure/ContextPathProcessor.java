package cn.huolala.arch.hermes.compatible.autoconfigure;

import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.support.Prioritized;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.bind.BindContext;
import org.springframework.boot.context.properties.bind.BindHandler;
import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.context.properties.source.ConfigurationPropertyName;

import java.util.Map;

import static cn.huolala.arch.hermes.common.constants.Constants.CONTEXT_PATH;

public class ContextPathProcessor extends AbstractConfigProcessor {

    private static final Logger logger = LoggerFactory.getLogger(ContextPathProcessor.class);

    private static final String SERVER_SERVLET_PREFIX = "server.servlet";


    @Override
    protected void doProcess(Map<String, Object> source) {
        ContextPathProperties contextPathProperties = Binder.get(environment).bindOrCreate(SERVER_SERVLET_PREFIX,
                Bindable.of(ContextPathProperties.class), new BindHandler() {
                    @Override
                    public Object onFailure(ConfigurationPropertyName name, Bindable<?> t, BindContext context, Exception e) {
                        logger.debug("Application context path config composite failed. config name：{}, failed tip：{}", name, e.getMessage());
                        return null;
                    }
                });
        contextPathProperties = contextPathProperties != null ? contextPathProperties : new ContextPathProperties();
        ApplicationContext.addAttribute(CONTEXT_PATH, contextPathProperties.getContextPath());
    }

    @Override
    public int getPriority() {
        return Prioritized.MAX_PRIORITY + 1000;
    }

    private static class ContextPathProperties {
        private String contextPath;

        public String getContextPath() {
            return contextPath;
        }

        public void setContextPath(String contextPath) {
            this.contextPath = contextPath;
        }
    }
}
