package cn.huolala.arch.hermes.compatible.autoconfigure;

import static cn.huolala.arch.hermes.common.constants.Constants.HERMES_VERSION_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.LOCALHOST_VALUE;
import static cn.huolala.arch.hermes.common.constants.SpiConstants.DISCOVERY_DEFAULT;
import static cn.huolala.arch.hermes.config.spring.SpringConstants.CONFIG_PREFIX;
import static java.util.Optional.ofNullable;

import java.util.Map;

import cn.huolala.arch.hermes.api.config.ConsumerConfig;
import cn.huolala.arch.hermes.api.config.DiscoveryConfig;
import cn.huolala.arch.hermes.api.config.ProviderConfig;
import cn.huolala.arch.hermes.common.support.Prioritized;
import cn.huolala.arch.hermes.common.util.StringUtils;

/**
 *  Discovery compatible Processor
 */
public class DiscoveryProcessor extends AbstractConfigProcessor {

    private static final String PROVIDER_DISCOVERY_PREFIX = CONFIG_PREFIX + ".provider.discoveries[0].";
    private static final String CONSUMER_DISCOVERY_PREFIX = CONFIG_PREFIX + ".consumer.discoveries[0].";
    private static final String GLOBAL_DISCOVERY_PREFIX = CONFIG_PREFIX + ".discovery.";
    private static final String REGISTER_ENABLED_KEY = "hll.service-registry.enabled";
    private static final String CONSUL_HOST_KEY = "hll.consul.host";
    private static final String CONSUL_PORT_KEY = "hll.consul.port";
    private static final String CONSUL_TOKEN_KEY = "hll.consul.token";

    @Override
    public void doProcess(Map<String, Object> source) {
        Boolean registerEnable = environment.getProperty(REGISTER_ENABLED_KEY, Boolean.class);
        String host = environment.getProperty(CONSUL_HOST_KEY, String.class);
        Integer port = environment.getProperty(CONSUL_PORT_KEY, Integer.class);
        String token = environment.getProperty(CONSUL_TOKEN_KEY, String.class);

        this.globalProcess(source, host, port, token, registerEnable);

        this.providerProcess(source, host, port, token, registerEnable);

        this.consumerProcess(source, host, port, token);

        // Hermes version tag
        source.put(HERMES_VERSION_KEY, environment.getProperty(HERMES_VERSION_KEY, ""));
        
    }
    
    private void globalProcess(Map<String, Object> source, String host, Integer port, String token,
            Boolean registerEnable) {
        DiscoveryConfig discovery = ofNullable(configProperties.getDiscovery()).orElse(new DiscoveryConfig());
        if (isDefaultProtocol(discovery)) {
            source.put(GLOBAL_DISCOVERY_PREFIX + "register", determineValue(registerEnable, discovery.getRegister(), true));
            source.put(GLOBAL_DISCOVERY_PREFIX + "address", determineAddress(host, discovery.getAddress()));
            source.put(GLOBAL_DISCOVERY_PREFIX + "port", determinePort(port, discovery.getPort()));
            source.put(GLOBAL_DISCOVERY_PREFIX + "token", determineToken(token, discovery.getToken()));
        }
    }
    
    private void consumerProcess(Map<String, Object> source, String host, Integer port, String token) {
        ConsumerConfig consumer = configProperties.getConsumer();
        DiscoveryConfig discovery = ofNullable(consumer).map(ConsumerConfig::getDiscoveries)
                .map(configs -> configs.iterator().next()).orElse(null);
        if (discovery != null && isDefaultProtocol(discovery)) {
            source.put(CONSUMER_DISCOVERY_PREFIX + "address", determineAddress(host, discovery.getAddress()));
            source.put(CONSUMER_DISCOVERY_PREFIX + "port", determinePort(port, discovery.getPort()));
            source.put(CONSUMER_DISCOVERY_PREFIX + "token", determineToken(token, discovery.getToken()));
        }
    }
    
    private void providerProcess(Map<String, Object> source, String host, Integer port, String token,
            Boolean registerEnable) {
        ProviderConfig provider = configProperties.getProvider();
        DiscoveryConfig discovery = ofNullable(provider).map(ProviderConfig::getDiscoveries)
                .map(configs -> configs.iterator().next()).orElse(null);
        if (discovery != null && isDefaultProtocol(discovery)) {
            source.put(PROVIDER_DISCOVERY_PREFIX + "register", determineValue(registerEnable, discovery.getRegister(), true));
            source.put(PROVIDER_DISCOVERY_PREFIX + "address", determineAddress(host, discovery.getAddress()));
            source.put(PROVIDER_DISCOVERY_PREFIX + "port", determinePort(port, discovery.getPort()));
            source.put(PROVIDER_DISCOVERY_PREFIX + "token", determineToken(token, discovery.getToken()));
        }
    }
    
    private Object determineAddress(Object firstOption, Object secondOption) {
        return this.determineValue(firstOption, secondOption, LOCALHOST_VALUE);
    }
    
    private Object determinePort(Object firstOption, Object secondOption) {
        return this.determineValue(firstOption, secondOption, "8500");
    }
    
    private Object determineToken(Object firstOption, Object secondOption) {
        return this.determineValue(firstOption, secondOption, "");
    }
    
    private Object determineValue(Object firstOption, Object secondOption, Object defaultValue) {
        return ofNullable(ofNullable(firstOption).orElse(secondOption)).orElse(defaultValue);
    }
    
    private boolean isDefaultProtocol(DiscoveryConfig config) {
        return StringUtils.isBlank(config.getProtocol()) || config.getProtocol().equals(DISCOVERY_DEFAULT);
    }
    
    @Override
    public int getPriority() {
        return Prioritized.MAX_PRIORITY + 200;
    }
}
