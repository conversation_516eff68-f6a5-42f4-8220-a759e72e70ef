package cn.huolala.arch.hermes.compatible.autoconfigure;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.huolala.arch.hermes.common.config.Configuration;
import cn.huolala.arch.hermes.common.config.GlobalConfiguration;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.support.Prioritized;

public class EnvironmentProcessor extends AbstractConfigProcessor {
    
    private static final String SERVER_PORT_KEY = "server.port";
    
    @Override
    protected void doProcess(Map<String, Object> source) {
        String serverPort = environment.getProperty(SERVER_PORT_KEY, "8080");
        source.put(SERVER_PORT_KEY, serverPort);
        
        Map<String, String> properties = source.keySet().stream().filter(k -> !Objects.isNull(source.get(k)))
                .collect(Collectors.toMap(k -> k, k -> String.valueOf(source.get(k))));
        
        // Add composite config into hermes environment
        GlobalConfiguration globalConfiguration = (GlobalConfiguration) ApplicationContext.getEnvironment()
                .getConfiguration();
        globalConfiguration.addConfiguration(new CompositeConfiguration(properties));
    }
    
    @Override
    public int getPriority() {
        return Prioritized.MIN_PRIORITY;
    }
    
    protected static class CompositeConfiguration implements Configuration {
        private Map<String, String> store = new LinkedHashMap<>();

        public CompositeConfiguration(Map<String, String> store) {
            this.store = store;
        }

        public void addProperty(String key, String value) {
            store.put(key, value);
        }

        public void addProperties(Map<String, String> properties) {
            if (properties != null) {
                this.store.putAll(properties);
            }
        }

        public void clear() {
            this.store.clear();
        }

        @Override
        public Object getInternalProperty(String key) {
            return store.get(key);
        }
    }
}
