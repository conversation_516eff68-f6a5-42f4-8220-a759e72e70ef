package cn.huolala.arch.hermes.compatible.autoconfigure;

import static cn.huolala.arch.hermes.common.constants.Constants.DEFAULT_ENV;
import static cn.huolala.arch.hermes.common.constants.Constants.HOSTPORTS_ENABLED_KEY;
import static cn.huolala.arch.hermes.config.spring.SpringConstants.CONFIG_PREFIX;
import static java.util.Optional.ofNullable;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import cn.huolala.tech.meta.system.LocalUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.bind.BindContext;
import org.springframework.boot.context.properties.bind.BindHandler;
import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.context.properties.source.ConfigurationPropertyName;
import org.springframework.util.CollectionUtils;

import cn.huolala.arch.hermes.api.config.HostPortsConfig.HostPort;
import cn.huolala.arch.hermes.common.support.Prioritized;

public class HostPortProcessor extends AbstractConfigProcessor {

    private static final Logger logger = LoggerFactory.getLogger(HostPortProcessor.class);
    private static final String HOSTPORTS_PREFIX = CONFIG_PREFIX + ".host-ports.";
    public static final Integer DEFAULT_NON_PORT = -1;

    @Override
    protected void doProcess(Map<String, Object> source) {
        ApplicationProperties appProps = Binder.get(environment).bindOrCreate(CONFIG_PREFIX,
                Bindable.of(ApplicationProperties.class), new BindHandler() {
                    @Override
                    public Object onFailure(ConfigurationPropertyName name, Bindable<?> t, BindContext context, Exception e) {
                        logger.debug("Direct connect config composite failed. config name：{}, failed tip：{}", name, e.getMessage());
                        return null;
                    }
                });
        appProps = appProps != null ? appProps : new ApplicationProperties();

        String env = environment.getProperty(CONFIG_PREFIX + ".application.environment", DEFAULT_ENV);
        boolean appConfigEnable = appProps.getConsumer().isApplicationConfigEnable();
        boolean hostPortsEnable = false;

        if (appConfigEnable || env.equalsIgnoreCase(DEFAULT_ENV) || LocalUtils.isLocal) {
            final Map<String, HostPort> hostPorts = configProperties.getHostPorts();

            // 兼容老的配置方式
            appProps.getApplication().forEach((appId, hostPort) -> {
                if (CollectionUtils.isEmpty(hostPorts) || !hostPorts.containsKey(appId)) {
                    source.put(HOSTPORTS_PREFIX + appId + ".host", hostPort.getAppHost());
                    source.put(HOSTPORTS_PREFIX + appId + ".port", ofNullable(hostPort.getAppPort()).orElse(DEFAULT_NON_PORT));
                } 
            });
            if (!CollectionUtils.isEmpty(hostPorts)) {
                hostPorts.forEach((appId, hostPort) -> {
                    source.put(HOSTPORTS_PREFIX + appId + ".port", ofNullable(hostPort.getPort()).orElse(DEFAULT_NON_PORT));
                });
            }
            hostPortsEnable = true;
        }
        source.put(HOSTPORTS_ENABLED_KEY, hostPortsEnable);
    }

    @Override
    public int getPriority() {
        return Prioritized.MAX_PRIORITY + 500;
    }

    protected static class ApplicationProperties {
        private ConsumerProperties consumer = new ConsumerProperties();

        private Map<String, HostPortProperties> application = new ConcurrentHashMap<>();

        public Map<String, HostPortProperties> getApplication() {
            return application;
        }

        public void setApplication(Map<String, HostPortProperties> application) {
            this.application = application;
        }

        public ConsumerProperties getConsumer() {
            return consumer;
        }

        public void setConsumer(ConsumerProperties consumer) {
            this.consumer = consumer;
        }

        protected static class HostPortProperties {
            private String appHost;

            private Integer appPort;

            public String getAppHost() {
                return appHost;
            }

            public void setAppHost(String appHost) {
                this.appHost = appHost;
            }

            public Integer getAppPort() {
                return appPort;
            }

            public void setAppPort(Integer appPort) {
                this.appPort = appPort;
            }
        }

        protected static class ConsumerProperties {
            private boolean applicationConfigEnable;

            public void setApplicationConfigEnable(boolean applicationConfigEnable) {
                this.applicationConfigEnable = applicationConfigEnable;
            }

            public boolean isApplicationConfigEnable() {
                return applicationConfigEnable;
            }
        }
    }
}
