package cn.huolala.arch.hermes.compatible.autoconfigure;

import static cn.huolala.arch.hermes.config.spring.SpringConstants.CONFIG_PREFIX;
import static java.util.Optional.ofNullable;

import java.util.Map;

import cn.huolala.arch.hermes.common.support.Prioritized;

public class ProviderProcessor extends AbstractConfigProcessor {
    private static final String PRD = "prd";

    @Override
    protected void doProcess(Map<String, Object> source) {
        Boolean accessLog = ofNullable(configProperties.getProvider()).map(p -> p.getAccesslog()).orElse(null);
        String env = source.getOrDefault(CONFIG_PREFIX + ".application.environment", "").toString();
        if (accessLog == null && !env.equalsIgnoreCase(PRD)) {
            source.put(CONFIG_PREFIX + ".provider.accesslog", true);
        }
    }
    
    @Override
    public int getPriority() {
        return Prioritized.MAX_PRIORITY + 800;
    }
}
