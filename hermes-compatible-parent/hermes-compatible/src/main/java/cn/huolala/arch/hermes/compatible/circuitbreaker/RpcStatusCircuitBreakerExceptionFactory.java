package cn.huolala.arch.hermes.compatible.circuitbreaker;

import cn.huolala.arch.hermes.compatible.protocol.exception.RpcStatusException;
import cn.huolala.arch.hermes.filter.circuitbreaker.CircuitBreakerExceptionFactory;

import org.apache.hc.core5.http.HttpStatus;

import java.util.function.Predicate;

public class RpcStatusCircuitBreakerExceptionFactory implements CircuitBreakerExceptionFactory {
    public static final String NAME = "rpcstatusexception";

    @Override
    public Predicate<Throwable> recordExceptionPredicate() {
        return e -> {
            if (RpcStatusException.class.isAssignableFrom(e.getClass())) {
                RpcStatusException rpcStatusException = (RpcStatusException) e;
                return rpcStatusException.getCode() >= HttpStatus.SC_INTERNAL_SERVER_ERROR;
            }
            return false;
        };
    }
}
