package cn.huolala.arch.hermes.compatible.config;

import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.compatible.config.support.ReferenceBeanBuilder;
import cn.huolala.arch.hermes.config.spring.ReferenceBean;
import cn.huolala.arch.hermes.config.spring.support.Compatible;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.AnnotationAttributes;

/**
 * Default Compatible impl
 */
@Activate
public class JsonRpcCompatible implements Compatible {

    @Override
    public ReferenceBean<?> buildReferenceBean(String referenceBeanName, ApplicationContext applicationContext, AnnotationAttributes attributes, Class<?> referencedType) throws Exception {
        return ReferenceBeanBuilder.newBuilder(attributes, applicationContext, referencedType).beanName(referenceBeanName).build();
    }

}
