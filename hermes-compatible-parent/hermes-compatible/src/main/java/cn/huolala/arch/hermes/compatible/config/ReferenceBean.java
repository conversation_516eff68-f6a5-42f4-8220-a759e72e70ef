package cn.huolala.arch.hermes.compatible.config;

import cn.huolala.arch.hermes.api.fallback.DefaultFallbackFactory;
import cn.huolala.arch.hermes.cluster.fallback.support.GlobalFallback;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.lalaframework.soa.annotation.Fallback;
import cn.lalaframework.soa.annotation.FallbackFactory;
import org.springframework.aop.framework.ProxyFactory;
import org.springframework.beans.factory.FactoryBean;

import java.lang.reflect.Method;
import java.util.Optional;

import static cn.huolala.arch.hermes.protocol.exception.RpcException.FALLBACK;

@ApiAudience.Private
public class ReferenceBean<T> extends cn.huolala.arch.hermes.config.spring.ReferenceBean<T> implements FactoryBean<T> {

    private static final Logger logger = LoggerFactory.getLogger(ReferenceBean.class);

    private T lazyProxy;

    @Override
    public void afterPropertiesSet() throws Exception {
        super.afterPropertiesSet();
        doAfterPropertiesSet();
    }


    protected void doAfterPropertiesSet() {
        if (fallbackFactory == null) {
            String[] fallBackBeanNames = applicationContext.getBeanNamesForAnnotation(Fallback.class);
            for (String fallBackBeanName : fallBackBeanNames) {
                Object bean = applicationContext.getBean(fallBackBeanName);
                Optional<Class<?>> candidateClass = lookupRightCandidate(bean.getClass());
                if (candidateClass.isPresent()) {
                    Class<?> fallBackClass = candidateClass.get();

                    Method method = ReflectUtils.getMethodByNameAndReturnType(fallBackClass, "create", interfaceClass);
                    if (Optional.ofNullable(method).isPresent()) {
                        try {
                            fallbackFactory = (cn.huolala.arch.hermes.api.fallback.FallbackFactory<T>) context -> ((FallbackFactory<T>) bean).create();
                        } catch (Throwable e) {
                            logger.warn("Failed to invoke fallback factory [" + fallBackClass.getName() + "] create method ");
                            throw new RpcException(FALLBACK, e);
                        }
                        logger.info(interfaceClass.getName() + " refer for fallback factory [" + fallBackClass.getName() + "]");
                        break;
                    }
                }
            }
            if (fallbackFactory == null) {
                getGlobalFallbackFactory().ifPresent(fallbackFactory -> this.fallbackFactory = fallbackFactory);
            }
        }
    }

    private Optional<cn.huolala.arch.hermes.api.fallback.FallbackFactory<?>> getGlobalFallbackFactory() {
        return applicationContext.getBeansOfType(GlobalFallback.class).values().stream().map(v -> (Object) v).findFirst()
                .map(DefaultFallbackFactory::new);
    }


    @Override
    public T get() {

        if (!isSingleton()) {
            return createLazyProxy();
        }

        if (lazyProxy == null) {
            lazyProxy = createLazyProxy();
        }

        return lazyProxy;
    }

    @Override
    public T getObject() {
        return get();
    }

    @Override
    public Class<?> getObjectType() {
        return getInterfaceClass();
    }


    private Optional<Class<?>> lookupRightCandidate(Class<?> beanClass) {
        if (beanClass.getName().equals(Object.class.getName())) {
            return Optional.empty();
        }
        Fallback fallback = beanClass.getAnnotation(Fallback.class);
        if (fallback != null) {
            if (!beanClass.isInterface() && FallbackFactory.class.isAssignableFrom(beanClass)) {
                return Optional.of(beanClass);
            }
            return Optional.empty();
        }
        return lookupRightCandidate(beanClass.getSuperclass());
    }

    private T createLazyProxy() {
        ProxyFactory proxyFactory = new ProxyFactory();
        //set proxy interfaces
        proxyFactory.addInterface(this.interfaceClass);
        proxyFactory.setTarget(super.get());
        return (T) proxyFactory.getProxy();
    }

}
