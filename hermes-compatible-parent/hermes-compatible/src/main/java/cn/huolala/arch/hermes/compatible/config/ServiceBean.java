package cn.huolala.arch.hermes.compatible.config;

import cn.huolala.arch.hermes.api.config.ProviderConfig;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.compatible.protocol.invoker.JsonRpcProviderContext;
import cn.huolala.arch.hermes.protocol.jsonrpc.JsonRpcProtocol;
import cn.lalaframework.soa.jsonrpc.ErrorResolverProvider;
import cn.lalaframework.soa.jsonrpc.JsonRpcInterceptorProvider;
import cn.lalaframework.soa.jsonrpc.helpers.ObjectMapperProvider;
import com.googlecode.jsonrpc4j.JsonRpcServerAdapter;
import org.springframework.web.HttpRequestHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class ServiceBean<T> extends cn.huolala.arch.hermes.config.spring.ServiceBean<T> implements HttpRequestHandler {

    private JsonRpcServerAdapter jsonRpcServerAdapter;

    @Override
    public void export() {
        super.export();
        Object proxiedImpl = JsonRpcProtocol.SKELETON_MAP.get(getServiceKey());
        if (proxiedImpl == null) {
            throw new IllegalStateException("Service " + getServiceKey() + "should have already been export in skeleton repository, "
                    + "but failed to find it.");
        }
        ProviderConfig providerConfig = ApplicationContext.getConfigManager().getProvider().get();
        jsonRpcServerAdapter = new JsonRpcServerAdapter(ObjectMapperProvider.getInstance(), proxiedImpl, getInterfaceClass());
        jsonRpcServerAdapter.setErrorResolver(ErrorResolverProvider.getInstance(providerConfig.getErrorResolver()));
        jsonRpcServerAdapter.setInterceptorList(JsonRpcInterceptorProvider.getInstance(providerConfig.getInterceptors()));
    }


    @Override
    public void handleRequest(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws IOException {
        JsonRpcProviderContext context = JsonRpcProviderContext.getContext();
        context.setRequest(httpServletRequest);
        context.setResponse(httpServletResponse);
        try {
            jsonRpcServerAdapter.handle(httpServletRequest, httpServletResponse);
        } finally {
            JsonRpcProviderContext.removeContext();
        }
    }

}
