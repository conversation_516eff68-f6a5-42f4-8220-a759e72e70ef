package cn.huolala.arch.hermes.compatible.config.beans;

import cn.huolala.arch.hermes.api.config.MethodConfig;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.compatible.config.generic.ReferenceBean;
import cn.huolala.arch.hermes.compatible.config.support.BeanDefinitionSupport;
import cn.huolala.arch.hermes.compatible.config.support.JsonRpcAttributeMetadata;
import cn.huolala.arch.hermes.compatible.protocol.IJsonRpcGenericService;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.huolala.arch.hermes.common.constants.Constants.JSONRPC_PROTOCOL;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC_FILTER;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC_GENERIC_CONSUMER_FILTER;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.PATH_PREFIX;

public class JsonRpcGenericReferenceAnnotationPostProcessor implements BeanDefinitionRegistryPostProcessor {

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {

        //register generic referenceBean
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.rootBeanDefinition(ReferenceBean.class);

        String beanName = IJsonRpcGenericService.class.getName();
        String applicationName = IJsonRpcGenericService.class.getSimpleName();

        builder.addPropertyValue("interfaceClass", IJsonRpcGenericService.class);
        builder.addPropertyValue("application", applicationName);
        builder.addPropertyValue("path", PATH_PREFIX);
        builder.addPropertyValue("protocolName", JSONRPC_PROTOCOL);

        Method[] methods = IJsonRpcGenericService.class.getMethods();
        List<MethodConfig> methodConfigs = new ArrayList<>(methods.length);
        for (Method method : methods) {
            JsonRpcAttributeMetadata mt = BeanDefinitionSupport.attributeMetadata(applicationName, PATH_PREFIX, method);
            MethodConfig methodConfig = new MethodConfig(method.getName(), ReflectUtils.getDesc(method.getParameterTypes()), mt.getFallbackFactory(), mt.getReadTimeoutMillis(),
                    mt.getConnectionTimeoutMillis(), mt.getMethodPath(), mt.getCommandKey(), mt.getDescription(), null, mt.isOneway(), mt.getParamNames(), mt.getAttributes());
            methodConfig.setRawPath(mt.getRawMethodPath());
            methodConfigs.add(methodConfig);
        }
        builder.addPropertyValue("methods", methodConfigs);

        Map<String, String> parameters = new HashMap<>();

        //enable jsonrpc consumer filter
        parameters.put(JSONRPC_FILTER, JSONRPC_GENERIC_CONSUMER_FILTER);
        builder.addPropertyValue("parameters", parameters);

        registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
    }


    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {

    }
}
