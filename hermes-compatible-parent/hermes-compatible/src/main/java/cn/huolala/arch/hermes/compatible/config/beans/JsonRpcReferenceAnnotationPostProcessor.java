package cn.huolala.arch.hermes.compatible.config.beans;

import cn.huolala.arch.hermes.api.config.MethodConfig;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.common.util.URLUtils;
import cn.huolala.arch.hermes.compatible.config.ReferenceBean;
import cn.huolala.arch.hermes.compatible.config.support.BeanDefinitionScanner;
import cn.huolala.arch.hermes.compatible.config.support.BeanDefinitionSupport;
import cn.huolala.arch.hermes.compatible.config.support.JsonRpcAttributeMetadata;
import cn.huolala.arch.hermes.config.spring.beans.AbstractServiceAnnotationProcessor;
import cn.lalaframework.soa.annotation.SOAService;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.BeanDefinitionHolder;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanNameGenerator;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.huolala.arch.hermes.common.constants.Constants.JSONRPC_PROTOCOL;
import static cn.huolala.arch.hermes.compatible.config.support.BeanDefinitionSupport.p2pConfig;
import static cn.huolala.arch.hermes.compatible.config.support.BeanDefinitionSupport.p2pEnabled;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.*;
import static cn.huolala.arch.hermes.config.spring.support.AnnotationUtils.getAttribute;
import static cn.huolala.arch.hermes.config.spring.support.AnnotationUtils.getAttributes;

public class JsonRpcReferenceAnnotationPostProcessor extends AbstractServiceAnnotationProcessor {

    private final Set<String> classToScan;

    private static final String GROUP = "clientProxy";

    public JsonRpcReferenceAnnotationPostProcessor(Set<String> packagesToScan, Set<String> classToScan) {
        super(packagesToScan, SOAService.class, ReferenceBean.class);
        this.classToScan = classToScan;
    }


    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        this.registerServiceBeans(packagesToScan, registry);
    }

    @Override
    protected void registerServiceBeans(Set<String> packagesToScan, BeanDefinitionRegistry registry) {
        BeanDefinitionScanner scanner = new BeanDefinitionScanner(registry, environment, resourceLoader);
        BeanNameGenerator beanNameGenerator = resolveBeanNameGenerator(registry);
        scanner.setBeanNameGenerator(beanNameGenerator);
        scanner.addIncludeFilter(new AnnotationTypeFilter(this.annotationType));

        Set<BeanDefinition> scanBeanDefinition = new HashSet<>();
        for (String packageToScan : packagesToScan) {
            scanBeanDefinition.addAll(scanner.findCandidateComponents(packageToScan));
        }
        Set<Class<?>> candidateClass = scanBeanDefinition.stream().map(this::resolveClass).collect(Collectors.toSet());
        candidateClass.addAll(classToScan.stream().map(String::trim).filter(org.springframework.util.StringUtils::hasText).map(v -> resolveClass(BeanDefinitionBuilder.genericBeanDefinition(v).getBeanDefinition())).collect(Collectors.toSet()));

        Set<BeanDefinitionHolder> beanDefinitionHolders = candidateClass.stream().map(v -> {
            BeanDefinition beanDefinition = BeanDefinitionBuilder.genericBeanDefinition(v).getBeanDefinition();
            String beanName = beanNameGenerator.generateBeanName(beanDefinition, registry);
            return new BeanDefinitionHolder(beanDefinition, beanName);

        }).collect(Collectors.toSet());

        if (!CollectionUtils.isEmpty(beanDefinitionHolders)) {
            for (BeanDefinitionHolder beanDefinitionHolder : beanDefinitionHolders) {
                registerServiceBean(beanDefinitionHolder, registry, scanner);
            }
            logger.info(beanDefinitionHolders.size() + " annotated @SOAService Components { "
                    + beanDefinitionHolders + " } were scanned under package[" + packagesToScan + "]");
        } else {
            logger.warn("No Spring Bean annotating @SOAService was found under package[" + packagesToScan + "]");
        }
    }


    @Override
    public AnnotationAttributes annotationAttributes(Annotation annotation) {
        return AnnotationAttributes.fromMap(getAttributes(annotation, environment, true));
    }

    @Override
    protected Class<?> resolveServiceInterfaceClass(AnnotationAttributes serviceAttributes, Class<?> defaultInterfaceClass) {
        Assert.isTrue(defaultInterfaceClass.isInterface(), "The class [" + defaultInterfaceClass.getName() + "] must be an interface");
        SOAService soaService = defaultInterfaceClass.getAnnotation(SOAService.class);
        Assert.notNull(soaService, "The class [" + defaultInterfaceClass.getName() + "]@SOAService interfaceClass() must be present");
        return defaultInterfaceClass;
    }


    @Override
    protected String generateBeanName(AnnotationAttributes serviceAttributes, Class<?> interfaceClass) {
        return interfaceClass.getName() + "-" + GROUP;
    }


    @Override
    protected AbstractBeanDefinition buildServiceBeanDefinition(Annotation service, AnnotationAttributes serviceAttributes, Class<?> interfaceClass, String beanName) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.rootBeanDefinition(serviceBeanClass);

        String applicationName = getAttribute(serviceAttributes, APP_ID, true);
        String servicePath = getAttribute(serviceAttributes, VALUE, true);

        builder.addPropertyValue("interfaceClass", interfaceClass);
        builder.addPropertyValue("application", applicationName);

        String contextId = getAttribute(serviceAttributes, CONTEXT_ID);
        if (StringUtils.isNotEmpty(contextId)) {
            servicePath = StringUtils.addSuffixIfNot(StringUtils.addPrefixIfNot(contextId, PATH_PREFIX), PATH_PREFIX) + StringUtils.removeStart(servicePath, PATH_PREFIX);
        }
        builder.addPropertyValue(SERVICE_PATH, servicePath);
        if (p2pEnabled()) {
            String appHost = getAttribute(serviceAttributes, APP_HOST);
            if (StringUtils.isNotEmpty(appHost)) {
                String appPort = getAttribute(serviceAttributes, APP_PORT);
                builder.addPropertyValue("url", URLUtils.newURLBuilder(JSONRPC_PROTOCOL, appHost, StringUtils.isEmpty(appPort) ? null : Integer.valueOf(appPort)).build().toFullString());
            } else {
                p2pConfig(applicationName).ifPresent(hostPort ->
                        builder.addPropertyValue("url", URLUtils.newURLBuilder(JSONRPC_PROTOCOL, hostPort.getHost(), hostPort.getPort() == null ? null : hostPort.getPort()).build().toFullString()));
            }
        }
        builder.addPropertyValue("protocolName", getAttribute(serviceAttributes, "protocolName", JSONRPC_PROTOCOL));

        Method[] methods = interfaceClass.getMethods();
        List<MethodConfig> methodConfigs = new ArrayList<>(methods.length);
        for (Method method : methods) {
            JsonRpcAttributeMetadata mt = BeanDefinitionSupport.attributeMetadata(applicationName, servicePath, method);
            MethodConfig methodConfig = new MethodConfig(method.getName(), ReflectUtils.getDesc(method.getParameterTypes()), mt.getFallbackFactory(), mt.getReadTimeoutMillis(),
                    mt.getConnectionTimeoutMillis(), mt.getMethodPath(), mt.getCommandKey(), mt.getDescription(), null, mt.isOneway(), mt.getParamNames(), mt.getAttributes());
            methodConfig.setRawPath(mt.getRawMethodPath());
            methodConfigs.add(methodConfig);
        }
        builder.addPropertyValue("methods", methodConfigs);
        builder.addPropertyValue("referenceInstance", GROUP);
        serviceAttributes.put(JSONRPC_FILTER, JSONRPC_CONSUMER_FILTER);
        builder.addPropertyValue("parameters", serviceAttributes);
        return builder.getBeanDefinition();
    }
}
