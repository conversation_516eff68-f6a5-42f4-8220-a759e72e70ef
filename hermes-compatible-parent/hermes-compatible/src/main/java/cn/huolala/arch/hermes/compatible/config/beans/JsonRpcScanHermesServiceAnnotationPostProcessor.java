package cn.huolala.arch.hermes.compatible.config.beans;

import cn.huolala.arch.hermes.api.annotation.HermesService;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.config.ServiceBean;
import cn.huolala.arch.hermes.config.spring.support.AnnotationUtils;
import org.springframework.core.annotation.AnnotationAttributes;

import java.lang.annotation.Annotation;
import java.util.Set;
import java.util.stream.Stream;

import static cn.huolala.arch.hermes.common.util.StringUtils.toLowerCaseFirstOne;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.VALUE;
import static cn.huolala.arch.hermes.config.spring.support.AnnotationUtils.getAttribute;

public class JsonRpcScanHermesServiceAnnotationPostProcessor extends JsonRpcScanServiceAnnotationPostProcessor {

    public JsonRpcScanHermesServiceAnnotationPostProcessor(Set<String> packagesToScan) {
        this(packagesToScan, HermesService.class, ServiceBean.class);
    }

    public JsonRpcScanHermesServiceAnnotationPostProcessor(Set<String> packagesToScan, Class<? extends Annotation> annotationType, Class<?> serviceBeanClass) {
        super(packagesToScan, annotationType, serviceBeanClass);
    }

    @Override
    protected Class<?> resolveServiceInterfaceClass(AnnotationAttributes serviceAttributes, Class<?> beanClass) {
        return AnnotationUtils.resolveServiceInterfaceClass(serviceAttributes, beanClass);
    }

    @Override
    protected String generateBeanName(AnnotationAttributes serviceAttributes, Class<?> interfaceClass) {
        serviceAttributes.put("defaultPath", toLowerCaseFirstOne(interfaceClass.getSimpleName()));
        Stream.of("value", "path", "defaultPath")
                .map(name -> getAttribute(serviceAttributes, name, ""))
                .filter(StringUtils::isNotBlank)
                .findFirst().ifPresent(value -> serviceAttributes.put(VALUE, value));
        return super.generateBeanName(serviceAttributes, interfaceClass);
    }

}
