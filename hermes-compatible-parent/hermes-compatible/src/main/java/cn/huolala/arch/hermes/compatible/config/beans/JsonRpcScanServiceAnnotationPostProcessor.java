package cn.huolala.arch.hermes.compatible.config.beans;

import cn.huolala.arch.hermes.compatible.config.ServiceBean;
import cn.huolala.arch.hermes.config.spring.beans.AbstractServiceAnnotationProcessor;
import cn.huolala.arch.hermes.config.spring.support.AnnotationUtils;
import cn.lalaframework.soa.annotation.SOAService;
import cn.lalaframework.soa.annotation.spring.SOAServiceAutoImpl;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.util.Assert;

import java.lang.annotation.Annotation;
import java.util.Set;

import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC_FILTER;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC_PROVIDER_FILTER;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.PATH_PREFIX;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.VALUE;
import static org.springframework.util.ClassUtils.getAllInterfacesForClass;

public class JsonRpcScanServiceAnnotationPostProcessor extends AbstractServiceAnnotationProcessor {

    public JsonRpcScanServiceAnnotationPostProcessor(Set<String> packagesToScan) {
        this(packagesToScan, SOAServiceAutoImpl.class, ServiceBean.class);
    }

    public JsonRpcScanServiceAnnotationPostProcessor(Set<String> packagesToScan, Class<? extends Annotation> annotationType, Class<?> serviceBeanClass) {
        super(packagesToScan, annotationType, serviceBeanClass);
    }

    @Override
    protected Class<?> resolveServiceInterfaceClass(AnnotationAttributes serviceAttributes, Class<?> defaultInterfaceClass) {
        Class<?> interfaceClass = null;
        Class<?>[] allInterfaces = getAllInterfacesForClass(defaultInterfaceClass);
        for (Class<?> superInterface : allInterfaces) {
            SOAService soaService = superInterface.getAnnotation(SOAService.class);
            if (soaService != null) {
                interfaceClass = superInterface;
                serviceAttributes.putAll(AnnotationUtils.getAttributes(soaService, environment, true));
                break;
            }
        }
        Assert.notNull(interfaceClass, "@SOAService interfaceClass() must be present");
        Assert.isTrue(interfaceClass.isInterface(), "The @SOAService annotated type must be an interface");
        return interfaceClass;
    }


    @Override
    protected AbstractBeanDefinition buildServiceBeanDefinition(Annotation service, AnnotationAttributes serviceAttributes, Class<?> interfaceClass, String beanName) {
        AbstractBeanDefinition beanDefinition = super.buildServiceBeanDefinition(service, serviceAttributes, interfaceClass, beanName);
        serviceAttributes.put("group", beanName);
        //Enable jsonrpc Provider context Filter
        serviceAttributes.put(JSONRPC_FILTER, JSONRPC_PROVIDER_FILTER);
        beanDefinition.getPropertyValues().addPropertyValue("parameters", serviceAttributes);
        return beanDefinition;
    }

    @Override
    protected String generateBeanName(AnnotationAttributes serviceAttributes, Class<?> interfaceClass) {
        String servicePath = AnnotationUtils.getAttribute(serviceAttributes, VALUE, true);
        if (servicePath.startsWith(PATH_PREFIX)) {
            return servicePath;
        }
        return PATH_PREFIX.concat(servicePath);
    }
}
