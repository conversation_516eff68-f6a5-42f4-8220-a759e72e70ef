package cn.huolala.arch.hermes.compatible.config.beans;

import cn.huolala.arch.hermes.config.spring.support.BeanDefinitionScanner;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.AnnotatedGenericBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinitionHolder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanNameGenerator;
import org.springframework.util.CollectionUtils;

import java.util.Set;
import java.util.stream.Collectors;

public class JsonRpcSpringServiceAnnotationPostProcessor extends JsonRpcScanServiceAnnotationPostProcessor {

    public JsonRpcSpringServiceAnnotationPostProcessor(Set<String> packagesToScan) {
        super(packagesToScan);
    }

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        this.registerServiceBeans(packagesToScan, registry);
    }

    @Override
    protected void registerServiceBeans(Set<String> candidateClass, BeanDefinitionRegistry registry) {
        BeanDefinitionScanner scanner = new BeanDefinitionScanner(registry, environment, resourceLoader);
        BeanNameGenerator beanNameGenerator = resolveBeanNameGenerator(registry);
        scanner.setBeanNameGenerator(beanNameGenerator);

        Set<BeanDefinitionHolder> beanDefinitionHolders = candidateClass.stream().map(v -> {
            AnnotatedGenericBeanDefinition beanDefinition;
            try {
                beanDefinition = new AnnotatedGenericBeanDefinition(Class.forName(v));
            } catch (ClassNotFoundException e) {
                throw new RuntimeException(e);
            }
            String beanName = beanNameGenerator.generateBeanName(beanDefinition, registry);
            return new BeanDefinitionHolder(beanDefinition, beanName);
        }).collect(Collectors.toSet());

        if (!CollectionUtils.isEmpty(beanDefinitionHolders)) {
            for (BeanDefinitionHolder beanDefinitionHolder : beanDefinitionHolders) {
                registerServiceBean(beanDefinitionHolder, registry, scanner);
            }
            logger.info(beanDefinitionHolders.size() + " annotated " + annotationType + " Components { "
                    + beanDefinitionHolders + " } were scanned");
        } else {
            logger.warn("No Spring Bean annotating " + annotationType + " was found");
        }
    }
}
