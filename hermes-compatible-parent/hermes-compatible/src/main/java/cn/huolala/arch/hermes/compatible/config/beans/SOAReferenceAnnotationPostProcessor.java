package cn.huolala.arch.hermes.compatible.config.beans;

import cn.huolala.arch.hermes.api.config.MethodConfig;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.config.support.BeanDefinitionSupport;
import cn.huolala.arch.hermes.compatible.config.support.JsonRpcAttributeMetadata;
import cn.huolala.arch.hermes.config.spring.ReferenceBean;
import cn.huolala.arch.hermes.config.spring.beans.AbstractAnnotationBeanPostProcessor;
import cn.huolala.arch.hermes.config.spring.support.AnnotationUtils;
import cn.lalaframework.soa.annotation.SOAService;
import cn.lalaframework.soa.jsonrpc.spring.reference.ReferenceMethod;
import cn.lalaframework.soa.jsonrpc.spring.reference.SOAReference;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.env.Environment;
import org.springframework.util.Assert;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.huolala.arch.hermes.common.constants.Constants.JSONRPC_PROTOCOL;
import static cn.huolala.arch.hermes.compatible.config.support.BeanDefinitionSupport.configureP2PUrl;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.APP_ID;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.CONNECTION_TIMEOUT_MILLIS;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.CONTEXT_ID;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC_CONSUMER_FILTER;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC_FILTER;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.METHODS;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.ONE_WAY;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.PATH_PREFIX;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.READ_TIMEOUT_MILLIS;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.VALUE;
import static cn.huolala.arch.hermes.config.spring.support.AnnotationUtils.getAttribute;
import static cn.huolala.arch.hermes.config.spring.support.AnnotationUtils.getAttributes;
import static java.util.Optional.ofNullable;

/**
 * {@link org.springframework.beans.factory.config.BeanPostProcessor} implementation
 * that Consumer service {@link SOAReference} annotated fields
 *
 * @see SOAReference
 */
public class SOAReferenceAnnotationPostProcessor extends AbstractAnnotationBeanPostProcessor implements ApplicationContextAware {

    public static final String BEAN_NAME = "soaReferenceAnnotationPostProcessor";

    public SOAReferenceAnnotationPostProcessor() {
        super(true, SOAReference.class);
    }

    @Override
    protected ReferenceBean<?> buildReferenceBean(String referenceBeanName, AnnotationAttributes serviceAttribute, Class<?> referencedType) throws Exception {
        Assert.isTrue(referencedType.isInterface(), "@SOAReference can only be specified on an interface");
        SOAService soaService = referencedType.getAnnotation(SOAService.class);
        Assert.isTrue(soaService != null, "@SOAReference can only be specified on an interface what has @SOAService Annotation");

        Environment environment = applicationContext.getEnvironment();
        Map<String, Object> soaServiceAttributes = getAttributes(soaService, environment, true);

        cn.huolala.arch.hermes.compatible.config.ReferenceBean<?> referenceBean = new cn.huolala.arch.hermes.compatible.config.ReferenceBean<>();
        referenceBean.setId(referenceBeanName);
        referenceBean.setInterface(referencedType.getName());
        referenceBean.setApplicationContext(applicationContext);

        referenceBean.setApplication(getAttribute(soaServiceAttributes, APP_ID, true));

        //p2p
        configureP2PUrl(soaServiceAttributes, referenceBean);

        Map<String, Map<String, Object>> methodAttributes = new HashMap<>();
        if (serviceAttribute.containsKey(METHODS)) {
            ReferenceMethod[] methods = (ReferenceMethod[]) serviceAttribute.remove(METHODS);
            for (ReferenceMethod referenceMethod : methods) {
                AnnotationAttributes methodAttribute = AnnotationUtils.getAnnotationAttributes(referenceMethod, environment, true);
                serviceAttribute.forEach(methodAttribute::putIfAbsent);
                methodAttributes.put(methodAttribute.getString(VALUE), methodAttribute);
            }
        }
        soaServiceAttributes.putAll(serviceAttribute);

        String servicePath = getAttribute(soaServiceAttributes, VALUE, true);
        String contextId = getAttribute(soaServiceAttributes, CONTEXT_ID);
        if (StringUtils.isNotEmpty(contextId)) {
            servicePath = StringUtils.addSuffixIfNot(StringUtils.addPrefixIfNot(contextId, PATH_PREFIX), PATH_PREFIX) + StringUtils.removeStart(servicePath, PATH_PREFIX);
        }
        referenceBean.setPath(servicePath);
        referenceBean.setProtocolName(JSONRPC_PROTOCOL);

        Method[] methods = referencedType.getMethods();
        List<MethodConfig> methodConfigs = new ArrayList<>(methods.length);
        for (Method method : methods) {
            JsonRpcAttributeMetadata mt = BeanDefinitionSupport.attributeMetadata(referenceBean.getApplication(), referenceBean.getPath(), method);
            if (methodAttributes.containsKey(method.getName())) {
                mt.of(methodAttributes.get(method.getName()));
            }
            ofNullable(soaServiceAttributes.get(CONNECTION_TIMEOUT_MILLIS)).ifPresent(time -> {
                if (!ofNullable(mt.getConnectionTimeoutMillis()).isPresent()) {
                    mt.setConnectionTimeoutMillis((Integer) time);
                }
            });
            ofNullable(soaServiceAttributes.get(READ_TIMEOUT_MILLIS)).ifPresent(time -> {
                if (!ofNullable(mt.getReadTimeoutMillis()).isPresent()) {
                    mt.setReadTimeoutMillis((Integer) time);
                }
            });
            ofNullable(soaServiceAttributes.get(ONE_WAY)).ifPresent(oneWay -> {
                if (!mt.isOneway()) {
                    mt.setOneway((Boolean) oneWay);
                }
            });
            MethodConfig methodConfig = new MethodConfig(method.getName(), ReflectUtils.getDesc(method.getParameterTypes()), mt.getFallbackFactory(), mt.getReadTimeoutMillis(),
                    mt.getConnectionTimeoutMillis(), mt.getMethodPath(), mt.getCommandKey(), mt.getDescription(), null, mt.isOneway(), mt.getParamNames(), mt.getAttributes());
            methodConfig.setRawPath(mt.getRawMethodPath());
            methodConfigs.add(methodConfig);
        }
        referenceBean.setMethods(methodConfigs);

        Map<String, String> parameters = new HashMap<>();
        soaServiceAttributes.forEach((key, value) -> ofNullable(value).ifPresent(v -> parameters.put(key, String.valueOf(v))));

        //enable jsonrpc consumer filter
        parameters.put(JSONRPC_FILTER, JSONRPC_CONSUMER_FILTER);

        referenceBean.setParameters(parameters);

        referenceBean.afterPropertiesSet();
        return referenceBean;
    }

}
