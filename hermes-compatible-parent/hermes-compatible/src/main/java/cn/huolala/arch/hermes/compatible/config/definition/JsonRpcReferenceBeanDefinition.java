package cn.huolala.arch.hermes.compatible.config.definition;

import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.context.ConfigManager;
import cn.huolala.arch.hermes.compatible.config.beans.JsonRpcGenericReferenceAnnotationPostProcessor;
import cn.huolala.arch.hermes.compatible.config.beans.JsonRpcReferenceAnnotationPostProcessor;
import cn.huolala.arch.hermes.compatible.config.beans.SOAReferenceAnnotationPostProcessor;
import cn.huolala.arch.hermes.config.spring.definition.ReferenceBeanDefinition;
import cn.lalaframework.soa.jsonrpc.spi.SOAServiceClassLoadSpi;
import cn.lalaframework.soa.jsonrpc.spi.SOAServiceScanPackageLoadSpi;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionReaderUtils;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;

import java.util.ServiceLoader;
import java.util.Set;

import static com.alibaba.spring.util.BeanRegistrar.registerInfrastructureBean;
import static java.util.Optional.ofNullable;

public class JsonRpcReferenceBeanDefinition implements ReferenceBeanDefinition {


    @Override
    public void registerReferenceAnnotationPostProcessor(BeanDefinitionRegistry registry) {
        /*
         * SOAReference
         */
        registerInfrastructureBean(registry, SOAReferenceAnnotationPostProcessor.BEAN_NAME, SOAReferenceAnnotationPostProcessor.class);

        ConfigManager configManager = ApplicationContext.getConfigManager();

        Set<String> scanBasePackages = Sets.newHashSet();
        Set<String> scanPackageClasses = Sets.newHashSet();
         configManager.getConsumer().ifPresent(consumerConfig -> {
             ofNullable(consumerConfig.getScanBasePackages()).ifPresent(scanBasePackages::addAll);
             ofNullable(consumerConfig.getScanPackageClasses()).ifPresent(scanPackageClasses::addAll);
         });

        //compatible spi loader
        loadClassBySpi(scanPackageClasses);
        loadScanPackageBySpi(scanBasePackages);

        //scan reference bean
        AbstractBeanDefinition referenceBeanDefinition = BeanDefinitionBuilder.rootBeanDefinition(JsonRpcReferenceAnnotationPostProcessor.class)
                .addConstructorArgValue(scanBasePackages)
                .addConstructorArgValue(scanPackageClasses)
                .setRole(BeanDefinition.ROLE_INFRASTRUCTURE)
                .getBeanDefinition();
        BeanDefinitionReaderUtils.registerWithGeneratedName(referenceBeanDefinition, registry);


        //generic BeanDefinition
        AbstractBeanDefinition genericBeanDefinition = BeanDefinitionBuilder.rootBeanDefinition(JsonRpcGenericReferenceAnnotationPostProcessor.class)
                .setRole(BeanDefinition.ROLE_INFRASTRUCTURE)
                .getBeanDefinition();
        BeanDefinitionReaderUtils.registerWithGeneratedName(genericBeanDefinition, registry);
    }



    private void loadClassBySpi(Set<String> services) {
        ServiceLoader<SOAServiceClassLoadSpi> serviceLoaders = ServiceLoader.load(SOAServiceClassLoadSpi.class);
        for (SOAServiceClassLoadSpi serviceLoader : serviceLoaders) {
            Set<Class<?>> classSet = serviceLoader.load();
            for (Class<?> aClass : classSet) {
                services.add(aClass.getName());
            }
        }
    }


    private void loadScanPackageBySpi(Set<String> scanBasePackages) {
        ServiceLoader<SOAServiceScanPackageLoadSpi> serviceLoaders = ServiceLoader.load(SOAServiceScanPackageLoadSpi.class);
        for (SOAServiceScanPackageLoadSpi serviceLoader : serviceLoaders) {
            Set<String> scanPackageSet = serviceLoader.load();
            scanBasePackages.addAll(scanPackageSet);
        }
    }

}
