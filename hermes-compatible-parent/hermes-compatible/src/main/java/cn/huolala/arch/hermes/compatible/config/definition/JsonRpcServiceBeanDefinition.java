package cn.huolala.arch.hermes.compatible.config.definition;

import cn.huolala.arch.hermes.common.util.ClassUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.config.beans.JsonRpcScanHermesServiceAnnotationPostProcessor;
import cn.huolala.arch.hermes.compatible.config.beans.JsonRpcScanServiceAnnotationPostProcessor;
import cn.huolala.arch.hermes.compatible.config.beans.JsonRpcSpringServiceAnnotationPostProcessor;
import cn.huolala.arch.hermes.config.spring.definition.AbstractServiceBeanDefinition;
import cn.lalaframework.soa.annotation.spring.SOAServiceAutoImpl;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.core.type.AnnotationMetadata;

import java.util.Set;

public class JsonRpcServiceBeanDefinition extends AbstractServiceBeanDefinition {


    @Override
    public void registerServiceAnnotationPostProcessor(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry, BeanFactory beanFactory) {
        //Get beans with SOAServiceImpl annotations from the BeanDefinitionRegistry
        Set<String> registerScanClass = getSpringRegisterScanClass(registry);
        super.registerServiceAnnotationPostProcessor(registerScanClass, registry, JsonRpcSpringServiceAnnotationPostProcessor.class);

        Set<String> packagesToScan = getPackagesToScan(importingClassMetadata, registry);
        super.registerServiceAnnotationPostProcessor(packagesToScan, registry, JsonRpcScanServiceAnnotationPostProcessor.class);
        super.registerServiceAnnotationPostProcessor(packagesToScan, registry, JsonRpcScanHermesServiceAnnotationPostProcessor.class);
    }


    private Set<String> getSpringRegisterScanClass(BeanDefinitionRegistry registry) {
        Set<String> scanClass = Sets.newHashSet();
        String[] beanDefinitionNames = registry.getBeanDefinitionNames();
        for (String beanDefinitionName : beanDefinitionNames) {
            BeanDefinition beanDefinition = registry.getBeanDefinition(beanDefinitionName);
            String beanClassName = beanDefinition.getBeanClassName();
            if (StringUtils.isNotEmpty(beanClassName)) {
                try {
                    Class<?> resolvedClass = ClassUtils.classForName(beanClassName);
                    if (resolvedClass.isAnnotationPresent(SOAServiceAutoImpl.class)) {
                        scanClass.add(beanClassName);
                    }
                } catch (ClassNotFoundException ignored) {
                    logger.warn("The class [" + beanClassName + "] could not be found");
                }
            }
        }
        return scanClass;
    }


    @Override
    public void registerServiceAnnotationPostProcessor(Set<String> packagesToScan, BeanDefinitionRegistry registry) {
        //NOTING TO DO
    }
}
