package cn.huolala.arch.hermes.compatible.config.generic;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.url.URLBuilder;
import cn.huolala.arch.hermes.common.util.StringUtils;
import org.springframework.beans.factory.SmartFactoryBean;

import static cn.huolala.arch.hermes.common.constants.Constants.CLUSTER_KEY;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.GENERIC_PREFIX;

@ApiAudience.Private
public class ReferenceBean<T> extends cn.huolala.arch.hermes.compatible.config.ReferenceBean<T> implements SmartFactoryBean<T> {


    @Override
    public void doAfterPropertiesSet() {
        // NOTHING TO DO
    }

    @Override
    protected URL getDiscoveryUrl() {
        URL discoveryUrl = super.getDiscoveryUrl();
        return URLBuilder.from(discoveryUrl).setProtocol(StringUtils.addPrefixIfNot(discoveryUrl.getProtocol(), GENERIC_PREFIX))
                .addParameter(CLUSTER_KEY, getClusterOrDefault()).build();
    }


    @Override
    public boolean isEagerInit() {
        return true;
    }
}
