package cn.huolala.arch.hermes.compatible.config.support;

import cn.huolala.arch.hermes.api.config.HostPortsConfig;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.util.*;
import cn.huolala.arch.hermes.compatible.config.ReferenceBean;
import cn.lalaframework.soa.annotation.SOAMethod;
import cn.lalaframework.soa.annotation.SOAParam;
import cn.lalaframework.soa.annotation.SOAParamsMode;
import com.google.common.collect.ImmutableList;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.huolala.arch.hermes.common.constants.Constants.HOSTPORTS_ENABLED_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.JSONRPC_PROTOCOL;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.APP_HOST;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.APP_PORT;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.MAX_COMMAND_KEY_LENGTH;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.PATH_PREFIX;
import static cn.huolala.arch.hermes.config.spring.support.AnnotationUtils.getAttribute;


public final class BeanDefinitionSupport {

    private BeanDefinitionSupport() {
    }

    public static JsonRpcAttributeMetadata attributeMetadata(String appId, String servicePath, Method method) {
        JsonRpcAttributeMetadata attributeMetadata = new JsonRpcAttributeMetadata();
        attributeMetadata.setMethodPath(method.getName());
        attributeMetadata.setRawMethodPath(method.getName());
        String methodNameForCommandKey = method.getName();
        SOAMethod soaMethod = method.getAnnotation(SOAMethod.class);
        String commandKey = null;
        if (null != soaMethod) {
            commandKey = soaMethod.commandKey();
            if (StringUtils.isNotBlank(soaMethod.value())) {
                attributeMetadata.setMethodPath(soaMethod.value());
                attributeMetadata.setRawMethodPath(soaMethod.value());
                methodNameForCommandKey = soaMethod.value();
            }
            soaMethodAttributesMetadata(attributeMetadata, soaMethod);
        }
        soaParamAttributesMetadata(method, attributeMetadata);
        attributeMetadata.setCommandKey(CommandKeyUtils.buildInConsumerSide(appId, confirmServicePathForCommandKey(servicePath),
                confirmMethodPathForCommandKey(attributeMetadata.getMethodPath(), methodNameForCommandKey, commandKey)));
        return attributeMetadata;
    }

    private static void soaMethodAttributesMetadata(JsonRpcAttributeMetadata attributesMetadata, SOAMethod soaMethod) {
        attributesMetadata.setDescription(soaMethod.description());
        attributesMetadata.setResultMode(soaMethod.resultMode());
        attributesMetadata.setParamsMethod(soaMethod.paramsMethod());
        attributesMetadata.setParamsMode(soaMethod.paramsMode());
        attributesMetadata.setCompleteResult(soaMethod.completeResult());
        attributesMetadata.setFixedParamsAppend(soaMethod.fixedParamsAppend());

        if (soaMethod.connectionTimeoutMillis() > 0) {
            attributesMetadata.setConnectionTimeoutMillis(soaMethod.connectionTimeoutMillis());
        }
        if (soaMethod.readTimeoutMillis() > 0) {
            attributesMetadata.setReadTimeoutMillis(soaMethod.readTimeoutMillis());
        }
        if (soaMethod.paramsMode() != SOAParamsMode.STANDARD) {
            attributesMetadata.setFixedParams(new HashMap<>());
            String methodPath = resolverFixParams(attributesMetadata.isFixedParamsAppend(), attributesMetadata.getFixedParams(), attributesMetadata.getMethodPath());
            attributesMetadata.setMethodPath(methodPath);
        }
    }

    /*
     * SOAParam
     */
    private static void soaParamAttributesMetadata(Method method, JsonRpcAttributeMetadata attributeMetadata) {
        Annotation[][] annotations = method.getParameterAnnotations();
        List<String> paramsTypeNames = Arrays.stream(annotations)
                .flatMap(Arrays::stream)
                .filter(annotation -> annotation.annotationType() == SOAParam.class)
                .map(annotation -> ((SOAParam) annotation).value())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(paramsTypeNames) && paramsTypeNames.size() != annotations.length) {
            throw new IllegalArgumentException("Param annotations must be added to all parameters, interface:"
                    + method.getDeclaringClass() + ", method:" + method.getName());
        }
        attributeMetadata.setParamNames(ImmutableList.copyOf(paramsTypeNames));
    }


    public static String resolverFixParams(boolean fixedParamsAppend, Map<String, Object> fixedParams, String methodName) {
        String methodPath = "";
        if (StringUtils.isNotEmpty(methodName)) {
            if (methodName.contains("?")) {
                if (fixedParamsAppend) {
                    methodPath = StringUtils.addPrefixIfNot(methodName, PATH_PREFIX);
                } else {
                    //解析methodName中的字符串，将其中的k=v存储到FixedParams中，不追加到url后面。
                    String[] pathArray = methodName.split("\\?");
                    if (ArrayUtils.isNotEmpty(pathArray)) {
                        methodPath = StringUtils.addPrefixIfNot(pathArray[0], PATH_PREFIX);
                        pathFixParams(fixedParams, pathArray);
                    }
                }
                // name=1&age=2
            } else if (methodName.contains("=")) {
                if (fixedParamsAppend) {
                    methodPath = "?" + methodName;
                } else {
                    String[] strArry = methodName.split("&");
                    for (String param : strArry) {
                        String[] p = param.split("=");
                        if (p.length > 1) {
                            fixedParams.put(p[0], p[1]);
                        }
                    }
                }
            } else {
                methodPath = StringUtils.addPrefixIfNot(methodName, PATH_PREFIX);
            }
        }
        return methodPath;
    }

    private static void pathFixParams(Map<String, Object> fixedParams, String[] pathArray) {
        if (pathArray.length > 1) {
            String params = pathArray[1].trim();
            String[] strArry = params.split("&");
            for (String param : strArry) {
                String[] p = param.split("=");
                if (p.length > 1) {
                    fixedParams.put(p[0], p[1]);
                }
            }
        }
    }


    private static String confirmServicePathForCommandKey(String servicePath) {
        String removeSuffix = StringUtils.removeEnd(servicePath, PATH_PREFIX);
        return removeSuffix != null ? removeSuffix : "";
    }



    /**
     * @param methodPath the equivalent of SOAMethodAttrs.getMethodName
     * @param customCommandKey just in place of methodPath
     */
    public static String generateConsumerKeyInGeneric(String appId, String servicePath, String methodPath, String customCommandKey) {
        servicePath = StringUtils.removeEnd(servicePath, PATH_PREFIX);

        //优先去自定义commandKey
        if (StringUtils.isNotBlank(customCommandKey)) {
            methodPath = customCommandKey;
        }
        methodPath = StringUtils.removeEnd(StringUtils.removeStart(methodPath, PATH_PREFIX), PATH_PREFIX);
        methodPath = StringUtils.isNotBlank(methodPath) ? methodPath : "";
        String consumerKey = CommandKeyUtils.buildInConsumerSide(appId, servicePath, methodPath);
        return consumerKey.length() > MAX_COMMAND_KEY_LENGTH ? consumerKey.substring(0, MAX_COMMAND_KEY_LENGTH) : consumerKey;
    }


    /**
     * @param methodPath which after the processing
     * @param methodName maybe method.getName() or soaMethod.value() and it will not be null
     * @param commandKey custom commandKey
     */
    private static String confirmMethodPathForCommandKey(String methodPath, String methodName, String commandKey) {
        //兼容1.3.2的methodName逻辑获取
        String confirmMethodPath = methodPath;
        if (StringUtils.isBlank(confirmMethodPath)) {
            confirmMethodPath = methodName;
        }
        if (StringUtils.isNotBlank(commandKey)) {
            confirmMethodPath = commandKey;
        }
        //去除前后斜杆
        confirmMethodPath = StringUtils.removeEnd(StringUtils.removeStart(confirmMethodPath, PATH_PREFIX), PATH_PREFIX);
        if (StringUtils.isNotBlank(confirmMethodPath)) {
            return confirmMethodPath;
        }
        return methodName;
    }


    /**
     * enabled p2p
     */
    public static boolean p2pEnabled() {
        return ApplicationContext.getEnvironment().getConfiguration().getBoolean(HOSTPORTS_ENABLED_KEY, false);
    }


    /**
     * p2p config
     */
    public static Optional<HostPortsConfig.HostPort> p2pConfig(String name) {
        Optional<HostPortsConfig> hostPortConfig = ApplicationContext.getConfigManager().getHostPortConfig();
        return hostPortConfig.map(hostPortsConfig -> hostPortsConfig.getHostPorts().get(name));
    }


    public static void configureP2PUrl(Map<String, Object> attributes, ReferenceBean<?> referenceBean) {
        if (p2pEnabled()) {
            String appHost = getAttribute(attributes, APP_HOST);
            if (StringUtils.isNotEmpty(appHost)) {
                String appPort = getAttribute(attributes, APP_PORT);
                referenceBean.setUrl(URLUtils.newURLBuilder(JSONRPC_PROTOCOL, appHost, StringUtils.isEmpty(appPort) ? null : Integer.valueOf(appPort)).build().toFullString());
            } else {
                p2pConfig(referenceBean.getApplication()).ifPresent(hostPort ->
                        referenceBean.setUrl(URLUtils.newURLBuilder(JSONRPC_PROTOCOL, hostPort.getHost(), hostPort.getPort() == null ? null : hostPort.getPort()).build().toFullString()));
            }
        }
    }

}
