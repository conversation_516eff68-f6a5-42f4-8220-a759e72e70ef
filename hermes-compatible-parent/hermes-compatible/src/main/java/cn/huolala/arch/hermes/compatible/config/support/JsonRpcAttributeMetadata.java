package cn.huolala.arch.hermes.compatible.config.support;

import cn.huolala.arch.hermes.api.fallback.FallbackFactory;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.lalaframework.soa.annotation.SOAParamsMethod;
import cn.lalaframework.soa.annotation.SOAParamsMode;
import cn.lalaframework.soa.annotation.SOAParamsPassMode;
import cn.lalaframework.soa.annotation.SOAResultMode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.FALLBACK_KEY;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.COMMAND_KEY;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.COMPLETE_RESULT;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.CONNECTION_TIMEOUT_MILLIS;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.FIXED_PARAMS;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.FIXED_PARAMS_APPEND;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.METHOD_PATH;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.ONE_WAY;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.PARAMS_METHOD;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.PARAMS_MODE;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.READ_TIMEOUT_MILLIS;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.RESULT_MODE;
import static cn.huolala.arch.hermes.config.spring.support.AnnotationUtils.getAttribute;

public class JsonRpcAttributeMetadata {

    /**
     * the method's name.
     */
    private String methodPath;

    private String rawMethodPath;

    /**
     * value oneway
     */
    private boolean oneway;

    /**
     * the method description
     */
    private String description;

    /**
     * invoke completeResult
     */
    private boolean completeResult;

    /**
     * set custom commandKey
     */
    private String commandKey;

    /**
     * when value is zero default use common config
     */
    private Integer connectionTimeoutMillis;

    /**
     * when value is zero default use common config
     */
    private Integer readTimeoutMillis;

    /**
     * value fixed params is append url
     */
    private boolean fixedParamsAppend;

    /**
     * The method params
     */
    private List<String> paramNames;

    /**
     * The fallback factory
     */
    private FallbackFactory<?> fallbackFactory;

    /**
     * param mode
     */
    private SOAParamsMode paramsMode = SOAParamsMode.STANDARD;

    /**
     * result mode
     */
    private SOAResultMode resultMode = SOAResultMode.OBJECT;

    /**
     * request method type
     */
    private SOAParamsMethod paramsMethod = SOAParamsMethod.GET;

    /**
     * paramsPassMode
     */
    private SOAParamsPassMode paramsPassMode = SOAParamsPassMode.AUTO;

    /**
     * fixedParams
     */
    private Map<String, Object> fixedParams = new HashMap<>();


    public String getMethodPath() {
        return methodPath;
    }

    public void setMethodPath(String methodPath) {
        this.methodPath = methodPath;
    }

    public String getRawMethodPath() {
        return rawMethodPath;
    }

    public void setRawMethodPath(String rawMethodPath) {
        this.rawMethodPath = rawMethodPath;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isCompleteResult() {
        return completeResult;
    }

    public void setCompleteResult(boolean completeResult) {
        this.completeResult = completeResult;
    }

    public String getCommandKey() {
        return commandKey;
    }

    public void setCommandKey(String commandKey) {
        this.commandKey = commandKey;
    }

    public Integer getConnectionTimeoutMillis() {
        return connectionTimeoutMillis;
    }

    public void setConnectionTimeoutMillis(Integer connectionTimeoutMillis) {
        this.connectionTimeoutMillis = connectionTimeoutMillis;
    }

    public Integer getReadTimeoutMillis() {
        return readTimeoutMillis;
    }

    public void setReadTimeoutMillis(Integer readTimeoutMillis) {
        this.readTimeoutMillis = readTimeoutMillis;
    }

    public List<String> getParamNames() {
        return paramNames;
    }

    public void setParamNames(List<String> paramNames) {
        this.paramNames = paramNames;
    }

    public boolean isOneway() {
        return oneway;
    }

    public void setOneway(boolean oneway) {
        this.oneway = oneway;
    }

    public boolean isFixedParamsAppend() {
        return fixedParamsAppend;
    }

    public void setFixedParamsAppend(boolean fixedParamsAppend) {
        this.fixedParamsAppend = fixedParamsAppend;
    }


    public FallbackFactory<?> getFallbackFactory() {
        return fallbackFactory;
    }

    public void setFallbackFactory(FallbackFactory<?> fallbackFactory) {
        this.fallbackFactory = fallbackFactory;
    }

    public SOAParamsMode getParamsMode() {
        return paramsMode;
    }

    public void setParamsMode(SOAParamsMode paramsMode) {
        this.paramsMode = paramsMode;
    }

    public SOAResultMode getResultMode() {
        return resultMode;
    }

    public void setResultMode(SOAResultMode resultMode) {
        this.resultMode = resultMode;
    }

    public SOAParamsMethod getParamsMethod() {
        return paramsMethod;
    }

    public void setParamsMethod(SOAParamsMethod paramsMethod) {
        this.paramsMethod = paramsMethod;
    }

    public SOAParamsPassMode getParamsPassMode() {
        return paramsPassMode;
    }

    public void setParamsPassMode(SOAParamsPassMode paramsPassMode) {
        this.paramsPassMode = paramsPassMode;
    }

    public Map<String, Object> getFixedParams() {
        return fixedParams;
    }

    public void setFixedParams(Map<String, Object> fixedParams) {
        this.fixedParams = fixedParams;
    }


    public void of(Map<String, Object> attributesMetadata) {
        configureIfPresent(attributesMetadata, this::setMethodPath, METHOD_PATH);
        configureIfPresent(attributesMetadata, this::setRawMethodPath, METHOD_PATH);
        configureIfPresent(attributesMetadata, this::setResultMode, RESULT_MODE);
        configureIfPresent(attributesMetadata, this::setParamsMethod, PARAMS_METHOD);
        configureIfPresent(attributesMetadata, this::setOneway, ONE_WAY);
        configureIfPresent(attributesMetadata, this::setFallbackFactory, FALLBACK_KEY);
        configureIfPresent(attributesMetadata, this::setParamsMode, PARAMS_MODE);
        configureIfPresent(attributesMetadata, this::setCompleteResult, COMPLETE_RESULT);
        configureIfPresent(attributesMetadata, this::setFixedParamsAppend, FIXED_PARAMS_APPEND);
        configureIfPresent(attributesMetadata, this::setConnectionTimeoutMillis, CONNECTION_TIMEOUT_MILLIS);
        configureIfPresent(attributesMetadata, this::setReadTimeoutMillis, READ_TIMEOUT_MILLIS);
        configureIfPresent(attributesMetadata, this::setCommandKey, COMMAND_KEY);
        configureIfPresent(attributesMetadata, this::setFixedParams, FIXED_PARAMS);
    }


    private <T> void configureIfPresent(Map<String, Object> attributesMetadata, Consumer<T> consumer, String name) {
        T attribute = getAttribute(attributesMetadata, name);
        if (attribute == null
                || attribute instanceof Number && ((Number) attribute).intValue() == 0) {
            return;
        }

        if (!(attribute instanceof CharSequence) || !StringUtils.isBlank((CharSequence) attribute)) {
            consumer.accept(attribute);
        }
    }

    public Map<String, Object> getAttributes() {
        Map<String, Object> extensionAttributes = new HashMap<>();
        extensionAttributes.put(RESULT_MODE, getResultMode());
        extensionAttributes.put(PARAMS_METHOD, getParamsMethod());
        extensionAttributes.put(COMPLETE_RESULT, isCompleteResult());
        extensionAttributes.put(PARAMS_MODE, getParamsMode());
        extensionAttributes.put(FIXED_PARAMS, getFixedParams());
        extensionAttributes.put(FIXED_PARAMS_APPEND, isFixedParamsAppend());
        return extensionAttributes;
    }

}
