package cn.huolala.arch.hermes.compatible.config.support;

import cn.huolala.arch.hermes.common.constants.ConstantsMarker;
import org.apache.hc.core5.pool.PoolConcurrencyPolicy;

public interface JsonRpcConstants extends ConstantsMarker {

    String ONE_WAY = "oneWay";
    String SERVICE_PATH = "path";
    String METHOD_PATH = "methodPath";

    String PARAMS_MODE = "paramsMode";
    String RESULT_MODE = "resultMode";
    String PARAMS_METHOD = "paramsMethod";
    String COMPLETE_RESULT = "completeResult";
    String FIXED_PARAMS_APPEND = "fixedParamsAppend";

    String COMMAND_KEY = "commandKey";
    String FIXED_PARAMS = "fixedParams";
    String CONNECTION_TIMEOUT = "connectionTimeout";
    String READ_TIMEOUT = "readTimeout";
    String RETURN_TYPE = "returnType";
    String ARGUMENTS = "arguments";

    String PATH_PREFIX = "/";
    int MAX_COMMAND_KEY_LENGTH = 100;

    String VALUE = "value";
    String APP_ID = "appId";
    String APP_HOST = "appHost";
    String APP_PORT = "appPort";
    String IS_HTTPS = "isHttps";
    String CONTEXT_ID = "contextId";
    String MODULE_NAME = "moduleName";


    String RET = "ret";
    String MESSAGE = "message";
    String EXCEPTION_TYPE_NAME = "exceptionTypeName";

    String DATA = "data";
    String ERROR_RET = "ret";
    String ERROR_MSG = "msg";

    String ID = "id";
    String METHOD = "method";
    String PARAMS = "params";
    String JSONRPC = "jsonrpc";
    String VERSION = "2.0";
    String ERROR = "error";
    String RESULT = "result";
    String ERROR_CODE = "code";
    String ERROR_MESSAGE = "message";


    String GENERIC_PREFIX = "generic";

    //reference
    String METHODS = "methods";
    String CONNECTION_TIMEOUT_MILLIS = "connectionTimeoutMillis";
    String READ_TIMEOUT_MILLIS = "readTimeoutMillis";


    //filter
    String JSONRPC_FILTER = "jsonrpc.filter";
    String JSONRPC_CONSUMER_FILTER = "jsonrpc.consumer";
    String JSONRPC_PROVIDER_FILTER = "jsonrpc.provider";
    String JSONRPC_GENERIC_CONSUMER_FILTER = "jsonrpc.generic.consumer";

    //append contextPath
    String APPEND_CONTEXT_PATH = "append.context.path";

    //http client config
    String HTTP_CONFIG_PREFIX = "http.request.config.";
    int CONNECTION_TIME_TO_LIVE_VALUE = 15;
    String CONNECTION_TIME_TO_LIVE_NAME = HTTP_CONFIG_PREFIX + "connectionTimeToLive";
    int MAX_CONN_TOTAL_VALUE = 1024;
    String MAX_CONN_TOTAL_NAME = HTTP_CONFIG_PREFIX + "maxConnTotal";
    int MAX_CONN_PER_ROUTE_VALUE = 1024;
    String MAX_CONN_PER_ROUTE_NAME = HTTP_CONFIG_PREFIX + "maxConnPerRoute";
    int IO_THREAD_COUNT_VALUE = Runtime.getRuntime().availableProcessors() * 2;
    String IO_THREAD_COUNT_NAME = HTTP_CONFIG_PREFIX + "ioThreadCount";
    String POOL_CONCURRENCY_POLICY_VALUE = PoolConcurrencyPolicy.LAX.name();
    String POOL_CONCURRENCY_POLICY_NAME = HTTP_CONFIG_PREFIX + "poolConcurrencyPolicy";
    boolean SOCKET_KEEP_LIVE_VALUE = true;
    String SOCKET_KEEP_LIVE_NAME = HTTP_CONFIG_PREFIX + "soKeepAlive";
    long DEFAULT_SELECT_INTERVAL = 100;
    int NANO_TIME_UNIT = 100_0000;

}
