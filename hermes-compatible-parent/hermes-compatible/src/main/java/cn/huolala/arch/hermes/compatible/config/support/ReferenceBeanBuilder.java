package cn.huolala.arch.hermes.compatible.config.support;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.FALLBACK_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.HTTPS_KEY;
import static cn.huolala.arch.hermes.common.util.StringUtils.toLowerCaseFirstOne;
import static cn.huolala.arch.hermes.compatible.config.support.BeanDefinitionSupport.configureP2PUrl;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.APPEND_CONTEXT_PATH;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.APP_ID;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.COMPLETE_RESULT;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.CONNECTION_TIMEOUT_MILLIS;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.FIXED_PARAMS_APPEND;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.IS_HTTPS;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC_CONSUMER_FILTER;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC_FILTER;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.METHOD_PATH;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.PARAMS_METHOD;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.PARAMS_MODE;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.READ_TIMEOUT_MILLIS;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.RESULT_MODE;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.SERVICE_PATH;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.VALUE;
import static cn.huolala.arch.hermes.config.spring.support.AnnotationUtils.getAttribute;
import static cn.huolala.arch.hermes.config.spring.support.AnnotationUtils.getAttributes;
import static java.util.Optional.ofNullable;
import static org.springframework.util.ClassUtils.getAllInterfacesForClass;

import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.env.Environment;
import org.springframework.util.Assert;

import cn.huolala.arch.hermes.api.annotation.Method;
import cn.huolala.arch.hermes.api.annotation.Param;
import cn.huolala.arch.hermes.api.config.MethodConfig;
import cn.huolala.arch.hermes.api.fallback.DefaultFallbackFactory;
import cn.huolala.arch.hermes.api.fallback.FallbackFactory;
import cn.huolala.arch.hermes.api.http.HttpMethod;
import cn.huolala.arch.hermes.api.http.ParamsMode;
import cn.huolala.arch.hermes.api.http.RequestMethod;
import cn.huolala.arch.hermes.api.http.ResultMode;
import cn.huolala.arch.hermes.cluster.fallback.Fallback;
import cn.huolala.arch.hermes.cluster.fallback.support.GlobalFallback;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.config.ReferenceBean;
import cn.huolala.arch.hermes.config.spring.support.AnnotationUtils;
import cn.lalaframework.soa.annotation.SOAParamsMethod;
import cn.lalaframework.soa.annotation.SOAParamsMode;
import cn.lalaframework.soa.annotation.SOAResultMode;
import cn.lalaframework.soa.annotation.SOAService;

/**
 * {@link ReferenceBean} Builder
 */
public final class ReferenceBeanBuilder {

    private final AnnotationAttributes attributes;

    private final ApplicationContext applicationContext;

    private final ClassLoader classLoader;

    private Class<?> interfaceClass;

    private String beanName;

    private ReferenceBeanBuilder(AnnotationAttributes attributes, ApplicationContext applicationContext, Class<?> interfaceClass) {
        Assert.notNull(attributes, "The Annotation attributes must not be null!");
        Assert.notNull(applicationContext, "The ApplicationContext must not be null!");

        this.attributes = attributes;
        this.applicationContext = applicationContext;
        this.interfaceClass = interfaceClass;
        this.classLoader = applicationContext.getClassLoader() != null ? applicationContext.getClassLoader()
                : Thread.currentThread().getContextClassLoader();
    }

    public static ReferenceBeanBuilder newBuilder(AnnotationAttributes attributes, ApplicationContext applicationContext, Class<?> interfaceClass) {
        return new ReferenceBeanBuilder(attributes, applicationContext, interfaceClass);
    }

    private void configureBean(ReferenceBean<?> referenceBean) throws Exception {
        referenceBean.setId(beanName);
        configureInterface(referenceBean);
        configureApplication(referenceBean);

        configureIfPresent(referenceBean::setPath, "path");
        configureIfPresent(referenceBean::setVersion, "version");

        configureUrl(referenceBean);
        configureIfPresent(referenceBean::setProtocolName, "protocol");
        configureIfPresent(referenceBean::setCluster, "cluster");
        configureIfPresent(referenceBean::setGroup, "group");
        configureIfPresent(referenceBean::setLoadbalance, "loadbalance");
        configureIfPresent(referenceBean::setTimeout, "timeout");
        configureIfPresent(referenceBean::setConnectionTimeout, "connectionTimeout");

        configureFallback(referenceBean);
        configureMethods(referenceBean);
    }

    private void configureUrl(ReferenceBean<?> referenceBean) {
        String url = getAttribute(attributes, "url");
        if (StringUtils.isNotEmpty(url)) {
            referenceBean.setUrl(url);
        } else {
            configureP2PUrl(attributes, referenceBean);
        }
    }


    private void postConfigureBean(AnnotationAttributes attributes, ReferenceBean<?> referenceBean) throws Exception {
        referenceBean.setApplicationContext(applicationContext);
        referenceBean.afterPropertiesSet();
        applicationContext.getAutowireCapableBeanFactory()
                .applyBeanPostProcessorsAfterInitialization(referenceBean, beanName);
    }

    private <T> void configureIfPresent(Consumer<T> consumer, String name) {
        T attribute = getAttribute(attributes, name);
        if (attribute == null
                || attribute instanceof Number && ((Number) attribute).intValue() == 0) {
            return;
        }

        if (!(attribute instanceof CharSequence) || !StringUtils.isBlank((CharSequence) attribute)) {
            consumer.accept(attribute);
        }
    }

    private void configureInterface(ReferenceBean<?> referenceBean) {
        Class<?>[] allInterfacesClass = getAllInterfacesForClass(interfaceClass);
        Class<?> clazz = allInterfacesClass.length > 0 ? allInterfacesClass[0] : interfaceClass;

        referenceBean.setInterface(clazz.getName());
    }

    private void configureApplication(ReferenceBean<?> referenceBean) {
        Stream.of("value", "application", "appId")
                .map(name -> getAttribute(attributes, name, ""))
                .filter(StringUtils::isNotBlank)
                .findFirst().ifPresent(referenceBean::setApplication);
    }

    private void configureFallback(ReferenceBean<?> reference) throws InstantiationException, IllegalAccessException {
        Class<?> fallback = attributes.getClass("fallback");

        Optional<FallbackFactory<?>> fallbackFactory = getFallbackFactory(fallback, reference.getInterfaceClass());
        if (fallbackFactory.isPresent()) {
            reference.setFallbackFactory(fallbackFactory.get());
        } else {
            // get app level FallbackFactory or throw IllegalStateException
            getGlobalFallbackFactory().ifPresent(reference::setFallbackFactory);
        }
    }


    private Optional<FallbackFactory<?>> getGlobalFallbackFactory() {
        return getFallbackBean(GlobalFallback.class).map(DefaultFallbackFactory::new);
    }

    private void configureMethods(ReferenceBean<?> reference) throws InstantiationException, IllegalAccessException {
        List<MethodConfig> methodConfigs = new ArrayList<>(reference.getInterfaceClass().getMethods().length);

        // reference methods first
        Method[] methods = attributes.getAnnotationArray("methods", Method.class);

        Map<String, Method> referenceMethodAttributes = Arrays.stream(methods).filter(method -> StringUtils.isNotEmpty(method.value())).collect(Collectors.toMap(Method::value, method -> method, (v1, v2) -> v2));

        for (java.lang.reflect.Method method : reference.getInterfaceClass().getMethods()) {
            Map<String, Object> methodAttributes = new HashMap<>();

            // interface method @Method
            ofNullable(method.getAnnotation(Method.class)).ifPresent(methodAnnotation -> methodAttributes.putAll(AnnotationUtils.getAnnotationAttributes(methodAnnotation, applicationContext.getEnvironment(), true)));

            // @HermesReference @Method cover interface @Method
            ofNullable(referenceMethodAttributes.get(method.getName())).ifPresent(methodAnnotation -> methodAttributes.putAll(AnnotationUtils.getAnnotationAttributes(methodAnnotation, applicationContext.getEnvironment(), true)));

            adapterMethod(methodAttributes, reference.getInterfaceClass());
            adapterHttpMethod(methodAttributes);

            JsonRpcAttributeMetadata attributeMetadata = BeanDefinitionSupport.attributeMetadata(reference.getApplication(), reference.getPath(), method);
            attributeMetadata.of(methodAttributes);

            String methodDesc = getAttribute(methodAttributes, "paramDesc");
            if (StringUtils.isEmpty(methodDesc)) {
                methodDesc = ReflectUtils.getDesc(method.getParameterTypes());
            }

            Annotation[][] annotations = method.getParameterAnnotations();
            List<String> paramsTypeNames = Arrays.stream(annotations)
                    .flatMap(Arrays::stream)
                    .filter(annotation -> annotation.annotationType() == Param.class)
                    .map(annotation -> ((Param) annotation).value())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(paramsTypeNames) && paramsTypeNames.size() != annotations.length) {
                throw new IllegalArgumentException("Param annotations must be added to all parameters, interface:"
                        + method.getDeclaringClass() + ", method:" + method.getName());
            }
            if (CollectionUtils.isNotEmpty(paramsTypeNames)) {
                attributeMetadata.setParamNames(paramsTypeNames);
            }

            MethodConfig methodConfig = new MethodConfig(method.getName(), methodDesc, attributeMetadata.getFallbackFactory(), attributeMetadata.getReadTimeoutMillis(),
                    attributeMetadata.getConnectionTimeoutMillis(), attributeMetadata.getMethodPath(), attributeMetadata.getCommandKey(), attributeMetadata.getDescription(),
                    getAttribute(methodAttributes, "loadbalance"), attributeMetadata.isOneway(), attributeMetadata.getParamNames(), attributeMetadata.getAttributes());
            methodConfig.setRawPath(attributeMetadata.getRawMethodPath());
            methodConfigs.add(methodConfig);
        }
        reference.setMethods(methodConfigs);
    }

    private void adapterMethod(Map<String, Object> methodAttributes, Class<?> interfaceClass) throws InstantiationException, IllegalAccessException {
        Class<?> fallback = getAttribute(methodAttributes, "fallback");
        if (fallback != null) {
            getFallbackFactory(fallback, interfaceClass).ifPresent(fallbackFactory -> methodAttributes.put(FALLBACK_KEY, fallbackFactory));
        }
        ofNullable(getAttribute(methodAttributes, "path")).ifPresent(path -> methodAttributes.put(METHOD_PATH, path));
        ofNullable(getAttribute(methodAttributes, "timeout")).ifPresent(time -> methodAttributes.put(READ_TIMEOUT_MILLIS, time));
        ofNullable(getAttribute(methodAttributes, "connectionTimeout")).ifPresent(time -> methodAttributes.put(CONNECTION_TIMEOUT_MILLIS, time));
    }

    private void adapterHttpMethod(Map<String, Object> methodAttributes) {
        HttpMethod httpMethod = getAttribute(methodAttributes, "http");
        if (httpMethod != null) {
            Map<String, Object> httpMethodAttributes = getAttributes(httpMethod, false);
            RequestMethod requestMethod = getAttribute(httpMethodAttributes, "method");
            SOAParamsMethod soaParamsMethod;
            if (requestMethod == RequestMethod.GET) {
                soaParamsMethod = SOAParamsMethod.GET;
            } else {
                soaParamsMethod = SOAParamsMethod.POST;
            }
            methodAttributes.put(PARAMS_METHOD, soaParamsMethod);

            SOAParamsMode soaParamsMode;
            ParamsMode paramsMode = getAttribute(httpMethodAttributes, "paramsMode");
            if (paramsMode == ParamsMode.PARAMS) {
                soaParamsMode = SOAParamsMode.PARAMS;
            } else if (paramsMode == ParamsMode.BODY_JSON) {
                soaParamsMode = SOAParamsMode.BODY_JSON;
            } else if (paramsMode == ParamsMode.BODY_FORM_DATA) {
                soaParamsMode = SOAParamsMode.BODY_FORM_DATA;
            } else {
                soaParamsMode = SOAParamsMode.STANDARD;
            }
            methodAttributes.put(PARAMS_MODE, soaParamsMode);

            SOAResultMode soaResultMode;
            ResultMode resultMode = getAttribute(httpMethodAttributes, "resultMode");
            if (resultMode == ResultMode.STRING) {
                soaResultMode = SOAResultMode.STRING;
            } else if (resultMode == ResultMode.OBJECT) {
                soaResultMode = SOAResultMode.OBJECT;
            } else {
                soaResultMode = SOAResultMode.JSON_OBJECT;
            }
            methodAttributes.put(RESULT_MODE, soaResultMode);
            boolean completeResult = getAttribute(httpMethodAttributes, "completeResult");
            if (completeResult) {
                methodAttributes.put(COMPLETE_RESULT, true);
            }

            boolean fixedParamsToUrl = getAttribute(httpMethodAttributes, "fixedParamsToUrl");
            if (fixedParamsToUrl) {
                methodAttributes.put(FIXED_PARAMS_APPEND, true);
            }
        }

    }

    private Optional<FallbackFactory<?>> getFallbackFactory(Class<?> fallback, Class<?> interfaceClass)
            throws IllegalArgumentException, InstantiationException, IllegalAccessException {
        if (void.class.isAssignableFrom(fallback)) {
            return Optional.empty();
        }

        if (FallbackFactory.class.isAssignableFrom(fallback)) {
            return Optional.of((FallbackFactory<?>) (getFallbackBean(fallback).orElse(fallback.newInstance())));
        } else if (Fallback.class.isAssignableFrom(fallback)
                || interfaceClass.isAssignableFrom(fallback)) {
            return Optional.of(new DefaultFallbackFactory<>(getFallbackBean(fallback).orElse(fallback.newInstance())));
        } else {
            throw new IllegalArgumentException("Unknown FallbackFactory config");
        }
    }

    private Optional<Object> getFallbackBean(Class<?> fallback) {
        Map<String, ?> beansOfType = applicationContext.getBeansOfType(fallback);
        return beansOfType.values().stream().map(v -> (Object) v).findFirst();
    }


    public ReferenceBeanBuilder beanName(String beanName) {
        this.beanName = beanName;
        return this;
    }

    public ReferenceBean<?> build() throws Exception {
        ReferenceBean<?> referenceBean = new ReferenceBean<>();
        Map<String, String> parameters = new HashMap<>();
        referenceBean.setParameters(parameters);

        Environment environment = applicationContext.getEnvironment();
        adapterSoaService(environment, parameters);

        //default interfaceClass simpleName as path
        if (StringUtils.isEmpty(getAttribute(attributes, SERVICE_PATH))) {
            attributes.put(SERVICE_PATH, toLowerCaseFirstOne(interfaceClass.getSimpleName()));
            parameters.put(APPEND_CONTEXT_PATH, "true");
        }
        
        // Append is https
        parameters.put(IS_HTTPS, String.valueOf(attributes.get(HTTPS_KEY)));

        // Enable jsonrpcClusterContextFilter
        parameters.put(JSONRPC_FILTER, JSONRPC_CONSUMER_FILTER);

        configureBean(referenceBean);
        postConfigureBean(attributes, referenceBean);

        return referenceBean;
    }


    private void adapterSoaService(Environment environment, Map<String, String> parameters) {
        ofNullable(interfaceClass.getAnnotation(SOAService.class)).ifPresent(soaService -> {
            Map<String, Object> serviceAttributes = getAttributes(soaService, environment, true);
            ofNullable(getAttribute(serviceAttributes, APP_ID)).ifPresent(appId -> attributes.put(APP_ID, appId));
            if (StringUtils.isEmpty(getAttribute(attributes, SERVICE_PATH))) {
                ofNullable(getAttribute(serviceAttributes, VALUE)).ifPresent(path -> attributes.put(SERVICE_PATH, path));
            }
            boolean isHttps = Boolean.parseBoolean(getAttribute(serviceAttributes, IS_HTTPS));
            if (isHttps) {
                attributes.put(HTTPS_KEY, isHttps);
            }
            serviceAttributes.forEach((String key, Object value) -> ofNullable(value).ifPresent(v -> parameters.put(key, String.valueOf(v))));
        });
    }
}
