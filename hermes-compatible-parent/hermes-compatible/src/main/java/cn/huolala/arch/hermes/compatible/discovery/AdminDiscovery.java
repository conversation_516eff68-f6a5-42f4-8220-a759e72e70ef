package cn.huolala.arch.hermes.compatible.discovery;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.thread.NamedThreadFactory;
import cn.huolala.arch.hermes.common.url.URLBuilder;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.JsonUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.util.http.HttpResult;
import cn.huolala.arch.hermes.compatible.util.http.HttpUtils;
import cn.huolala.arch.hermes.discovery.DiscoveryStrategy;
import cn.huolala.arch.hermes.discovery.ServiceInstance;
import cn.huolala.arch.hermes.discovery.core.AbstractDiscovery;
import cn.huolala.arch.hermes.discovery.core.DefaultServiceInstance;
import cn.huolala.arch.hermes.discovery.event.DiscoveryListener;
import cn.huolala.arch.hermes.discovery.exception.DiscoveryException;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static cn.huolala.arch.hermes.common.constants.Constants.ADMIN_HOST_KEY;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.DISCOVERY_ADMIN;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.REGISTER;

public class AdminDiscovery extends AbstractDiscovery<AdminHealthListener, String> {
    private static final Logger logger = LoggerFactory.getLogger(AdminDiscovery.class);

    private final String domain;

    private static final String BASE_REQUEST_URL = "http://%s/admin/domain/";

    private static final String DOMAIN_INFO_URL = BASE_REQUEST_URL + "info?appid=%s";

    private static final String BATCH_DOMAIN_INFO_URL_FORMAT = BASE_REQUEST_URL + "batch_info";

    private final String batchDomainInfoUrl;

    public static final int DEFAULT_NON_PORT = -1;

    protected final Map<String, ServiceInstance> serviceDomainInstanceCaches = new ConcurrentHashMap<>();

    private ScheduledExecutorService watchExecutor;

    public AdminDiscovery(URL discoveryUrl) {
        super(URLBuilder.from(discoveryUrl).
                setAddress((String) ApplicationContext.getAttributes().get(ADMIN_HOST_KEY))
                .setPort(80)
                .setProtocol(DISCOVERY_ADMIN)
                .addParameter(REGISTER, false)
                .build());
        this.domain = (String) ApplicationContext.getAttributes().get(ADMIN_HOST_KEY);
        this.batchDomainInfoUrl = String.format(BATCH_DOMAIN_INFO_URL_FORMAT, domain);
    }

    public void initialize() throws DiscoveryException {
        watchExecutor = Executors.newSingleThreadScheduledExecutor(new NamedThreadFactory(
                "Admin Registry subscribe thread", true));
        Optional.ofNullable(domain).ifPresent(domain -> watchExecutor.scheduleWithFixedDelay(
                () -> {
                    try {
                        this.refreshBatch();
                    } catch (Exception e) {
                        logger.warn("batch refresh domain error, request url: " + batchDomainInfoUrl + ", exception message: " + e.getMessage());
                    }
                }, 0, 60, TimeUnit.SECONDS));
    }

    @Override
    protected void doRegister(ServiceInstance serviceInstance) throws DiscoveryException {
        //doNothing for soa-admin
    }

    @Override
    public void update(ServiceInstance serviceInstance) throws DiscoveryException {
        //doNothing for soa-admin
    }

    @Override
    protected void doUnregister(ServiceInstance serviceInstance) throws DiscoveryException {
        //doNothing for soa-admin
    }

    @Override
    protected ServiceInstance doBuildServiceInstance(String serviceName) throws DiscoveryException {
        return refreshSingle(serviceName);
    }

    @Override
    public void addListener(String serviceName, List<String> filterTags, DiscoveryListener listener) throws DiscoveryException {
        listeners.computeIfAbsent(serviceName, key -> new AdminHealthListener())
                .addListener(listener);
    }

    @Override
    public void removeListener(String serviceName, List<String> filterTags, DiscoveryListener listener) throws DiscoveryException {
        AdminHealthListener adminHealthListener = listeners.get(serviceName);
        if (adminHealthListener != null) {
            adminHealthListener.removeListener(listener);
        }
    }

    @Override
    public List<ServiceInstance> getInstances(String serviceName, List<String> filterTags) throws DiscoveryException {
        List<ServiceInstance> serviceInstances = new ArrayList<>();
        Optional.ofNullable(serviceDomainInstanceCaches.computeIfAbsent(serviceName, this::refreshSingle)).ifPresent(serviceInstances::add);
        return serviceInstances;
    }

    @Override
    public DiscoveryStrategy.Type type() {
        return DiscoveryStrategy.Type.DOMAIN;
    }

    @Override
    protected void doDestroy() {
        watchExecutor.shutdown();
    }

    private void refreshBatch() throws Exception {
        Set<String> serviceNames = listeners.keySet();
        if (CollectionUtils.isEmpty(serviceNames)) {
            return;
        }

        Map<String, Set<String>> requestJsonBody = new HashMap<>();
        requestJsonBody.put("appIds", serviceNames);
        final HttpResult httpResult = HttpUtils.doPost(batchDomainInfoUrl, JsonUtils.serialize(requestJsonBody));
        if (httpResult.getCode() != 200) {
            return;
        }

        DomainResponse<Map<String, String>> domainResponse = JsonUtils.deserialize(httpResult.getBody(),
                new TypeReference<DomainResponse<Map<String, String>>>() {
                });
        if (domainResponse != null && domainResponse.isSuccess()) {
            Map<String, String> data = domainResponse.getData();
            data.forEach((serviceName, serviceDomain) -> {
                DefaultServiceInstance newServiceInstance = new DefaultServiceInstance(serviceName, serviceDomain, DEFAULT_NON_PORT);
                ServiceInstance oldServiceInstance = serviceDomainInstanceCaches.get(serviceName);
                if (Objects.isNull(oldServiceInstance) || !StringUtils.equals(oldServiceInstance.getHost(), newServiceInstance.getHost())) {
                    listeners.computeIfAbsent(serviceName, s -> new AdminHealthListener())
                            .getListenersAndOnEvent(serviceName, Collections.singletonList(newServiceInstance));
                    String preDomain = Objects.isNull(oldServiceInstance) ? "" : oldServiceInstance.getHost();
                    logger.info("update " + serviceName + "'s domain: " + preDomain + " to " + newServiceInstance.getHost());
                }
                serviceDomainInstanceCaches.put(serviceName, newServiceInstance);
            });
        }
    }

    private ServiceInstance refreshSingle(String serviceName) {
        String requestUrl = String.format(DOMAIN_INFO_URL, domain, serviceName);
        if (StringUtils.isEmpty(serviceName)) {
            return null;
        }

        try {
            HttpResult httpResult = HttpUtils.doGet(requestUrl);
            if (httpResult.getCode() != 200) {
                return null;
            }
            DomainResponse<List<String>> domainResponse = JsonUtils.deserialize(httpResult.getBody(), new TypeReference<DomainResponse<List<String>>>() {
            });
            if (domainResponse != null && domainResponse.isSuccess()) {
                if (CollectionUtils.isNotEmpty(domainResponse.getData())) {
                    logger.info("refresh domain success, appId: " + serviceName + " domain: " + domainResponse.getData().get(0));
                    return new DefaultServiceInstance(serviceName, domainResponse.getData().get(0), DEFAULT_NON_PORT);
                }
            }
        } catch (Exception e) {
            logger.warn(" refresh single domain error,appId: " + serviceName + ",URL: " + requestUrl, e);
        }
        return null;
    }

    private static class DomainResponse<T> implements Serializable {

        private Integer ret;
        private String msg;
        private T data;

        public Integer getRet() {
            return ret;
        }

        public void setRet(Integer ret) {
            this.ret = ret;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public T getData() {
            return data;
        }

        public void setData(T data) {
            this.data = data;
        }

        public boolean isSuccess() {
            return ret == 0;
        }
    }

}
