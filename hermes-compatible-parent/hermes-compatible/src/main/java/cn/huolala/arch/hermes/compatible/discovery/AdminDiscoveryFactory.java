package cn.huolala.arch.hermes.compatible.discovery;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.discovery.Discovery;
import cn.huolala.arch.hermes.discovery.core.AbstractDiscoveryFactory;

public class AdminDiscoveryFactory extends AbstractDiscoveryFactory {
    @Override
    protected Discovery createDiscovery(URL discoveryUrl) {
        return new AdminDiscovery(discoveryUrl);
    }
}
