package cn.huolala.arch.hermes.compatible.discovery;

import cn.huolala.arch.hermes.discovery.DiscoveryStrategy;
import cn.huolala.arch.hermes.discovery.ServiceInstance;
import cn.huolala.arch.hermes.discovery.event.DiscoveryEvent;
import cn.huolala.arch.hermes.discovery.event.DiscoveryListener;

import java.util.List;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;

public class AdminHealthListener {

    private final Set<DiscoveryListener> listeners = new CopyOnWriteArraySet<>();

    public Set<DiscoveryListener> getListeners() {
        return listeners;
    }

    public void addListener(DiscoveryListener discoveryListener) {
        this.listeners.add(discoveryListener);
    }

    public void removeListener(DiscoveryListener discoveryListener) {
        this.listeners.remove(discoveryListener);
    }

    public void getListenersAndOnEvent(String serviceName, List<ServiceInstance> serviceInstances) {
        listeners.forEach(discoveryListener
                -> discoveryListener.onEvent(new DiscoveryEvent(serviceName, serviceInstances, DiscoveryStrategy.Type.DOMAIN)));
    }
}
