package cn.huolala.arch.hermes.compatible.discovery;

import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.config.bootstrap.Bootstrap;
import cn.huolala.arch.hermes.discovery.Discovery;
import cn.huolala.arch.hermes.discovery.ServiceInstance;
import cn.huolala.arch.hermes.discovery.exception.DiscoveryException;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.spec.classification.ApiStability;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static cn.huolala.arch.hermes.discovery.core.AbstractDiscoveryFactory.getServiceDiscoveries;

/**
 * DiscoveryUtils for discovery compatible
 */
@ApiStability.Evolving
@ApiAudience.LimitedPrivate("CI")
public final class DiscoveryUtils {
    private DiscoveryUtils() {
    }

    private static final String SERVICE_TAG = "service";

    private static final String DISCOVERY_PROTOCOL = "discovery";

    private static final String PROTOCOL_PARAMETER = "protocol";

    private static final String DEFAULT_DISCOVERY = "consul";

    /**
     * get ServiceInstance
     */
    public static Optional<ServiceInstance> getServiceInstance() {
        return Optional.ofNullable(Bootstrap.getInstance().getServiceInstance());
    }

    /**
     * update ServiceInstance tags, ignore tag keys that don't start with hll(not case-sensitive)
     */
    public static void updateServiceInstanceTags(Map<String, String> wholeTags) {
        Bootstrap.getInstance().updateServiceInstanceTags(wholeTags);
    }

    /**
     * update ServiceInstance tags, ignore tag keys that don't start with hll(not case-sensitive) and register if the register is true
     *
     * @throws DiscoveryException if failed
     */
    public static void updateServiceInstanceTags(Map<String, String> wholeTags, boolean register) {
        Bootstrap.getInstance().updateServiceInstanceTags(wholeTags, register);
    }

    /**
     * Unregisters an instance of {@link ServiceInstance}.
     *
     * @throws DiscoveryException if failed
     */
    public static void unregisterServiceInstance() {
        Bootstrap.getInstance().unregisterServiceInstance();
    }

    /**
     * Registers an instance of {@link ServiceInstance}.
     *
     * @throws DiscoveryException if failed
     */
    public static void registerServiceInstance() {
        Bootstrap.getInstance().registerServiceInstance(true);
    }

    /**
     * Determine whether the current instance is registered
     */
    public static boolean isRegistered() {
        return getDiscovery().map(value -> value.isRegistered(Bootstrap.getInstance().getServiceInstance())).orElse(false);
    }

    /**
     * Gets the default protocol of {@link Discovery }
     *
     * @throws DiscoveryException if failed
     */
    public static Optional<Discovery> getDiscovery() throws DiscoveryException {
        return getDiscovery(d -> StringUtils.equals(d.getUrl().getProtocol(), DISCOVERY_PROTOCOL)
                && StringUtils.equals(d.getUrl().getParameter(PROTOCOL_PARAMETER), d.getUrl().getParameter(DISCOVERY_PROTOCOL, DEFAULT_DISCOVERY)));
    }

    /**
     * Gets the {@link Discovery } using predicate condition.
     *
     * @param predicate condition
     * @throws DiscoveryException if failed
     */
    public static Optional<Discovery> getDiscovery(Predicate<Discovery> predicate) throws DiscoveryException {
        return getServiceDiscoveries().stream().filter(predicate).findFirst();
    }

    /**
     * Gets all {@link ServiceInstance soa service instances} by the specified service name.
     *
     * @param serviceName {@link ServiceInstance#getServiceName()}
     * @throws DiscoveryException if failed
     */
    public static List<ServiceInstance> getInstances(String serviceName) throws DiscoveryException {
        return getInstances(serviceName, Lists.newArrayList(SERVICE_TAG));
    }


    /**
     * Gets all {@link ServiceInstance service instances} by the specified service name and tags.
     *
     * @param serviceName {@link ServiceInstance#getServiceName()}
     * @throws DiscoveryException if failed
     */
    public static List<ServiceInstance> getInstances(String serviceName, List<String> filterTags) throws DiscoveryException {
        Optional<Discovery> discovery = getDiscovery();
        if (!discovery.isPresent()) {
            throw new DiscoveryException("Unable to find Discovery matching the predicate:[" + getServiceDiscoveries().stream().map(Discovery::getUrl).collect(Collectors.toList()) + "]");
        }
        return discovery.get().getInstances(serviceName, filterTags);
    }

}
