package cn.huolala.arch.hermes.compatible.discovery.integration;

import cn.huolala.arch.hermes.api.config.HostPortsConfig;
import cn.huolala.arch.hermes.cluster.Cluster;
import cn.huolala.arch.hermes.cluster.directory.StaticDirectory;
import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.url.URLBuilder;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.protocol.JsonRpcInvocation;
import cn.huolala.arch.hermes.discovery.directory.DiscoveryDirectory;
import cn.huolala.arch.hermes.discovery.integration.DiscoveryInvoker;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Protocol;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import com.google.common.collect.Lists;

import java.util.Optional;
import java.util.concurrent.ExecutionException;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.REFER_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.CLUSTER_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.GENERIC_INVOKER_CACHE_SIZE;
import static cn.huolala.arch.hermes.common.constants.Constants.GENERIC_INVOKER_CACHE_SIZE_NAME;
import static cn.huolala.arch.hermes.common.constants.Constants.PROTOCOL_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.REMOTE_APPLICATION_KEY;
import static cn.huolala.arch.hermes.compatible.config.support.BeanDefinitionSupport.p2pConfig;
import static cn.huolala.arch.hermes.compatible.config.support.BeanDefinitionSupport.p2pEnabled;

public class GenericDiscoveryInvoker<T> extends DiscoveryInvoker<T> {

    /**
     * application DiscoveryInvoker
     */
    private final Cache<String, Invoker<T>> discoveryInvoker;


    public GenericDiscoveryInvoker(Protocol protocol, Class<T> type, URL url) {
        super(protocol, type, url);
        discoveryInvoker = CacheBuilder.newBuilder()
                .maximumSize(url.getParameter(GENERIC_INVOKER_CACHE_SIZE_NAME, GENERIC_INVOKER_CACHE_SIZE))
                .removalListener((RemovalListener<String, Invoker<T>>) notification -> notification.getValue().destroy())
                .build();
    }

    @Override
    protected void buildInvoker() {
        this.directory = new DiscoveryDirectory<>(type, discoveryUrl);
        this.directory.setProtocol(protocol);
        this.consumerUrl = this.directory.getConsumerUrl();

        Invoker<T> invoker = protocol.refer(getInterface(), consumerUrl.setProtocol(consumerUrl.getParameter(PROTOCOL_KEY)));
        this.invoker = Cluster.getCluster(discoveryUrl.getParameter(CLUSTER_KEY)).join(new StaticDirectory<>(Lists.newArrayList(invoker)));
    }


    @Override
    public Result invoke(Invocation invocation) throws RpcException {
        JsonRpcInvocation jsonRpcInvocation = (JsonRpcInvocation) invocation.getArguments()[0];
        if (jsonRpcInvocation == null) {
            throw new IllegalArgumentException("The JsonRpcInvocation must be used as a parameter in the GenericService class,invocation:[" + invocation + "]");
        }
        String application = jsonRpcInvocation.getApplication();
        if (!StringUtils.isEmptyApplication(application)) {
            try {
                return invoke(invocation, application);
            } catch (ExecutionException e) {
                throw RpcException.getRpcException(invocation.getInvoker().getUrl(), invocation, e);
            }
        }
        return super.invoke(invocation);
    }

    /**
     * Loaded from cache
     */
    private Result invoke(Invocation invocation, String application) throws ExecutionException {
        return discoveryInvoker.get(application, () -> {
            URL consumer = URLBuilder.from(consumerUrl).addParameter(REMOTE_APPLICATION_KEY, application).build();
            if (p2pEnabled()) {
                Optional<HostPortsConfig.HostPort> hostPortOptional = p2pConfig(application);
                if (hostPortOptional.isPresent()) {
                    HostPortsConfig.HostPort hostPort = hostPortOptional.get();
                    consumer = consumer.setProtocol(consumer.getParameter(PROTOCOL_KEY)).setHost(hostPort.getHost()).setPort(hostPort.getPort());
                    Invoker<T> invoker = protocol.refer(getInterface(), consumer);
                    return Cluster.getCluster(discoveryUrl.getParameter(CLUSTER_KEY)).join(new StaticDirectory<>(Lists.newArrayList(invoker)));
                }
            }
            return new DiscoveryInvoker<>(protocol, type, URLBuilder.from(discoveryUrl).addParameterAndEncoded(REFER_KEY, consumer.toFullString()).build());
        }).invoke(invocation);
    }


    @Override
    public void destroy() {
        discoveryInvoker.asMap().values().forEach(Invoker::destroy);
        super.destroy();
    }
}
