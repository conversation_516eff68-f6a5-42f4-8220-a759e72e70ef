package cn.huolala.arch.hermes.compatible.discovery.integration;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.extension.Wrapper;
import cn.huolala.arch.hermes.common.url.URLBuilder;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.discovery.integration.DiscoveryProtocol;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.GENERIC_PREFIX;

@Wrapper.Ignore
public class GenericDiscoveryProtocol extends DiscoveryProtocol {

    @Override
    public <T> Invoker<T> refer(Class<T> type, URL url) throws RpcException {
        URL discoveryUrl = URLBuilder.from(url).setProtocol(StringUtils.removeStart(url.getProtocol(), GENERIC_PREFIX)).build();
        return new GenericDiscoveryInvoker<>(protocol, type, discoveryUrl);
    }
}
