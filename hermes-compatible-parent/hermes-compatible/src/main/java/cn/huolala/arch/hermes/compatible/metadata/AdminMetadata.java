package cn.huolala.arch.hermes.compatible.metadata;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.event.EventListener;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.thread.NamedThreadFactory;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.JsonUtils;
import cn.huolala.arch.hermes.common.util.NetUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.util.http.HttpResult;
import cn.huolala.arch.hermes.compatible.util.http.HttpUtils;
import cn.huolala.arch.hermes.compatible.metadata.event.DynamicMetadataReportEvent;
import cn.huolala.arch.hermes.compatible.metadata.model.ApiMetadataModel;
import cn.huolala.arch.hermes.compatible.metadata.model.AppRuntimeEventModel;
import cn.huolala.arch.hermes.compatible.metadata.model.CommandKeyReportModel;
import cn.huolala.arch.hermes.compatible.metadata.model.ServiceDefinitionModel;
import cn.huolala.arch.hermes.metadata.event.AppRunTimeEvent;
import cn.huolala.arch.hermes.metadata.report.MetadataReport;
import cn.huolala.arch.hermes.metadata.store.InMemoryWritableMetadata;

import static cn.huolala.arch.hermes.common.constants.Constants.ADMIN_HOST_KEY;


public class AdminMetadata extends InMemoryWritableMetadata implements MetadataReport, EventListener<DynamicMetadataReportEvent> {

    private static final Logger logger = LoggerFactory.getLogger(AdminMetadata.class);

    private static final String ADMIN_COMMANDKEY_REPORT_URL = "http://%s/admin/management/service_report";

    //the following URL is equivalent to SOA-Admin
    private static final String EVENT_POST_URL = "http://%s/event/report";

    private static final String METADATA_REPORT_URL = "http://%s/metadata/report?instanceId=%s&appId=%s";

    private static final Map<String, ServiceDefinitionModel> DYNAMIC_REPORT_SERVICE_DEFINITION_CACHE = new ConcurrentHashMap<>();

    private final String domain = (String) ApplicationContext.getAttributes().getOrDefault(ADMIN_HOST_KEY, StringUtils.EMPTY_STRING);

    private final String appId = ApplicationContext.getApplicationConfig().getName();

    private final AtomicBoolean enabled = new AtomicBoolean(false);

    public AdminMetadata() {
        if (StringUtils.isNotBlank(domain)) {
            Executors.newSingleThreadScheduledExecutor(new NamedThreadFactory("dynamic service metadata export thread", true))
                    .scheduleAtFixedRate(this::timingReportCommandKey, 30, 30, TimeUnit.SECONDS);
            enabled.set(true);
        }
        logger.info("admin metadata enable：" + enabled.get() + "  admin's domain: " + domain);
    }


    private void report(Object data, String requestUrl) {
        if (enabled.get()) {
            try {
                HttpResult httpResult = HttpUtils.doPost(requestUrl, JsonUtils.toJSONString(data));
                if (httpResult.getCode() != 200) {
                    logger.warn("admin metadata report http request fail, " + "url:" + requestUrl + " response :"
                            + httpResult.getBody());
                }
            } catch (Exception e) {
                logger.warn("admin metadata report error " + "url: " + requestUrl + "message: " + e.getMessage());
            }
        }
    }


    /**
     * instanceId will just contain appId and address
     */
    private String buildInstanceId() {
        String host = NetUtils.getLocalHost();
        return appId + "-" + host.replace(".", "-");
    }


    private void timingReportCommandKey() {
        if (CollectionUtils.isNotEmptyMap(DYNAMIC_REPORT_SERVICE_DEFINITION_CACHE)) {
            Set<ServiceDefinitionModel> tempServiceDefinitionModels = new HashSet<>(DYNAMIC_REPORT_SERVICE_DEFINITION_CACHE.values());
            DYNAMIC_REPORT_SERVICE_DEFINITION_CACHE.clear();
            CommandKeyReportModel commandKeyReportModel = new CommandKeyReportModel();
            commandKeyReportModel.setAppId(appId);
            commandKeyReportModel.setEnv(ApplicationContext.getApplicationConfig().getEnvironment());
            commandKeyReportModel.setServices(tempServiceDefinitionModels);
            report(commandKeyReportModel, String.format(ADMIN_COMMANDKEY_REPORT_URL, domain));
        }
    }


    @Override
    public <T> void appMetadata(T metadata) {
        logger.info("report metadata to admin");
        report(metadata, String.format(METADATA_REPORT_URL, domain, buildInstanceId(), appId));
        List<ServiceDefinitionModel> serviceDefinitionModels = new ArrayList<>();
        ExtensionLoader.getExtensionLoader(ApiMetadataModel.MetadataContainer.class).getSupportedExtensionInstances()
                .forEach(metadataContainer -> metadataContainer.collectCommand(serviceDefinitionModels, appId));
        CommandKeyReportModel commandKeyReportModel = new CommandKeyReportModel();
        commandKeyReportModel.setAppId(appId);
        commandKeyReportModel.setEnv(ApplicationContext.getApplicationConfig().getEnvironment());
        commandKeyReportModel.setServices(new HashSet<>(serviceDefinitionModels));
        report(commandKeyReportModel, String.format(ADMIN_COMMANDKEY_REPORT_URL, domain));
    }


    @Override
    public <T> void appRuntimeEvent(AppRunTimeEvent<T> appRunTimeEvent) {
        AppRuntimeEventModel eventModel = new AppRuntimeEventModel();
        eventModel.setAppId(appId);
        eventModel.setType(appRunTimeEvent.getType().toString());
        eventModel.setCreateTime(appRunTimeEvent.getCreateTime());
        eventModel.setInstanceId(buildInstanceId());
        try {
            eventModel.setData(JsonUtils.toJSONString(appRunTimeEvent.getData()));
            report(eventModel, String.format(EVENT_POST_URL, domain));
        } catch (IOException e) {
            logger.warn("app runtime event: " + appRunTimeEvent.getType().name() + " report error: ", e);
        }
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public void onEvent(DynamicMetadataReportEvent event) {
        List<ServiceDefinitionModel> serviceDefinitionModels = (List<ServiceDefinitionModel>) event.getSource();
        serviceDefinitionModels.forEach(item -> DYNAMIC_REPORT_SERVICE_DEFINITION_CACHE.putIfAbsent(item.getName(), item));
    }
}
