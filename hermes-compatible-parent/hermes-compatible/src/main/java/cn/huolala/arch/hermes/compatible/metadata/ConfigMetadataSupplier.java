package cn.huolala.arch.hermes.compatible.metadata;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.metadata.CacheableMetadataSupplier;
import cn.huolala.arch.hermes.common.url.URLStrParser;
import cn.huolala.arch.hermes.compatible.metadata.model.ApiMetadataModel;
import cn.huolala.arch.hermes.compatible.metadata.model.AppMetadataModel;
import cn.huolala.arch.hermes.metadata.WritableMetadata;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static cn.huolala.arch.hermes.common.constants.Constants.PROTOCOL_KEY;

public class ConfigMetadataSupplier extends CacheableMetadataSupplier {

    private static final WritableMetadata metadata = WritableMetadata.getDefaultExtension();

    @Override
    public String getDesc() {
        return "配置参数和环境参数";
    }

    @Override
    public Map<String, Object> getCacheableMetadata() {
        AppMetadataModel app = AppMetadataModel.build();
        metadata.getSubscribedURLs().forEach(urlString -> {
            URL url = URLStrParser.parseDecodedStr(urlString);
            ExtensionLoader.getExtensionLoader(ApiMetadataModel.MetadataContainer.class)
                    .getActivateExtension(url, PROTOCOL_KEY).forEach(metadataContainer -> metadataContainer.addConsumer(url));
        });
        metadata.getExportedURLs().forEach(urlString -> {
            URL url = URLStrParser.parseDecodedStr(urlString);
            Optional.ofNullable(ExtensionLoader.getExtensionLoader(ApiMetadataModel.MetadataContainer.class)
                    .getExtension(url.getProtocol())).ifPresent(metadataContainer -> metadataContainer.addProvider(url));
        });
        Map<String, Object> metadataReport = new HashMap<>();
        ExtensionLoader.getExtensionLoader(ApiMetadataModel.MetadataContainer.class).getSupportedExtensionInstances()
                .forEach(metadataContainer -> metadataReport.put(metadataContainer.apiMetadataKey(), metadataContainer));
        metadataReport.put("app", app);
        return metadataReport;
    }

}
