package cn.huolala.arch.hermes.compatible.metadata.event.listener;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import cn.huolala.arch.hermes.common.event.EventDispatcher;
import cn.huolala.arch.hermes.common.event.EventListener;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.compatible.metadata.event.DynamicMetadataReportEvent;
import cn.huolala.arch.hermes.compatible.metadata.model.ServiceDefinitionModel;
import cn.huolala.arch.hermes.metadata.event.MetadataEvent;


public abstract class AbstractDynamicMetadataEventListener<T extends MetadataEvent> implements EventListener<T> {


    private static final Map<String, ServiceDefinitionModel> DYNAMIC_SERVICE_DEFINITION = new ConcurrentHashMap<>();

    private static final int MAX_DYNAMIC_NUM = 2000;

    @Override
    public void onEvent(T event) {
        List<ServiceDefinitionModel> serviceDefinitionModels = doOnEvent(event);
        if (DYNAMIC_SERVICE_DEFINITION.size() < MAX_DYNAMIC_NUM) {
            List<ServiceDefinitionModel> serviceDefinitionModelReport = new ArrayList<>();
            serviceDefinitionModels.forEach(serviceDefinitionModel -> DYNAMIC_SERVICE_DEFINITION.computeIfAbsent(serviceDefinitionModel.getName(), key -> {
                serviceDefinitionModelReport.add(serviceDefinitionModel);
                return serviceDefinitionModel;
            }));
            if (CollectionUtils.isNotEmpty(serviceDefinitionModelReport)) {
                EventDispatcher.getDefaultExtension().dispatch(new DynamicMetadataReportEvent(serviceDefinitionModelReport));
            }
        }
    }

    protected abstract List<ServiceDefinitionModel> doOnEvent(T event);
}
