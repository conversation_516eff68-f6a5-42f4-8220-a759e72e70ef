package cn.huolala.arch.hermes.compatible.metadata.event.listener;


import cn.huolala.arch.hermes.common.event.EventListener;
import cn.huolala.arch.hermes.metadata.event.MetadataEvent;
import cn.huolala.arch.hermes.metadata.report.MetadataReport;

public abstract class AbstractMetadataEventListener<T extends MetadataEvent> implements EventListener<T> {

    protected MetadataReport metadataReport;

    public AbstractMetadataEventListener() {
        this.metadataReport = MetadataReport.getDefaultExtension();
    }
}
