package cn.huolala.arch.hermes.compatible.metadata.event.listener;

import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.metadata.MetadataSupplier;
import cn.huolala.arch.hermes.metadata.event.AppRunTimeEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static cn.huolala.arch.hermes.metadata.event.AppRunTimeEvent.EVENT_TYPE_KEY;

@SuppressWarnings({"unchecked", "rawtypes"})
public class AppRunTimeEventListener extends AbstractMetadataEventListener<AppRunTimeEvent> {

    private static final Logger logger = LoggerFactory.getLogger(AppRunTimeEventListener.class);

    public static final String METADATA_CONFIG = "config";

    @Override
    public void onEvent(AppRunTimeEvent appRunTimeEvent) {
        AppRunTimeEvent.EventType type = appRunTimeEvent.getType();
        if (type == AppRunTimeEvent.EventType.LIFE
                && Objects.equals(((Map<String, Object>) appRunTimeEvent.getData()).get(EVENT_TYPE_KEY),
                AppRunTimeEvent.EventType.LifeSubEventType.START.toString())) {
            metadataReport.appMetadata(metadataReportContent());
        }
        metadataReport.appRuntimeEvent(appRunTimeEvent);
    }

    public static Map<String, Object> metadataReportContent() {
        try {
            MetadataSupplier metadataSupplier = ExtensionLoader.getExtensionLoader(MetadataSupplier.class).getExtension(METADATA_CONFIG);
            return metadataSupplier.get();
        } catch (Throwable e) {
            logger.error("AppRunTimeEventListener exception", e);
        }
        return new HashMap<>();
    }
}
