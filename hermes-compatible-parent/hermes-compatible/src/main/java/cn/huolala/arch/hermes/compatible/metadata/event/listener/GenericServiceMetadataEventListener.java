package cn.huolala.arch.hermes.compatible.metadata.event.listener;

import java.util.Collections;
import java.util.List;

import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.compatible.metadata.event.GenericServiceMetadataEvent;
import cn.huolala.arch.hermes.compatible.metadata.model.ServiceDefinitionModel;
import cn.huolala.arch.hermes.compatible.protocol.JsonRpcInvocation;

public class GenericServiceMetadataEventListener extends AbstractDynamicMetadataEventListener<GenericServiceMetadataEvent> {


    @Override
    protected List<ServiceDefinitionModel> doOnEvent(GenericServiceMetadataEvent genericServiceMetadataEvent) {
        JsonRpcInvocation jsonRpcInvocation = (JsonRpcInvocation) genericServiceMetadataEvent.getSource();
        String commandKey = jsonRpcInvocation.getCommandKey();
        ServiceDefinitionModel serviceDefinitionModel = new ServiceDefinitionModel();
        serviceDefinitionModel.setAppId(ApplicationContext.getApplicationConfig().getName());
        serviceDefinitionModel.setName(commandKey);
        serviceDefinitionModel.setReportFrom(ServiceDefinitionModel.ReportFromEnum.GENERIC);
        serviceDefinitionModel.setSource(ServiceDefinitionModel.ServiceSource.consumer);
        return Collections.singletonList(serviceDefinitionModel);
    }
}

