package cn.huolala.arch.hermes.compatible.metadata.event.listener;

import java.util.ArrayList;
import java.util.List;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.compatible.metadata.model.ApiMetadataModel;
import cn.huolala.arch.hermes.compatible.metadata.model.ServiceDefinitionModel;
import cn.huolala.arch.hermes.config.bootstrap.Bootstrap;
import cn.huolala.arch.hermes.metadata.event.ReferenceMetadataEvent;

import static cn.huolala.arch.hermes.common.constants.Constants.PROTOCOL_KEY;

public class ReferenceMetadataEventListener extends AbstractDynamicMetadataEventListener<ReferenceMetadataEvent> {


    @Override
    protected List<ServiceDefinitionModel> doOnEvent(ReferenceMetadataEvent referenceMetadataEvent) {
        List<ServiceDefinitionModel> serviceDefinitionModels = new ArrayList<>();
        URL url = (URL) referenceMetadataEvent.getSource();

        if (Bootstrap.getInstance().isReady()) {
            ExtensionLoader.getExtensionLoader(ApiMetadataModel.MetadataContainer.class).getActivateExtension(url, PROTOCOL_KEY)
                    .forEach(metadataContainer -> metadataContainer.collectConsumerCommand(url, serviceDefinitionModels));
        } else {
            ExtensionLoader.getExtensionLoader(ApiMetadataModel.MetadataContainer.class)
                    .getActivateExtension(url, PROTOCOL_KEY).forEach(metadataContainer -> metadataContainer.addConsumer(url));
        }
        return serviceDefinitionModels;
    }
}
