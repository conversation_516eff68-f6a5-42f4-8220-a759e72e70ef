package cn.huolala.arch.hermes.compatible.metadata.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.protocol.IJsonRpcGenericService;

public final class ApiMetadataModel implements Serializable {

    private ApiMetadataModel() {
    }

    public static class JsonRpcServiceMetadataContainer implements MetadataContainer {

        private final Map<String, JsonRpcServiceMetadataModel> consumer = new HashMap<>();

        private final Map<String, JsonRpcServiceMetadataModel> provider = new HashMap<>();

        private final Map<String, JsonRpcServiceMetadataModel> export = new HashMap<>();

        private final Map<String, JsonRpcServiceMetadataModel> remote = new HashMap<>();

        public Map<String, JsonRpcServiceMetadataModel> getConsumer() {
            return consumer;
        }

        public Map<String, JsonRpcServiceMetadataModel> getProvider() {
            return provider;
        }

        public Map<String, JsonRpcServiceMetadataModel> getExport() {
            return export;
        }

        public Map<String, JsonRpcServiceMetadataModel> getRemote() {
            return remote;
        }

        @Override
        public String apiMetadataKey() {
            return "api";
        }

        @Override
        public void addConsumer(URL url) {
            if (!StringUtils.equals(IJsonRpcGenericService.class.getName(), url.getServiceInterface())) {
                getConsumer().computeIfAbsent(url.getServiceKey(), serviceKey -> JsonRpcServiceMetadataModel.buildConsumerServiceDefinition(url));
            }
        }

        @Override
        public void addProvider(URL url) {
            getProvider().computeIfAbsent(url.getServiceKey(), serviceKey
                    -> JsonRpcServiceMetadataModel.buildProviderServiceDefinition(url));
        }

        @Override
        public void collectConsumerCommand(URL url, List<ServiceDefinitionModel> serviceDefinitionModels) {
            JsonRpcServiceMetadataModel.buildConsumerServiceDefinition(url).convertServiceDefinition(serviceDefinitionModels,
                    ServiceDefinitionModel.ServiceSource.consumer, ApplicationContext.getApplicationConfig().getName());
        }

        @Override
        public void collectCommand(List<ServiceDefinitionModel> serviceDefinitionModels, String appId) {
            getConsumer().values().forEach(jsonRpcServiceMetadataModel ->
                    jsonRpcServiceMetadataModel.convertServiceDefinition(serviceDefinitionModels, ServiceDefinitionModel.ServiceSource.consumer, appId));
            getProvider().values().forEach(jsonRpcServiceMetadataModel ->
                    jsonRpcServiceMetadataModel.convertServiceDefinition(serviceDefinitionModels, ServiceDefinitionModel.ServiceSource.provider, appId));
        }
    }


    public static class GrpcServiceMetadataContainer implements MetadataContainer {

        private final Map<String, GrpcServiceMetadataModel> consumer = new HashMap<>();

        private final Map<String, GrpcServiceMetadataModel> provider = new HashMap<>();

        public Map<String, GrpcServiceMetadataModel> getConsumer() {
            return consumer;
        }

        public Map<String, GrpcServiceMetadataModel> getProvider() {
            return provider;
        }

        @Override
        public String apiMetadataKey() {
            return "apiGrpc";
        }

        @Override
        public void addConsumer(URL url) {
            getConsumer().computeIfAbsent(url.getServiceKey(), serviceKey -> GrpcServiceMetadataModel.buildConsumerServiceDefinition(url));
        }

        @Override
        public void addProvider(URL url) {
            getProvider().computeIfAbsent(url.getServiceKey(), serviceKey -> GrpcServiceMetadataModel.buildProviderServiceDefinition(url));
        }

        @Override
        public void collectConsumerCommand(URL url, List<ServiceDefinitionModel> serviceDefinitionModels) {
            GrpcServiceMetadataModel.buildConsumerServiceDefinition(url).convertServiceDefinition(serviceDefinitionModels,
                    ServiceDefinitionModel.ServiceSource.consumer, ApplicationContext.getApplicationConfig().getName());
        }

        @Override
        public void collectCommand(List<ServiceDefinitionModel> serviceDefinitionModels, String appId) {
            getConsumer().values().forEach(grpcServiceMetadataModel ->
                    grpcServiceMetadataModel.convertServiceDefinition(serviceDefinitionModels, ServiceDefinitionModel.ServiceSource.consumer, appId));
            getProvider().values().forEach(grpcServiceMetadataModel ->
                    grpcServiceMetadataModel.convertServiceDefinition(serviceDefinitionModels, ServiceDefinitionModel.ServiceSource.provider, appId));
        }
    }


    @SPI
    public interface MetadataContainer {

        String apiMetadataKey();

        void addConsumer(URL url);

        void addProvider(URL url);

        void collectConsumerCommand(URL url, List<ServiceDefinitionModel> serviceDefinitionModels);

        void collectCommand(List<ServiceDefinitionModel> serviceDefinitionModels, String appId);

    }
}

