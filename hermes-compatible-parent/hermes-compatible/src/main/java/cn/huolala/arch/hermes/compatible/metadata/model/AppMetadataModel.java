package cn.huolala.arch.hermes.compatible.metadata.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.context.ConfigManager;

import static java.util.Optional.of;

public class AppMetadataModel implements Serializable {

    /**
     * app
     */
    private final Map<String, Object> rpcConfig = new HashMap<>();

    /**
     * system
     */
    private final Map<Object, Object> systemConfig = new HashMap<>();


    private final Map<Object, Object> systemEnv = new HashMap<>();


    public void addRpcConfig(String configType, Object config) {
        rpcConfig.put(configType, config);
    }


    public Map<Object, Object> getSystemConfig() {
        return systemConfig;
    }


    public Map<Object, Object> getSystemEnv() {
        return systemEnv;
    }

    public Map<String, Object> getRpcConfig() {
        return rpcConfig;
    }

    public static AppMetadataModel build() {
        final AppMetadataModel appDefinition = new AppMetadataModel();
        appDefinition.getSystemConfig().putAll(System.getProperties());
        appDefinition.getSystemEnv().putAll(System.getenv());
        ConfigManager manager = ApplicationContext.getConfigManager();
        manager.getProvider().ifPresent(providerConfig -> appDefinition.addRpcConfig("provider", providerConfig));
        manager.getDiscovery().ifPresent(discoveryConfig -> appDefinition.addRpcConfig("discovery", discoveryConfig));
        manager.getApplication().ifPresent(applicationConfig -> appDefinition.addRpcConfig("application", applicationConfig));
        manager.getConsumer().ifPresent(consumerConfig -> appDefinition.addRpcConfig("consumer", consumerConfig));
        manager.getConfigCenter().ifPresent(configCenterConfig -> appDefinition.addRpcConfig("configCenter", configCenterConfig));
        of(manager.getConsumerProtocols()).ifPresent(consumerProtocolConfig -> appDefinition.addRpcConfig("consumerProtocol", consumerProtocolConfig));
        manager.getHostPortConfig().ifPresent(hostPortConfig -> appDefinition.addRpcConfig("hostPort", hostPortConfig));
        return appDefinition;
    }
}

