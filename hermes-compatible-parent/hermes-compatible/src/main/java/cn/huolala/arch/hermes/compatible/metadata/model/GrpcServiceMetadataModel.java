package cn.huolala.arch.hermes.compatible.metadata.model;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.context.model.MethodModel;
import cn.huolala.arch.hermes.common.context.model.ServiceModel;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;

import java.io.Serializable;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.huolala.arch.hermes.common.constants.Constants.*;


public class GrpcServiceMetadataModel implements Serializable {

    /**
     * The name of serviceInterface name
     */
    private String serviceName;


    private String url;

    /**
     * method
     */
    private List<MethodMetadata> methodDefinitionMetadata;


    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public List<MethodMetadata> getMethodDefinitionMetadata() {
        return methodDefinitionMetadata;
    }

    public void setMethodDefinitionMetadata(List<MethodMetadata> methodDefinitionMetadata) {
        this.methodDefinitionMetadata = methodDefinitionMetadata;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }


    public static GrpcServiceMetadataModel buildConsumerServiceDefinition(URL url) {
        GrpcServiceMetadataModel serviceMetadata = new GrpcServiceMetadataModel();
        serviceMetadata.setUrl(url.toFullString());
        Optional.ofNullable(ApplicationContext.getConsumerModel(url.getServiceKey()))
                .ifPresent(consumerModel -> buildServiceDefinition(serviceMetadata, consumerModel.getServiceModel(), consumerModel::getCommandKey));
        return serviceMetadata;
    }

    public static GrpcServiceMetadataModel buildProviderServiceDefinition(URL url) {
        GrpcServiceMetadataModel serviceMetadata = new GrpcServiceMetadataModel();
        serviceMetadata.setUrl(url.toFullString());
        Optional.ofNullable(ApplicationContext.getProviderModel(url.getServiceKey()))
                .ifPresent(providerModel -> buildServiceDefinition(serviceMetadata, providerModel.getServiceModel(), providerModel::getCommandKey));
        return serviceMetadata;
    }


    public static GrpcServiceMetadataModel buildServiceDefinition(GrpcServiceMetadataModel serviceMetadata, ServiceModel serviceModel, Function<Method, String> commandKeyFunction) {
        Optional.ofNullable(serviceModel).ifPresent(serviceModel1 -> {
            serviceMetadata.setServiceName(serviceModel1.getServiceName());
            List<MethodMetadata> methodMetadata =
                    Arrays.stream(serviceModel1.getServiceInterfaceClass().getMethods()).map(method -> {
                        MethodModel methodModel = serviceModel1.getMethod(method);
                        if (Optional.ofNullable(methodModel).isPresent()) {
                            MethodMetadata methodMD = buildMethodDefinition(methodModel);
                            methodMD.setCommandKey(commandKeyFunction.apply(method));
                            return methodMD;
                        }
                        return null;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
            serviceMetadata.setMethodDefinitionMetadata(methodMetadata);
        });
        return serviceMetadata;
    }


    private static MethodMetadata buildMethodDefinition(MethodModel methodModel) {
        MethodMetadata methodMetadata = new MethodMetadata();
        String methodName = methodModel.getMethodName();
        methodMetadata.setMethodName(methodName);
        methodMetadata.setParamDesc(methodModel.getParamDesc());
        methodMetadata.setParameterClasses(methodModel.getParameterClasses());
        methodMetadata.setReturnClass(methodModel.getReturnClass());
        return methodMetadata;
    }

    public void convertServiceDefinition(List<ServiceDefinitionModel> serviceDefinitionModels,
                                         ServiceDefinitionModel.ServiceSource serviceSource,
                                         String appId) {
        if (CollectionUtils.isEmpty(getMethodDefinitionMetadata())) {
            return;
        }
        for (MethodMetadata methodDefinition : getMethodDefinitionMetadata()) {
            ServiceDefinitionModel serviceDefinitionModel = new ServiceDefinitionModel();
            serviceDefinitionModel.setAppId(appId);
            serviceDefinitionModel.setName(methodDefinition.getCommandKey());
            serviceDefinitionModel.setSource(serviceSource);
            //It is sufficient to ensure that the commandKey is correct，the rest can be ignored
            serviceDefinitionModel.setVersion("");
            serviceDefinitionModel.setDescription("");
            serviceDefinitionModel.setModuleName("");
            serviceDefinitionModel.setReportFrom(ServiceDefinitionModel.ReportFromEnum.STANDARD);
            serviceDefinitionModels.add(serviceDefinitionModel);
        }
    }

    public static class MethodMetadata implements Serializable {

        private String methodName;

        private String paramDesc;

        private Class<?>[] parameterClasses;

        private Class<?> returnClass;

        /**
         * The governance of commandKey
         */
        private String commandKey;


        public String getMethodName() {
            return methodName;
        }

        public void setMethodName(String methodName) {
            this.methodName = methodName;
        }

        public String getParamDesc() {
            return paramDesc;
        }

        public void setParamDesc(String paramDesc) {
            this.paramDesc = paramDesc;
        }

        public Class<?>[] getParameterClasses() {
            return parameterClasses;
        }

        public void setParameterClasses(Class<?>[] parameterClasses) {
            this.parameterClasses = parameterClasses;
        }

        public Class<?> getReturnClass() {
            return returnClass;
        }

        public void setReturnClass(Class<?> returnClass) {
            this.returnClass = returnClass;
        }

        public String getCommandKey() {
            return commandKey;
        }

        public void setCommandKey(String commandKey) {
            this.commandKey = commandKey;
        }

    }

    private static String getRemoteApplication(URL url) {
        String appId = StringUtils.EMPTY_STRING;
        if (url.getParameter(SIDE_KEY).equals(CONSUMER)) {
            appId = url.getParameter(REMOTE_APPLICATION_KEY);
        }
        return appId;
    }
}

