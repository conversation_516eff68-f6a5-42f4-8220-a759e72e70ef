package cn.huolala.arch.hermes.compatible.metadata.model;

import cn.huolala.arch.hermes.api.config.MethodConfig;
import cn.huolala.arch.hermes.api.config.ReferenceConfigBase;
import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.context.model.ConsumerModel;
import cn.huolala.arch.hermes.common.context.model.ProviderModel;
import cn.huolala.arch.hermes.common.context.model.ServiceModel;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.config.support.BeanDefinitionSupport;
import cn.huolala.arch.hermes.compatible.config.support.JsonRpcAttributeMetadata;

import cn.lalaframework.soa.annotation.SOAService;

import java.io.Serializable;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.IS_HTTPS;


public class JsonRpcServiceMetadataModel implements Serializable {

    /**
     * The name of serviceInterface name
     */
    private String serviceName;

    /**
     * service interface path
     */
    private String servicePath;

    /**
     * service host
     */
    private String host;

    /**
     * port
     */
    private Integer port;

    /**
     * service module
     */
    private String module;

    /**
     * ssl
     */
    private boolean ssl;

    private String url;

    /**
     * method
     */
    private List<MethodMetadata> methodDefinitionMetadata;


    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getServicePath() {
        return servicePath;
    }

    public void setServicePath(String servicePath) {
        this.servicePath = servicePath;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public boolean isSsl() {
        return ssl;
    }

    public void setSsl(boolean ssl) {
        this.ssl = ssl;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public List<MethodMetadata> getMethodDefinitionMetadata() {
        return methodDefinitionMetadata;
    }

    public void setMethodDefinitionMetadata(List<MethodMetadata> methodDefinitionMetadata) {
        this.methodDefinitionMetadata = methodDefinitionMetadata;
    }

    public static JsonRpcServiceMetadataModel buildConsumerServiceDefinition(URL url) {
        JsonRpcServiceMetadataModel serviceMetadata = new JsonRpcServiceMetadataModel();
        ConsumerModel consumerModel = ApplicationContext.getConsumerModel(url.getServiceKey());
        Optional.ofNullable(consumerModel).ifPresent(cModel -> {
            serviceMetadata.setUrl(url.toFullString());
            serviceMetadata.setServiceName(cModel.getServiceModel().getServiceName());
            ReferenceConfigBase<?> referenceConfig = cModel.getReferenceConfig();
            List<MethodConfig> methodConfigs = referenceConfig.getMethods();
            serviceMetadata.setServicePath(referenceConfig.getPath());
            Optional.of(url.getPort()).ifPresent(value -> serviceMetadata.setPort(url.getPort()));
            Optional.ofNullable(url.getHost()).ifPresent(value -> serviceMetadata.setHost(url.getHost()));
            serviceMetadata.setSsl(url.getParameter(IS_HTTPS, false));
            List<MethodMetadata> methodMetadata = new ArrayList<>();
            if (Objects.nonNull(methodConfigs)) {
                for (Method method : cModel.getServiceModel().getServiceInterfaceClass().getMethods()) {
                    methodConfigs.stream().filter(config -> config.getName().equals(method.getName())).findFirst().ifPresent(
                        config -> {
                            MethodMetadata mdMetadata = new MethodMetadata();
                            mdMetadata.setCommandKey(config.getCommandKey());
                            mdMetadata.setMethodName(config.getName());
                            mdMetadata.setConnectionTimeout(config.getConnectionTimeout());
                            mdMetadata.setMethodPath(config.getPath());
                            mdMetadata.setParamsType(cModel.getMethodModel(method).getParameterClasses());
                            mdMetadata.setDescription(config.getDescription());
                            mdMetadata.setOneWay(config.getOneWay());
                            mdMetadata.setReadTimeout(config.getTimeout());
                            mdMetadata.setVersion(referenceConfig.getVersion());
                            mdMetadata.setExtensionParameters(config.getAttributes());
                            methodMetadata.add(mdMetadata);
                        });
                }
            }
            serviceMetadata.setMethodDefinitionMetadata(methodMetadata);
        });
        return serviceMetadata;
    }


    public static JsonRpcServiceMetadataModel buildProviderServiceDefinition(URL url) {
        JsonRpcServiceMetadataModel serviceMetadata = new JsonRpcServiceMetadataModel();
        ProviderModel providerModel = ApplicationContext.getProviderModel(url.getServiceKey());
        Optional.ofNullable(providerModel).ifPresent(pModel -> {
            ServiceModel serviceModel = pModel.getServiceModel();
            String appId;
            serviceMetadata.setUrl(url.toFullString());
            serviceMetadata.setServiceName(serviceModel.getServiceName());
            if (Optional.ofNullable(serviceModel.getServiceInterfaceClass().getAnnotation(SOAService.class)).isPresent()) {
                SOAService soaService = serviceModel.getServiceInterfaceClass().getAnnotation(SOAService.class);
                serviceMetadata.setServicePath(soaService.value());
                serviceMetadata.setSsl(soaService.isHttps());
                appId = soaService.appId();
                Optional.ofNullable(soaService.appPort()).map(s -> {
                    if (StringUtils.isNotEmpty(s)) {
                        try {
                            return Integer.parseInt(s);
                        } catch (Exception ignore) {
                        }
                    }
                    return null;
                }).ifPresent(serviceMetadata::setPort);
                serviceMetadata.setHost(soaService.appHost());
                serviceMetadata.setModule(soaService.moduleName());
            } else {
                Stream.of("value", "path", "defaultPath")
                        .map(name -> url.getParameter(name, ""))
                        .filter(StringUtils::isNotBlank)
                        .findFirst().ifPresent(serviceMetadata::setServicePath);
                appId = ApplicationContext.getName();
            }
            List<MethodMetadata> methodMetadataList = new ArrayList<>();
            Arrays.stream(serviceModel.getServiceInterfaceClass().getMethods()).forEach(method -> {
                JsonRpcAttributeMetadata attributeMetadata = BeanDefinitionSupport.attributeMetadata(appId,
                        serviceMetadata.getServicePath(), method);
                MethodMetadata md = new MethodMetadata();
                md.setExtensionParameters(attributeMetadata.getAttributes());
                md.setMethodName(method.getName());
                md.setReadTimeout(attributeMetadata.getReadTimeoutMillis());
                md.setConnectionTimeout(attributeMetadata.getConnectionTimeoutMillis());
                md.setMethodPath(attributeMetadata.getMethodPath());
                md.setOneWay(attributeMetadata.isOneway());
                md.setDescription(attributeMetadata.getDescription());
                md.setParamsType(method.getParameterTypes());
                md.setCommandKey(providerModel.getCommandKey(method));
                methodMetadataList.add(md);
            });
            serviceMetadata.setMethodDefinitionMetadata(methodMetadataList);
        });
        return serviceMetadata;
    }


    public void convertServiceDefinition(List<ServiceDefinitionModel> serviceDefinitionModels, ServiceDefinitionModel.ServiceSource serviceSource, String appId) {
        if (CollectionUtils.isEmpty(getMethodDefinitionMetadata())) {
            return;
        }
        for (MethodMetadata methodDefinition : getMethodDefinitionMetadata()) {
            ServiceDefinitionModel serviceDefinitionModel = new ServiceDefinitionModel();
            serviceDefinitionModel.setAppId(appId);
            //It is sufficient to ensure that the commandKey is correct，the rest can be ignored
            serviceDefinitionModel.setName(methodDefinition.getCommandKey());
            serviceDefinitionModel.setSource(serviceSource);
            serviceDefinitionModel.setVersion(methodDefinition.getVersion());
            serviceDefinitionModel.setDescription(methodDefinition.getDescription());
            serviceDefinitionModel.setModuleName(this.getModule());
            serviceDefinitionModel.setReportFrom(ServiceDefinitionModel.ReportFromEnum.STANDARD);
            serviceDefinitionModels.add(serviceDefinitionModel);
        }
    }

    public static class MethodMetadata implements Serializable {
        /**
         * The method of name
         */
        private String methodName;

        /**
         * if SOAMethod annotation has the value will be replaced methodName
         * <p>
         * default methodName
         */
        private String methodPath;

        /**
         * connectionTimeout
         * default 1000ms
         */
        private Integer connectionTimeout;

        /**
         * readTimeout
         * default 6000ms
         */
        private Integer readTimeout;

        /**
         * The governance of commandKey
         */
        private String commandKey;

        /**
         * The governance of commandKey
         */
        private Object[] paramsType;

        /**
         * oneWay no mind about the result of request ,default is false
         */
        private boolean oneWay;

        /**
         * The serviceInterface version
         */
        private String version;

        /**
         * the serviceInterface of description
         */
        private String description;

        /**
         * extensionParameters
         */
        private Map<String, Object> extensionParameters = new HashMap<>();

        public String getMethodName() {
            return methodName;
        }

        public void setMethodName(String methodName) {
            this.methodName = methodName;
        }

        public String getMethodPath() {
            return methodPath;
        }

        public void setMethodPath(String methodPath) {
            this.methodPath = methodPath;
        }

        public Integer getConnectionTimeout() {
            return connectionTimeout;
        }

        public void setConnectionTimeout(Integer connectionTimeout) {
            this.connectionTimeout = connectionTimeout;
        }

        public Integer getReadTimeout() {
            return readTimeout;
        }

        public void setReadTimeout(Integer readTimeout) {
            this.readTimeout = readTimeout;
        }

        public String getCommandKey() {
            return commandKey;
        }

        public void setCommandKey(String commandKey) {
            this.commandKey = commandKey;
        }

        public Object[] getParamsType() {
            return paramsType;
        }

        public void setParamsType(Object[] paramsType) {
            this.paramsType = paramsType;
        }

        public boolean isOneWay() {
            return oneWay;
        }

        public void setOneWay(boolean oneWay) {
            this.oneWay = oneWay;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public Map<String, Object> getExtensionParameters() {
            return extensionParameters;
        }

        public void setExtensionParameters(Map<String, Object> extensionParameters) {
            this.extensionParameters = extensionParameters;
        }
    }
}

