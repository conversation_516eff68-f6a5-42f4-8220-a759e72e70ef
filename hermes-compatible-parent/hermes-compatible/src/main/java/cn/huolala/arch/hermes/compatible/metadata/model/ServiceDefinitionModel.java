package cn.huolala.arch.hermes.compatible.metadata.model;

import java.io.Serializable;

public class ServiceDefinitionModel implements Serializable {

    private ServiceSource source = ServiceSource.consumer;

    private String appId;

    private String name;

    private String version;

    private String description;

    private String moduleName;

    private ReportFromEnum reportFrom = ReportFromEnum.STANDARD;

    public ServiceDefinitionModel() {
    }

    public ReportFromEnum getReportFrom() {
        return reportFrom;
    }

    public void setReportFrom(ReportFromEnum reportFrom) {
        this.reportFrom = reportFrom;
    }

    public String getNameVersion() {
        return this.name + this.version;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public ServiceSource getSource() {
        return source;
    }

    public void setSource(ServiceSource source) {
        this.source = source;
    }

    public enum ServiceSource {
        provider, consumer
    }

    public enum ReportFromEnum {
        EASYOPEN,
        GENERIC,
        STANDARD
    }
}
