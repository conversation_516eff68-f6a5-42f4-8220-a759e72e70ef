package cn.huolala.arch.hermes.compatible.protocol;

import cn.huolala.arch.hermes.common.context.ApplicationContext;

public interface IJsonRpcGenericService {

    /**
     * generic invoke
     */
    <R> R invoke(JsonRpcInvocation invocation) throws Throwable;

    /**
     * static invoker
     */
    static <R> R invoker(JsonRpcInvocation invocation) throws Throwable {
        String genericServiceName = IJsonRpcGenericService.class.getName();
        org.springframework.context.ApplicationContext applicationContext =
                (org.springframework.context.ApplicationContext) ApplicationContext.getAttributes().get(org.springframework.context.ApplicationContext.class.getName());
        return ((IJsonRpcGenericService) applicationContext.getBean(genericServiceName)).invoke(invocation);
    }
}
