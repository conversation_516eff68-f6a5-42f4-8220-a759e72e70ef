package cn.huolala.arch.hermes.compatible.protocol;

import cn.huolala.arch.hermes.api.fallback.FallbackFactory;
import cn.huolala.arch.hermes.cluster.fallback.Fallback;
import cn.huolala.arch.hermes.common.event.EventDispatcher;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.config.support.BeanDefinitionSupport;
import cn.huolala.arch.hermes.compatible.metadata.event.GenericServiceMetadataEvent;
import cn.huolala.arch.hermes.protocol.core.RpcResult;
import cn.lalaframework.soa.generic.SOAFallback;
import cn.lalaframework.soa.generic.SOAMethodAttrs;

import java.util.HashMap;
import java.util.Map;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.FALLBACK_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.UNKNOWN_APPLICATION_NAME;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.ARGUMENTS;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.CONNECTION_TIMEOUT;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.READ_TIMEOUT;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.RETURN_TYPE;
import static cn.lalaframework.soa.annotation.SOAParamsMode.STANDARD;

public final class IJsonRpcGenericServiceAdapter {

    /**
     * EventDispatcher
     */
    private static final EventDispatcher EVENT_DISPATCHER;

    static {
        EVENT_DISPATCHER = EventDispatcher.getDefaultExtension();
    }

    private IJsonRpcGenericServiceAdapter() {
    }

    public static <T> T call(String appId, String servicePath, boolean isHttps, SOAMethodAttrs soaMethodAttrs, SOAFallback<T> soaFallback, Object[] argument, Map<String, String> extraHttpHeaders, Class<T> returnType) throws Throwable {
        String methodPath = soaMethodAttrs.getMethodName();
        if (StringUtils.isNotEmpty(soaMethodAttrs.getMethodPath())) {
            methodPath = soaMethodAttrs.getMethodPath();
        }
        JsonRpcInvocation rpcInvocation = JsonRpcInvocation.builder()
                .application(StringUtils.isEmpty(appId) ? UNKNOWN_APPLICATION_NAME : appId.trim())
                .oneway(soaMethodAttrs.getOneWay() != null && soaMethodAttrs.getOneWay())
                .servicePath(servicePath)
                .methodPath(methodPath)
                .rawMethodPath(methodPath)
                .extraHeader(extraHttpHeaders)
                .paramsMode(soaMethodAttrs.getParamsMode())
                .commandKey(soaMethodAttrs.getCommandKey())
                .paramsMethod(soaMethodAttrs.getParamsMethod())
                .fixedParams(soaMethodAttrs.getFixedParams() == null ? new HashMap<>() : new HashMap<>(soaMethodAttrs.getFixedParams()))
                .resultMode(soaMethodAttrs.getResultMode())
                .completeResult(soaMethodAttrs.getCompleteResult())
                .fixedParamsAppend(soaMethodAttrs.getFixedParamsAppend())
                .https(isHttps)
                .build();

        rpcInvocation.setCommandKey(BeanDefinitionSupport.generateConsumerKeyInGeneric(rpcInvocation.getApplication(),
                rpcInvocation.getServicePath(), soaMethodAttrs.getMethodName(), rpcInvocation.getCommandKey()));

        if (rpcInvocation.getParamsMode() != STANDARD) {
            String reMethodPath = BeanDefinitionSupport.resolverFixParams(rpcInvocation.isFixedParamsAppend(), rpcInvocation.getFixedParams(), rpcInvocation.getMethodPath());
            rpcInvocation.setMethodPath(reMethodPath);
        }

        if (soaMethodAttrs.getReadTimeoutMillis() > 0) {
            rpcInvocation.getAttributes().put(READ_TIMEOUT, soaMethodAttrs.getReadTimeoutMillis());
        }
        if (soaMethodAttrs.getConnectionTimeoutMillis() > 0) {
            rpcInvocation.getAttributes().put(CONNECTION_TIMEOUT, soaMethodAttrs.getConnectionTimeoutMillis());
        }

        rpcInvocation.getAttributes().put(RETURN_TYPE, returnType);
        rpcInvocation.getAttributes().put(ARGUMENTS, argument);
        rpcInvocation.getAttributes().put(FALLBACK_KEY,
                (FallbackFactory<Fallback>) context -> (Fallback) (invocation, ex) -> RpcResult.newRpcResult(soaFallback.call(argument), invocation));
        EVENT_DISPATCHER.dispatch(new GenericServiceMetadataEvent(rpcInvocation));
        return IJsonRpcGenericService.invoker(rpcInvocation);
    }

}
