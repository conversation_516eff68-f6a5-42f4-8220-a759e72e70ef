package cn.huolala.arch.hermes.compatible.protocol;

import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.lalaframework.soa.annotation.SOAParamsMethod;
import cn.lalaframework.soa.annotation.SOAParamsMode;
import cn.lalaframework.soa.annotation.SOAResultMode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.COMPLETE_RESULT;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.FIXED_PARAMS;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.FIXED_PARAMS_APPEND;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.PARAMS_METHOD;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.PARAMS_MODE;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.RESULT_MODE;
import static cn.huolala.arch.hermes.config.spring.support.AnnotationUtils.getAttribute;

public class JsonRpcInvocation {

    /**
     * appId
     */
    private String application;

    /**
     * async
     */
    private boolean async;

    /**
     * async & ignore result
     */
    private boolean oneway;

    /**
     * host
     */
    private String host;

    /**
     * port
     */
    private int port;

    /**
     * https
     */
    private boolean https;

    /**
     * service path
     */
    private String servicePath;

    /**
     * method path
     */
    private String methodPath;

    private String rawMethodPath;

    /**
     * The governance of commandKey
     */
    private String commandKey;

    /**
     * completeResult
     */
    private boolean completeResult;

    /**
     * The request method param type
     */
    private SOAParamsMethod paramsMethod;

    /**
     * The request method params model
     */
    private SOAParamsMode paramsMode;

    /**
     * The result model
     * since 1.3.0 will resolve method result type
     */
    private SOAResultMode resultMode;

    /**
     * extra params,Send  with the request
     */
    private Map<String, String> extraHeader;

    /**
     * fixedParams
     */
    private Map<String, Object> fixedParams;

    /**
     * fixedParams
     */
    private boolean fixedParamsAppend;

    /**
     * paramsName
     */
    private List<String> paramsName;

    /**
     * Only used on the caller side, will not appear on the wire.
     */
    private final transient Map<Object, Object> attributes;


    private JsonRpcInvocation(Builder builder) {
        setApplication(builder.application);
        setAsync(builder.async);
        setOneway(builder.oneway);
        setHost(builder.host);
        setPort(builder.port);
        setHttps(builder.https);
        setServicePath(builder.servicePath);
        setMethodPath(builder.methodPath);
        setRawMethodPath(builder.rawMethodPath);
        setCommandKey(builder.commandKey);
        setCompleteResult(builder.completeResult);
        setParamsMethod(builder.paramsMethod);
        setParamsMode(builder.paramsMode);
        setResultMode(builder.resultMode);
        setExtraHeader(CollectionUtils.isEmptyMap(builder.extraHeader) ? new HashMap<>() : builder.extraHeader);
        setFixedParams(builder.fixedParams);
        setFixedParamsAppend(builder.fixedParamsAppend);
        setParamsName(builder.paramsName);
        this.attributes = new HashMap<>();
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public boolean isAsync() {
        return async;
    }

    public void setAsync(boolean async) {
        this.async = async;
    }

    public boolean isOneway() {
        return oneway;
    }

    public void setOneway(boolean oneway) {
        this.oneway = oneway;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public boolean isHttps() {
        return https;
    }

    public void setHttps(boolean https) {
        this.https = https;
    }

    public String getServicePath() {
        return servicePath;
    }

    public void setServicePath(String servicePath) {
        this.servicePath = servicePath;
    }

    public String getMethodPath() {
        return methodPath;
    }

    public void setMethodPath(String methodPath) {
        this.methodPath = methodPath;
    }

    public String getRawMethodPath() {
        return rawMethodPath;
    }

    public void setRawMethodPath(String rawMethodPath) {
        this.rawMethodPath = rawMethodPath;
    }

    public String getCommandKey() {
        return commandKey;
    }

    public void setCommandKey(String commandKey) {
        this.commandKey = commandKey;
    }

    public boolean isCompleteResult() {
        return completeResult;
    }

    public void setCompleteResult(boolean completeResult) {
        this.completeResult = completeResult;
    }

    public SOAParamsMethod getParamsMethod() {
        return paramsMethod;
    }

    public void setParamsMethod(SOAParamsMethod paramsMethod) {
        this.paramsMethod = paramsMethod;
    }

    public SOAParamsMode getParamsMode() {
        return paramsMode;
    }

    public void setParamsMode(SOAParamsMode paramsMode) {
        this.paramsMode = paramsMode;
    }

    public SOAResultMode getResultMode() {
        return resultMode;
    }

    public void setResultMode(SOAResultMode resultMode) {
        this.resultMode = resultMode;
    }

    public Map<String, String> getExtraHeader() {
        return extraHeader;
    }

    public void setExtraHeader(Map<String, String> extraHeader) {
        this.extraHeader = extraHeader;
    }

    public Map<String, Object> getFixedParams() {
        return fixedParams;
    }

    public void setFixedParams(Map<String, Object> fixedParams) {
        this.fixedParams = fixedParams;
    }

    public boolean isFixedParamsAppend() {
        return fixedParamsAppend;
    }

    public void setFixedParamsAppend(boolean fixedParamsAppend) {
        this.fixedParamsAppend = fixedParamsAppend;
    }

    public List<String> getParamsName() {
        return paramsName;
    }

    public void setParamsName(List<String> paramsName) {
        this.paramsName = paramsName;
    }

    public Map<Object, Object> getAttributes() {
        return attributes;
    }

    public static Builder builder() {
        return new Builder();
    }


    public static class Builder {

        private String application;
        private boolean async;
        private boolean oneway;
        private String host;
        private int port;
        private boolean https;
        private String servicePath;
        private String methodPath;
        private String rawMethodPath;
        private String commandKey;
        private boolean completeResult;
        private SOAParamsMethod paramsMethod = SOAParamsMethod.GET;
        private SOAParamsMode paramsMode = SOAParamsMode.STANDARD;
        private SOAResultMode resultMode = SOAResultMode.OBJECT;
        private Map<String, String> extraHeader;
        private Map<String, Object> fixedParams;
        private boolean fixedParamsAppend;
        private List<String> paramsName;

        public Builder application(String application) {
            this.application = application;
            return this;
        }

        public Builder async(boolean async) {
            this.async = async;
            return this;
        }

        public Builder oneway(boolean oneway) {
            this.oneway = oneway;
            return this;
        }

        public Builder host(String host) {
            this.host = host;
            return this;
        }

        public Builder port(int port) {
            this.port = port;
            return this;
        }

        public Builder https(boolean https) {
            this.https = https;
            return this;
        }

        public Builder servicePath(String servicePath) {
            this.servicePath = servicePath;
            return this;
        }

        public Builder methodPath(String methodPath) {
            this.methodPath = methodPath;
            return this;
        }

        public Builder rawMethodPath(String rawMethodPath) {
            this.rawMethodPath = rawMethodPath;
            return this;
        }

        public Builder commandKey(String commandKey) {
            this.commandKey = commandKey;
            return this;
        }

        public Builder completeResult(boolean completeResult) {
            this.completeResult = completeResult;
            return this;
        }

        public Builder paramsMethod(SOAParamsMethod paramsMethod) {
            this.paramsMethod = paramsMethod;
            return this;
        }

        public Builder paramsMode(SOAParamsMode paramsMode) {
            this.paramsMode = paramsMode;
            return this;
        }

        public Builder resultMode(SOAResultMode resultMode) {
            this.resultMode = resultMode;
            return this;
        }

        public Builder extraHeader(Map<String, String> extraHeader) {
            if (this.extraHeader == null) {
                this.extraHeader = new HashMap<>();
            }
            if (CollectionUtils.isNotEmptyMap(extraHeader)) {
                this.extraHeader.putAll(extraHeader);
            }
            return this;
        }

        public Builder putExtraHeader(String key, String value) {
            if (this.extraHeader == null) {
                this.extraHeader = new HashMap<>();
            }
            this.extraHeader.put(key, value);
            return this;
        }

        public Builder fixedParams(Map<String, Object> fixedParams) {
            if (this.fixedParams == null) {
                this.fixedParams = new HashMap<>();
            }
            this.fixedParams.putAll(fixedParams);
            return this;
        }

        public Builder putFixedParams(String key, Object value) {
            if (this.fixedParams == null) {
                this.fixedParams = new HashMap<>();
            }
            this.fixedParams.put(key, value);
            return this;
        }

        public Builder fixedParamsAppend(boolean fixedParamsAppend) {
            this.fixedParamsAppend = fixedParamsAppend;
            return this;
        }


        public Builder paramsName(List<String> paramsName) {
            this.paramsName = paramsName;
            return this;
        }

        public Builder ofAttribute(Map<String, Object> attributes) {
            resultMode(getAttribute(attributes, RESULT_MODE, SOAResultMode.OBJECT));
            paramsMethod(getAttribute(attributes, PARAMS_METHOD, SOAParamsMethod.GET));
            completeResult(getAttribute(attributes, COMPLETE_RESULT, Boolean.FALSE));
            paramsMode(getAttribute(attributes, PARAMS_MODE, SOAParamsMode.STANDARD));
            fixedParams(getAttribute(attributes, FIXED_PARAMS, new HashMap<>()));
            fixedParamsAppend(getAttribute(attributes, FIXED_PARAMS_APPEND, Boolean.FALSE));
            return this;
        }

        public JsonRpcInvocation build() {
            return new JsonRpcInvocation(this);
        }
    }
}
