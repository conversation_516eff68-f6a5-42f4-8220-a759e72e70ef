package cn.huolala.arch.hermes.compatible.protocol;

import cn.huolala.arch.hermes.api.config.ConsumerConfig;
import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.context.ConfigManager;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.compatible.protocol.handler.BodyFormParamsHandler;
import cn.huolala.arch.hermes.compatible.protocol.handler.BodyJsonParamsHandler;
import cn.huolala.arch.hermes.compatible.protocol.handler.ParamsHandler;
import cn.huolala.arch.hermes.compatible.protocol.handler.ParamsParamsHandler;
import cn.huolala.arch.hermes.compatible.protocol.handler.StandardParamsHandler;
import cn.huolala.arch.hermes.compatible.protocol.interceptor.ClientInterceptor;
import cn.huolala.arch.hermes.compatible.protocol.interceptor.HttpClientInterceptor;
import cn.huolala.arch.hermes.compatible.protocol.interceptor.InterceptorChain;
import cn.huolala.arch.hermes.compatible.protocol.invoker.ConcreteInvocationInvoker;
import cn.huolala.arch.hermes.compatible.protocol.invoker.InvocationInvoker;
import cn.huolala.arch.hermes.compatible.protocol.invoker.JsonRpcRefinedInvoker;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.huolala.arch.hermes.protocol.jsonrpc.refined.RefinedInvoker;
import cn.huolala.arch.hermes.protocol.jsonrpc.refined.RefinedInvokerBuilder;
import cn.huolala.arch.hermes.serialize.Serialization;
import cn.lalaframework.soa.annotation.SOAParamsMode;
import org.apache.hc.client5.http.impl.async.CloseableHttpAsyncClient;
import org.apache.hc.client5.http.impl.async.HttpAsyncClientBuilder;
import org.apache.hc.client5.http.impl.async.HttpAsyncClients;
import org.apache.hc.client5.http.impl.nio.PoolingAsyncClientConnectionManager;
import org.apache.hc.client5.http.impl.nio.PoolingAsyncClientConnectionManagerBuilder;
import org.apache.hc.client5.http.ssl.ClientTlsStrategyBuilder;
import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.core5.http.config.CharCodingConfig;
import org.apache.hc.core5.http.nio.ssl.TlsStrategy;
import org.apache.hc.core5.pool.PoolConcurrencyPolicy;
import org.apache.hc.core5.reactor.IOReactorConfig;
import org.apache.hc.core5.ssl.SSLContexts;
import org.apache.hc.core5.util.TimeValue;

import javax.net.ssl.SSLContext;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static cn.huolala.arch.hermes.common.constants.Constants.DEFAULT_TIMEOUT;
import static cn.huolala.arch.hermes.common.constants.Constants.NAME;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.CONNECTION_TIME_TO_LIVE_NAME;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.CONNECTION_TIME_TO_LIVE_VALUE;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.DEFAULT_SELECT_INTERVAL;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.IO_THREAD_COUNT_NAME;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.IO_THREAD_COUNT_VALUE;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.MAX_CONN_PER_ROUTE_NAME;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.MAX_CONN_PER_ROUTE_VALUE;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.MAX_CONN_TOTAL_NAME;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.MAX_CONN_TOTAL_VALUE;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.POOL_CONCURRENCY_POLICY_NAME;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.POOL_CONCURRENCY_POLICY_VALUE;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.SOCKET_KEEP_LIVE_NAME;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.SOCKET_KEEP_LIVE_VALUE;
import static cn.huolala.arch.hermes.protocol.exception.RpcException.UNKNOWN_EXCEPTION;
import static java.util.Optional.ofNullable;

public class JsonRpcRefinedInvokerBuilder implements RefinedInvokerBuilder {

    private static final String JSON_EXT_NAME = "jackson";

    protected final Serialization serialization;

    private final CloseableHttpAsyncClient asyncClient;

    /**
     * The strategy map of paramsMode
     */
    private static final Map<SOAParamsMode, ParamsHandler> PARAMS_MODEL_HANDLER_MAP = new HashMap<>();

    static {
        PARAMS_MODEL_HANDLER_MAP.put(SOAParamsMode.STANDARD, new StandardParamsHandler());
        PARAMS_MODEL_HANDLER_MAP.put(SOAParamsMode.BODY_JSON, new BodyJsonParamsHandler());
        PARAMS_MODEL_HANDLER_MAP.put(SOAParamsMode.PARAMS, new ParamsParamsHandler());
        PARAMS_MODEL_HANDLER_MAP.put(SOAParamsMode.BODY_FORM_DATA, new BodyFormParamsHandler());
    }


    public JsonRpcRefinedInvokerBuilder() {
        this.serialization = ExtensionLoader.getExtensionLoader(Serialization.class).getExtension(JSON_EXT_NAME);
        try {
            asyncClient = buildClient();
        } catch (Exception e) {
            throw new RpcException(UNKNOWN_EXCEPTION, "BuildHttpAsyncClient error", e);
        }
    }


    @Override
    public <T> RefinedInvoker<T> build(Class<T> type, URL url) {
        return new JsonRpcRefinedInvoker<>(type, url, buildInterceptorChain(new ConcreteInvocationInvoker(url, serialization, asyncClient, PARAMS_MODEL_HANDLER_MAP), url));
    }


    private CloseableHttpAsyncClient buildClient() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {

        final SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(TrustAllStrategy.INSTANCE).build();
        final TlsStrategy tlsStrategy = ClientTlsStrategyBuilder.create()
                .setSslContext(sslContext)
                .setHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                .build();

        ConfigManager configManager = ApplicationContext.getConfigManager();
        ConsumerConfig consumerConfig = configManager.getConsumer().get();
        final PoolingAsyncClientConnectionManager cm = PoolingAsyncClientConnectionManagerBuilder.create()
                .setTlsStrategy(tlsStrategy)
                .setConnectionTimeToLive(TimeValue.ofSeconds(Long.parseLong(consumerConfig.getParameters().getOrDefault(CONNECTION_TIME_TO_LIVE_NAME, String.valueOf(CONNECTION_TIME_TO_LIVE_VALUE)))))
                .setPoolConcurrencyPolicy(PoolConcurrencyPolicy.valueOf(consumerConfig.getParameters().getOrDefault(POOL_CONCURRENCY_POLICY_NAME, POOL_CONCURRENCY_POLICY_VALUE)))
                .setMaxConnTotal(Integer.parseInt(consumerConfig.getParameters().getOrDefault(MAX_CONN_TOTAL_NAME, String.valueOf(MAX_CONN_TOTAL_VALUE))))
                .setMaxConnPerRoute(Integer.parseInt(consumerConfig.getParameters().getOrDefault(MAX_CONN_PER_ROUTE_NAME, String.valueOf(MAX_CONN_PER_ROUTE_VALUE))))
                .build();

        IOReactorConfig ioReactorConfig = IOReactorConfig.custom()
                .setSoTimeout(ofNullable(consumerConfig.getTimeout()).orElse(DEFAULT_TIMEOUT), TimeUnit.MILLISECONDS)
                .setSoKeepAlive(Boolean.parseBoolean(consumerConfig.getParameters().getOrDefault(SOCKET_KEEP_LIVE_NAME, String.valueOf(SOCKET_KEEP_LIVE_VALUE))))
                .setIoThreadCount(Integer.parseInt(consumerConfig.getParameters().getOrDefault(IO_THREAD_COUNT_NAME, String.valueOf(IO_THREAD_COUNT_VALUE))))
                .setSelectInterval(TimeValue.ofMilliseconds(DEFAULT_SELECT_INTERVAL))
                .build();

        HttpAsyncClientBuilder builder = HttpAsyncClients.custom()
        .setConnectionManager(cm)
        .setIOReactorConfig(ioReactorConfig)
        .setCharCodingConfig(CharCodingConfig.custom().setCharset(StandardCharsets.UTF_8).build())
        .setUserAgent(NAME)
        .disableAutomaticRetries();

        Set<HttpClientInterceptor> interceptors = ExtensionLoader.getExtensionLoader(HttpClientInterceptor.class).getSupportedExtensionInstances();
        if (CollectionUtils.isNotEmpty(interceptors)) {
            interceptors.forEach(interceptor -> {
                builder.addRequestInterceptorLast(interceptor);
                builder.addResponseInterceptorLast(interceptor);
            });
        }

        CloseableHttpAsyncClient asyncClient = builder.build();
        asyncClient.start();
        return asyncClient;
    }


    private InterceptorChain buildInterceptorChain(InvocationInvoker invoker, URL url) {
        InterceptorChain last = InterceptorChain.last(invoker);

        List<ClientInterceptor> interceptors = ExtensionLoader.getExtensionLoader(ClientInterceptor.class)
                .getActivateExtension(url, (String[]) null);

        if (!interceptors.isEmpty()) {
            for (int i = interceptors.size() - 1; i >= 0; i--) {
                last = InterceptorChain.of(interceptors.get(i), last);
            }
        }
        return last;
    }

}
