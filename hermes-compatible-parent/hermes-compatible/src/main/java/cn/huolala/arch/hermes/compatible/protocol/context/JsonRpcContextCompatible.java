package cn.huolala.arch.hermes.compatible.protocol.context;

import cn.huolala.arch.hermes.protocol.core.context.ContextCompatible;
import cn.huolala.arch.hermes.common.extension.Activate;
import cn.lalaframework.context.core.ContextHolder;

import java.util.Map;

/**
 * Default Compatible impl
 */
@Activate
public class JsonRpcContextCompatible implements ContextCompatible {

    @Override
    public Map<String, String> attachments() {
        return ContextHolder.getInstance().getContextHeaders();
    }
}
