package cn.huolala.arch.hermes.compatible.protocol.exception;

import cn.huolala.arch.hermes.common.exception.ExceptionMarker;

import org.apache.hc.core5.http.HttpException;

public class RpcStatusException extends HttpException implements ExceptionMarker {

    private int code;

    public RpcStatusException() {
        super();
    }

    public RpcStatusException(int code) {
        super();
        this.code = code;
    }

    public RpcStatusException(int code, String message) {
        super(message);
        this.code = code;
    }

    public RpcStatusException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
