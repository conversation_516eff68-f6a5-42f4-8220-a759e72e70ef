package cn.huolala.arch.hermes.compatible.protocol.handler;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.logger.Level;
import cn.huolala.arch.hermes.common.util.ArrayUtils;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.protocol.JsonRpcInvocation;
import cn.huolala.arch.hermes.compatible.protocol.exception.RpcStatusException;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.huolala.arch.hermes.serialize.Serialization;
import cn.lalaframework.soa.annotation.SOAContext;
import cn.lalaframework.soa.annotation.SOAResultMode;
import cn.lalaframework.soa.exception.BusinessException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.fasterxml.jackson.databind.node.ValueNode;
import org.apache.hc.client5.http.async.methods.SimpleBody;
import org.apache.hc.client5.http.async.methods.SimpleHttpRequest;
import org.apache.hc.client5.http.async.methods.SimpleHttpResponse;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.entity.UrlEncodedFormEntity;
import org.apache.hc.core5.http.HttpHost;
import org.apache.hc.core5.http.HttpStatus;
import org.apache.hc.core5.http.NameValuePair;
import org.apache.hc.core5.http.ParseException;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.message.BasicNameValuePair;

import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.huolala.arch.hermes.common.util.StringUtils.*;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.*;
import static cn.huolala.arch.hermes.protocol.exception.RpcException.SERIALIZATION_EXCEPTION;
import static java.util.Optional.ofNullable;

@SuppressWarnings("unchecked")
public abstract class AbstractParamsHandler implements ParamsHandler {


    @Override
    public SimpleHttpRequest pack(Invocation invocation, JsonRpcInvocation jsonRpcInvocation, Serialization serialization, URL url) throws Exception {

        Map<String, Object> requestParams = new HashMap<>(ofNullable(jsonRpcInvocation.getFixedParams()).orElse(new HashMap<>()));
        Object argument = resolverArgument(invocation.getArguments(), jsonRpcInvocation.getParamsName(), requestParams, serialization, url);
        if (argument instanceof Map) {
            requestParams = (Map<String, Object>) argument;
        }

        String requestPath = buildRequestPath(jsonRpcInvocation.getServicePath(), jsonRpcInvocation.getMethodPath(), requestParams, serialization, url);
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(invocation.getConnectionTimeout(), TimeUnit.MILLISECONDS)
                .setConnectionRequestTimeout(invocation.getConnectionTimeout(), TimeUnit.MILLISECONDS)
                .setResponseTimeout(invocation.getTimeout(), TimeUnit.MILLISECONDS)
                .setCircularRedirectsAllowed(true)
                .setRedirectsEnabled(true)
                .build();

        String uri = new HttpHost(getScheme(jsonRpcInvocation.isHttps()), jsonRpcInvocation.getHost(), jsonRpcInvocation.getPort() <= 0 ? -1 : jsonRpcInvocation.getPort()).toURI() + requestPath;
        SimpleHttpRequest request = doPack(jsonRpcInvocation, uri, argument, serialization, url);
        ofNullable(jsonRpcInvocation.getExtraHeader()).ifPresent(extraHeader -> extraHeader.forEach(request::addHeader));
        request.setConfig(requestConfig);

        return request;
    }

    @Override
    public Object unpack(Object response, Invocation invocation, JsonRpcInvocation jsonRpcInvocation, Serialization serialization, URL url) throws Exception {
        if (response instanceof  Exception) {
            throw (Exception) response;
        }
        if (jsonRpcInvocation.isOneway()) {
            return null;
        }

        SimpleHttpResponse httpResponse = (SimpleHttpResponse) response;
        String responseBody = null;
        SimpleBody simpleBody = httpResponse.getBody();
        if (simpleBody != null && simpleBody.getBodyBytes() != null){
            responseBody = new String(simpleBody.getBodyBytes(), StandardCharsets.UTF_8);
            final String mdcResponseBody = responseBody;
            invocation.putDMCLog("RESPONSE_BODY", () -> mdcResponseBody, Level.DEBUG);
        }
        int responseCode = httpResponse.getCode();
        boolean isSuccess = isSuccess(responseCode);
        if (!isSuccess) {
            throw creteException(responseBody, responseCode, jsonRpcInvocation, serialization, url);
        }

        if (StringUtils.isNotEmpty(responseBody)) {
            JsonNode bodyNode  = resolveBody(responseBody, invocation.getReturnType(), jsonRpcInvocation.isCompleteResult(), serialization, url);
            Exception exception = handleErrorResponse(bodyNode, jsonRpcInvocation);
            if (exception != null) {
                throw exception;
            }
            Type[] returnTypes = invocation.getReturnTypes();
            return doUnpack(bodyNode, invocation.getReturnType(), returnTypes[1], jsonRpcInvocation, serialization, url);
        }
        return null;
    }


    protected Exception creteException(String body, int code, JsonRpcInvocation invocation, Serialization serialization, URL url) throws IOException {
        return new RpcStatusException(code, body);
    }


    protected JsonNode resolveBody(String responseBody, Class<?> returnType, boolean completeResult, Serialization serialization, URL url) throws IOException {
        JsonNode jsonNode;
        if (String.class.isAssignableFrom(returnType)) {
            try {
                jsonNode = serialization.deserializeString(url, responseBody, JsonNode.class);
            } catch (Exception e) {
                jsonNode = TextNode.valueOf(responseBody);
            }
        } else if (ReflectUtils.isPrimitive(returnType)) {
            jsonNode = serialization.deserializeString(url, responseBody, ValueNode.class);
        } else if (returnType.isArray() || Collection.class.isAssignableFrom(returnType)) {
            if (!completeResult) {
                jsonNode = serialization.deserializeString(url, responseBody, JsonNode.class);
                if (jsonNode != null && jsonNode.isObject() && jsonNode.has(DATA)) {
                    jsonNode = jsonNode.get(DATA);
                }
            } else {
                jsonNode = serialization.deserializeString(url, responseBody, ArrayNode.class);
            }
        } else {
            jsonNode = serialization.deserializeString(url, responseBody, JsonNode.class);
        }
        return jsonNode;
    }


    /**
     * compatible with response which have ret field , auto to handle ret no equal to 0 and throw
     *
     * @see BusinessException
     * others will discern to no error ,because response may not have ret field
     */
    protected Exception handleErrorResponse(JsonNode jsonNode, JsonRpcInvocation invocation) {
        if (jsonNode instanceof ArrayNode || jsonNode instanceof ValueNode) {
            return null;
        }
        JsonNode retNode = jsonNode.get(ERROR_RET);
        boolean completeResult = invocation.isCompleteResult();
        if (null != retNode && (retNode.isNumber() ? (retNode.asInt() != 0) : (!"0".equals(retNode.asText()))) && !completeResult) {
            int ret = 0;
            String msg = "NULL";
            JsonNode retJsonNode = jsonNode.get(ERROR_RET);
            JsonNode msgJsonNode = jsonNode.get(ERROR_MSG);
            if(null != retJsonNode){
                if (retJsonNode.isNumber()) {
                    ret = retJsonNode.asInt();
                } else {
                    ret = Integer.parseInt(retJsonNode.asText());
                }
            }

            if (null != msgJsonNode && !msgJsonNode.isNull()) {
                msg = msgJsonNode.asText();
            }
            return new BusinessException(ret, msg);
        }
        return null;
    }



    protected Object doUnpack(JsonNode jsonNode, Class<?> returnType, Type genericReturnType, JsonRpcInvocation jsonRpcInvocation, Serialization serialization, URL url) throws IOException {

        SOAResultMode resultMode = jsonRpcInvocation.getResultMode();
        boolean completeResult = jsonRpcInvocation.isCompleteResult();
        if (resultMode == SOAResultMode.OBJECT) {
            //返回类型为Object&设置属性不返回所有内容&现返回内容有data字段，取data字段内容返回，否则返回全部内容
            if (!completeResult && jsonNode.has(DATA)) {
                jsonNode = jsonNode.get(DATA);
            }
        } else if (resultMode == SOAResultMode.STRING) {
            if (!completeResult) {
                jsonNode = jsonNode.get(DATA);
            }
        }
        if (returnType == void.class) {
            return null;
        }

        if (ReflectUtils.isPrimitive(returnType)) {
            String body = jsonNode == null ? null : (jsonNode.isTextual() ? jsonNode.textValue() : jsonNode.toString());
            if (isEmpty(body)) {
                return null;
            }
            if (String.class.isAssignableFrom(returnType)) {
                return body;
            }
            return serialization.deserializeString(url, body, genericReturnType);
        }
        String body = jsonNode == null ? null : jsonNode.toString();
        return serialization.deserializeString(url, body, genericReturnType);
    }


    protected Object resolverArgument(Object[] arguments, List<String> paramNames, Map<String, Object> requestParamsMap, Serialization serialization, URL url) {
        Map<String, Object> argumentMergeMap = new HashMap<>(requestParamsMap);
        if (CollectionUtils.isNotEmpty(paramNames)) {
            if (paramNames.size() != arguments.length) {
                throw new IllegalArgumentException("Param annotations must be added to all parameters, arguments:" + Arrays.toString(arguments) + ",paramSignatures:" + paramNames);
            }
            for (int i = 0; i < paramNames.size(); i++) {
                Object argument = arguments[i];
                if (argument != null) {
                    argumentMergeMap.put(paramNames.get(i), argument);
                }
            }
            return argumentMergeMap;
        }
        return doResolverArgument(arguments, argumentMergeMap, serialization, url);
    }


    protected Object doResolverArgument(Object[] arguments, Map<String, Object> requestParams, Serialization serialization, URL url) {
        if (ArrayUtils.isEmpty(arguments)) {
            return requestParams;
        }
        if (arguments.length > 1) {
            throw new IllegalArgumentException("The Method argument params must only one argument[" + Arrays.toString(arguments) + "]");
        }
        Object argument = arguments[0];
        if (ReflectUtils.isPrimitive(argument.getClass())) {
            throw new IllegalArgumentException("The Method argument params must be Map or Object argument[" + Arrays.toString(arguments) + "]");
        }
        return doResolverArgument(requestParams, argument, serialization, url);
    }



    protected Map<String, Object> doResolverArgument(Map<String, Object> requestParams, Object argument, Serialization serialization, URL url) {
        if (argument instanceof Map) {
            requestParams.putAll(CollectionUtils.removeNullValue((Map<String, Object>) argument));
        } else {
            if (argument.getClass().equals(Object.class)) {
                return requestParams;
            }
            try {
                Map<String, Object> params  = serialization.deserializeBytes(url, serialization.serialize(url, argument), Map.class, new TypeReference<Map<String, Object>>() { }.getType());
                requestParams.putAll(params);
            } catch (IOException e) {
                throw new RpcException(SERIALIZATION_EXCEPTION, "The Method argument serialize error,argument" + argument + ", case:[" + e.getMessage() + "]");
            }
        }
        return requestParams;
    }


    protected String buildRequestPath(String servicePath, String methodPath, Map<String, Object> requestParams, Serialization serialization, URL url) {
        String requestPath;
        if (servicePath == null) {
            servicePath = "";
        }
        if (servicePath.startsWith(PATH_PREFIX)) {
            requestPath = servicePath;
        } else {
            requestPath = PATH_PREFIX + servicePath;
        }
        if (StringUtils.isNotEmpty(methodPath)) {
            if (requestPath.endsWith(PATH_PREFIX)) {
                requestPath = removeEnd(requestPath, PATH_PREFIX) + methodPath;
            } else {
                requestPath = requestPath + methodPath;
            }
        }
        requestPath = pathFormat(requestPath, requestParams);
        requestPath = joinURL(requestPath, SOAContext.getUrlParams(), serialization, url);
        return requestPath;
    }



    protected String joinURL(String servicePath, Map<String, Object> parameter, Serialization serialization, URL url) {
        if (CollectionUtils.isEmptyMap(parameter)) {
            return servicePath;
        }
        String finalPath = servicePath;
        List<NameValuePair> nvPair = parameter.entrySet().stream().filter(entry -> entry.getValue() != null).map(entry -> {
            Object value = entry.getValue();
            if (!ReflectUtils.isPrimitive(value.getClass()) && !String.class.isAssignableFrom(value.getClass())) {
                try {
                    value = new String(serialization.serialize(url, value));
                } catch (IOException e) {
                    throw new RpcException(SERIALIZATION_EXCEPTION, "Build params request error,path[" + finalPath + "],parameter[" + parameter + "]", e);
                }
            }
            return new BasicNameValuePair(entry.getKey(), String.valueOf(value));
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(nvPair)) {
            return servicePath;
        }

        try {
            String kv = EntityUtils.toString(new UrlEncodedFormEntity(nvPair, StandardCharsets.UTF_8), StandardCharsets.UTF_8);
            int i = servicePath.indexOf("?");
            if (i < 0) {
                servicePath += "?" + kv;
            } else if (servicePath.endsWith("?")) {
                servicePath += kv;
            } else {
                servicePath += "&" + kv;
            }
        } catch (IOException | ParseException e) {
            throw new RpcException(SERIALIZATION_EXCEPTION, "Build params request error,path[" + servicePath + "],parameter[" + parameter + "]", e);
        }
        return servicePath;
    }

    /**
     * scheme
     */
    private String getScheme(boolean https) {
        return https  ? "https" : "http";
    }

    private boolean isSuccess(int code) {
        return code >= HttpStatus.SC_SUCCESS & code <= HttpStatus.SC_IM_USED;
    }

    protected abstract SimpleHttpRequest doPack(JsonRpcInvocation invocation, String uri, Object argument, Serialization serialization, URL url) throws IOException;
}
