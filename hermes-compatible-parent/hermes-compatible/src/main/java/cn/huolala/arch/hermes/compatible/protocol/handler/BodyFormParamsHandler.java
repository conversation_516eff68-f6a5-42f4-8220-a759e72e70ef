package cn.huolala.arch.hermes.compatible.protocol.handler;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.compatible.protocol.JsonRpcInvocation;
import cn.huolala.arch.hermes.serialize.Serialization;
import org.apache.hc.client5.http.async.methods.SimpleHttpRequest;
import org.apache.hc.client5.http.entity.mime.MultipartEntityBuilder;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.Method;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Map;

public class BodyFormParamsHandler extends AbstractParamsHandler {

    @SuppressWarnings("unchecked")
    @Override
    protected SimpleHttpRequest doPack(JsonRpcInvocation invocation, String uri, Object argument, Serialization serialization, URL url) throws IOException {
        SimpleHttpRequest request = SimpleHttpRequest.create(Method.POST.name(), uri);
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.setLaxMode();
        for (Map.Entry<String, Object> entry : ((Map<String, Object>) argument).entrySet()) {
            String key = entry.getKey();
            final Object value = entry.getValue();
            if (value != null && String.class.isAssignableFrom(value.getClass())) {
                builder.addBinaryBody(key, ((String) value).getBytes());
                continue;
            }
            builder.addBinaryBody(key, serialization.serialize(url, entry.getValue()));
        }
        HttpEntity httpEntity = builder.build();
        final ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        httpEntity.writeTo(outStream);
        outStream.flush();
        request.setBody(outStream.toByteArray(), ContentType.parse(httpEntity.getContentType()));
        return request;
    }
}
