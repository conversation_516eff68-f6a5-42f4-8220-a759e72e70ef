package cn.huolala.arch.hermes.compatible.protocol.handler;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.common.util.ArrayUtils;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.compatible.protocol.JsonRpcInvocation;
import cn.huolala.arch.hermes.serialize.Serialization;
import org.apache.hc.client5.http.async.methods.SimpleHttpRequest;
import org.apache.hc.client5.http.async.methods.SimpleRequestBuilder;
import org.apache.hc.core5.http.ContentType;

import java.io.IOException;
import java.util.Collection;
import java.util.Map;

@Activate
public class BodyJsonParamsHandler extends AbstractParamsHandler {

    private static final ContentType CONTENT_TYPE = ContentType.create(ContentType.APPLICATION_JSON.getMimeType());

    @Override
    protected Object doResolverArgument(Object[] arguments, Map<String, Object> requestParamsMap, Serialization serialization, URL url) {

        if (ArrayUtils.isEmpty(arguments)) {
            return requestParamsMap;
        }

        if (arguments.length == 1) {
            Object argument = arguments[0];
            if (ReflectUtils.isPrimitive(argument.getClass()) || String.class.isAssignableFrom(argument.getClass())
                    || argument.getClass().isArray() || Collection.class.isAssignableFrom(argument.getClass())) {
                return argument;
            }
            return doResolverArgument(requestParamsMap, argument, serialization, url);
        }
        return arguments;
    }


    @Override
    protected SimpleHttpRequest doPack(JsonRpcInvocation invocation, String uri, Object argument, Serialization serialization, URL url) throws IOException {
        return SimpleRequestBuilder.post(uri).setBody(serialization.serialize(url, argument), CONTENT_TYPE).build();
    }
}
