package cn.huolala.arch.hermes.compatible.protocol.handler;

import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.thread.NamedThreadFactory;
import cn.huolala.arch.hermes.compatible.protocol.support.StopWatch;
import cn.huolala.arch.hermes.protocol.Invocation;

import java.io.Serializable;
import java.net.SocketTimeoutException;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.DEFAULT_SELECT_INTERVAL;
import static java.lang.Math.max;
import static java.lang.Math.min;

/**
 * JsonRpcResponse
 */
public class JsonRpcResponse implements Serializable {

    private static final Logger logger = LoggerFactory.getLogger(JsonRpcResponse.class);
    private static final long serialVersionUID = -5560757508085220156L;

    private static final int TIMEOUT_MIN_MILLIS = 10;
    private static final int TIMEOUT_THREAD = min(max(2, Runtime.getRuntime().availableProcessors()), 8);
    private static final ScheduledExecutorService TIMEOUT_EXECUTOR = Executors
            .newScheduledThreadPool(TIMEOUT_THREAD, new NamedThreadFactory("JsonRpc-Timeout"));

    private final StopWatch stopWatch;
    private final Invocation invocation;
    private final CompletableFuture<Object> future;

    public JsonRpcResponse(StopWatch stopWatch, Invocation invocation) {
        this.stopWatch = stopWatch;
        this.invocation = invocation;
        this.future = new CompletableFuture<>();
    }

    public Object recreate() throws Throwable {
        try {
            return future.get(Integer.MAX_VALUE, TimeUnit.MILLISECONDS);
        } catch (ExecutionException e) {
            if (e.getCause() != null) {
                throw e.getCause();
            } else {
                throw e;
            }
        } finally {
            try {
                if (this.stopWatch.isRunning()) {
                    this.stopWatch.stop();
                }
                invocation.putDMCLog("ELAPSED_DETAILS", this.stopWatch.toString());
            } catch (Exception ignoreException) {
                logger.error("ELAPSED_DETAILS error", ignoreException);
            }
        }
    }

    /**
     * start timeout check if necessary<br/>
     * hack for timeout is less than IOReactor's SelectInterval
     */
    public void startTimeoutChecker() {
        Optional.ofNullable(this.invocation)
                .map(Invocation::getTimeout)
                .filter(timeout -> timeout >= TIMEOUT_MIN_MILLIS && timeout <= DEFAULT_SELECT_INTERVAL)
                .ifPresent(timeout -> TIMEOUT_EXECUTOR.schedule(() -> timeout(timeout), timeout, TimeUnit.MILLISECONDS));
    }

    public CompletableFuture<Object> getFuture() {
        return future;
    }

    public void succeeded(Object value) {
        future.complete(value);
    }

    public void failed(Throwable ex) {
        future.completeExceptionally(ex);
    }

    public void cancelled() {
        future.cancel(true);
    }

    private void timeout(int timeout) {
        if (!future.isDone()) {
            // maintain uniformity with network timeout Exception
            failed(new SocketTimeoutException(String.format("%d MILLISECONDS", timeout)));
        }
    }
}
