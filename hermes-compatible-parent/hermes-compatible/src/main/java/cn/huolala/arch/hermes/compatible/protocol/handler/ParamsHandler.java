package cn.huolala.arch.hermes.compatible.protocol.handler;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.compatible.protocol.JsonRpcInvocation;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.serialize.Serialization;
import org.apache.hc.client5.http.async.methods.SimpleHttpRequest;

public interface ParamsHandler {

    /**
     * pack invocation to SimpleHttpRequest
     */
    SimpleHttpRequest pack(Invocation invocation, JsonRpcInvocation jsonRpcInvocation, Serialization serialization, URL url) throws Exception;

    /**
     * unpack result
     */
    Object unpack(Object response, Invocation invocation, JsonRpcInvocation jsonRpcInvocation, Serialization serialization, URL url) throws Exception;

}
