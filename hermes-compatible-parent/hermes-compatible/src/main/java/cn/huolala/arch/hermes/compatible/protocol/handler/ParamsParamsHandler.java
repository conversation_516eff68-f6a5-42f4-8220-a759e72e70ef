package cn.huolala.arch.hermes.compatible.protocol.handler;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.protocol.JsonRpcInvocation;
import cn.huolala.arch.hermes.serialize.Serialization;
import cn.lalaframework.soa.annotation.SOAParamsMethod;
import cn.lalaframework.soa.annotation.SOAParamsMode;
import org.apache.hc.client5.http.async.methods.SimpleHttpRequest;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.Method;

import java.nio.charset.StandardCharsets;
import java.util.Map;

public class ParamsParamsHandler extends AbstractParamsHandler {

    private static final ContentType CONTENT_TYPE = ContentType.create(ContentType.APPLICATION_FORM_URLENCODED.getMimeType());

    @SuppressWarnings("unchecked")
    @Override
    protected SimpleHttpRequest doPack(JsonRpcInvocation invocation, String uri, Object argument, Serialization serialization, URL url) {
        Map<String, Object> requestParams = (Map<String, Object>) argument;
        if (invocation.getParamsMethod() == SOAParamsMethod.GET && invocation.getParamsMode() == SOAParamsMode.PARAMS) {
            uri = joinURL(uri, requestParams, serialization, url);
            return SimpleHttpRequest.create(Method.GET.name(), uri);
        }

        SimpleHttpRequest request = SimpleHttpRequest.create(Method.POST.name(), uri);
        StringBuilder sb = new StringBuilder();
        requestParams.forEach((k, v) -> sb.append(k).append("=").append(v).append("&"));
        String content = StringUtils.removeEnd(sb.toString(), "&");
        request.setBody(content.getBytes(StandardCharsets.UTF_8), CONTENT_TYPE);
        return request;
    }
}
