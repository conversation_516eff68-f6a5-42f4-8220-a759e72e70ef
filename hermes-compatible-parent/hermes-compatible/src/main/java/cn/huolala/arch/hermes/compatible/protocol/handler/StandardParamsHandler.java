package cn.huolala.arch.hermes.compatible.protocol.handler;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.protocol.JsonRpcInvocation;
import cn.huolala.arch.hermes.config.spring.util.ObjectMapperProvider;
import cn.huolala.arch.hermes.serialize.Serialization;

import cn.lalaframework.soa.annotation.SOAContext;
import cn.lalaframework.soa.exception.UnkonwnBizException;
import cn.lalaframework.soa.jsonrpc.JsonRpcClientException;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.hc.client5.http.async.methods.SimpleHttpRequest;
import org.apache.hc.client5.http.async.methods.SimpleRequestBuilder;
import org.apache.hc.core5.http.ContentType;

import java.io.IOException;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.DATA;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.ERROR;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.ERROR_CODE;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.ERROR_MESSAGE;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.ERROR_RET;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.EXCEPTION_TYPE_NAME;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.ID;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.METHOD;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.PARAMS;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.PATH_PREFIX;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.RESULT;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.VERSION;
import static java.util.Optional.ofNullable;


public class StandardParamsHandler extends AbstractParamsHandler {

    private static final Logger logger = LoggerFactory.getLogger(StandardParamsHandler.class);

    private static final ContentType CONTENT_TYPE = ContentType.create("application/json-rpc", "UTF-8");

    private static final Map<String, Class<?>> EXCEPTION_TYPE_NAME_MAP = new ConcurrentHashMap<>();

    @Override
    protected Object resolverArgument(Object[] arguments, List<String> paramNames, Map<String, Object> requestParamsMap, Serialization serialization, URL url) {
        if (CollectionUtils.isNotEmpty(paramNames)) {
            Map<String, Object> namedParams = new HashMap<>();
            for (int i = 0; i < paramNames.size(); i++) {
                namedParams.put(paramNames.get(i), arguments[i]);
            }
            return namedParams;
        }
        return arguments;
    }


    @Override
    protected SimpleHttpRequest doPack(JsonRpcInvocation invocation, String uri, Object argument, Serialization serialization, URL url) throws IOException {
        ObjectMapper objectMapper = getContextObjectMapper(url).orElseGet(this::genObjectMapper);
        ObjectNode jsonrpc = new ObjectNode(objectMapper.getNodeFactory());
        jsonrpc.put(ID, Integer.toString(ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE)));
        jsonrpc.put(METHOD, invocation.getMethodPath());
        jsonrpc.put(JSONRPC, VERSION);
        if (argument.getClass().isArray()) {
            jsonrpc.set(PARAMS, arrayParams(objectMapper, (Object[]) argument));
        } else {
            jsonrpc.set(PARAMS, objectMapper.valueToTree(argument));
        }
        byte[] bytes = serialization.serialize(url, jsonrpc);
        return SimpleRequestBuilder.post(uri).setBody(bytes, CONTENT_TYPE).build();
    }

    private ObjectMapper genObjectMapper() {
        return ObjectMapperHolder.objectMapper;
    }

    /**
     * ObjectMapper Holder
     **/
    private static class ObjectMapperHolder {
        private static final ObjectMapper objectMapper;

        static {
            ObjectMapper mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

            mapper.configure(SerializationFeature.WRAP_ROOT_VALUE, false);
            mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

            objectMapper = mapper;
        }
    }

    /**
     * get ObjectMapper from global context
     */
    private Optional<ObjectMapper> getContextObjectMapper(URL url) {
        Object mapper = ApplicationContext.getAttributes().get(ObjectMapper.class.getName());

        if(url != null) {
            String appid = url.getParameter(Constants.REMOTE_APPLICATION_KEY);
            if(StringUtils.isNotEmpty(appid)) {
                Object customMapper = ApplicationContext.getCustomizeObjectMapper(appid);
                if(customMapper != null && customMapper instanceof ObjectMapper) {
                    mapper = customMapper;
                }
            }
        }

        return Optional.ofNullable(mapper)
                .filter(obj -> obj instanceof ObjectMapper)
                .map(obj -> (ObjectMapper) obj);
    }

    private ArrayNode arrayParams(ObjectMapper mapper, Object[] args) {
        ArrayNode params = new ArrayNode(mapper.getNodeFactory());
        for (Object arg : args) {
            params.add(mapper.valueToTree(arg));
        }
        return params;
    }


    @Override
    protected String buildRequestPath(String servicePath, String methodPath, Map<String, Object> requestParams, Serialization serialization, URL url) {
        servicePath = StringUtils.addPrefixIfNot(servicePath, PATH_PREFIX);
        servicePath = joinURL(servicePath, SOAContext.getUrlParams(), serialization, url);
        return  servicePath;
    }


    @Override
    protected Exception creteException(String body, int code, JsonRpcInvocation invocation, Serialization serialization, URL url) throws IOException {
        if (code == 500) {
            JsonNode responseNode = null;
            try {
                responseNode = serialization.deserializeString(url, body, JsonNode.class);
            } catch (IOException e) {
                return super.creteException(body, code, invocation, serialization, url);
            }
            return handleErrorResponse(responseNode, invocation);
        }
        return super.creteException(body, code, invocation, serialization, url);
    }


    @Override
    protected JsonNode resolveBody(String body, Class<?> returnType, boolean completeResult, Serialization serialization, URL url) throws IOException {
        return serialization.deserializeString(url, body, JsonNode.class);
    }


    @Override
    protected Exception handleErrorResponse(JsonNode jsonNode, JsonRpcInvocation invocation) {
        if (hasError(jsonNode)) {
            ObjectNode errorObject = (ObjectNode) jsonNode.get(ERROR);
            if (hasNonNullObjectData(errorObject)) {
                return createJsonRpcClientException(errorObject);
            }
            ObjectNode dataObject = (ObjectNode) errorObject.get(DATA);
            if (!hasNonNullTextualData(dataObject, EXCEPTION_TYPE_NAME)) {
                return createJsonRpcClientException(errorObject);
            }
            try {
                String exceptionTypeName = dataObject.get(EXCEPTION_TYPE_NAME).asText();
                String message = hasNonNullTextualData(dataObject, ERROR_MESSAGE) ? dataObject.get(ERROR_MESSAGE).asText() : null;
                Integer ret = hasNonNullData(dataObject, ERROR_RET) ? dataObject.get(ERROR_RET).asInt() : null;
                return createBusinessException(exceptionTypeName, ret, message);
            } catch (Exception e) {
                return createJsonRpcClientException(errorObject);
            }
        }
        return null;
    }


    private boolean hasNonNullObjectData(final JsonNode node) {
        return !hasNonNullData(node, DATA) || !node.get(DATA).isObject();
    }

    private boolean hasNonNullData(final JsonNode node, final String key) {
        return node.has(key) && !node.get(key).isNull();
    }

    private boolean hasNonNullTextualData(final JsonNode node, final String key) {
        return hasNonNullData(node, key) && node.get(key).isTextual();
    }

    private boolean hasError(JsonNode jsonObject) {
        return jsonObject.has(ERROR) && jsonObject.get(ERROR) != null && !jsonObject.get(ERROR).isNull();
    }

    private Exception createBusinessException(String exceptionClassName, Integer ret, String message) throws IllegalAccessException, InvocationTargetException, InstantiationException {
        Exception exception;
        Class<?> exceptionClass = EXCEPTION_TYPE_NAME_MAP.computeIfAbsent(exceptionClassName, className -> loadExceptionClass(exceptionClassName));
        //compatible
        Constructor<?> messageConstructor = getMessageConstructor(exceptionClass);
        if (message != null && messageConstructor != null) {
            exception = (Exception) messageConstructor.newInstance(message);
        } else {
            exception = (Exception) exceptionClass.newInstance();
        }
        ofNullable(ret).ifPresent(ret1 -> ofNullable(ReflectUtils.getField(exceptionClass, "ret")).ifPresent(field -> {
            try {
                ReflectUtils.setFieldValue(exception, field, ret1);
            } catch (IllegalAccessException e) {
                logger.warn("The RET field of the [" + exceptionClass + "]  is missing and cannot be filled,ret:[" + ret1 + "]");
            }
        }));
        ofNullable(message).ifPresent(message1 -> ofNullable(ReflectUtils.getField(exceptionClass, "msg")).ifPresent(field -> {
            try {
                ReflectUtils.setFieldValue(exception, field, message1);
            } catch (IllegalAccessException e) {
                logger.warn("The MSG field of the [" + exceptionClass + "]  is missing and cannot be filled,msg:[" + message1 + "]");
            }
        }));
        return exception;
    }


    protected Class<? extends Exception> loadExceptionClass(String typeName) {
        Class<?> clazz;
        try {
            clazz = Class.forName(typeName);
            if (!Exception.class.isAssignableFrom(clazz)) {
                logger.warn("Type does not inherit from Exception [" + clazz.getName() + "]");
            } else {
                return clazz.asSubclass(Exception.class);
            }
        } catch (Throwable e) {
            logger.warn("Unable to load Exception class [" + typeName + "]");
        }
        return UnkonwnBizException.class;
    }


    private Constructor<?> getMessageConstructor(Class<?> clazz) {
        Constructor<?> messageCtr = null;
        try {
            messageCtr = clazz.getConstructor(String.class);
        } catch (NoSuchMethodException ignore) {
        }
        return messageCtr;
    }

    private JsonRpcClientException createJsonRpcClientException(ObjectNode errorObject) {
        int code = errorObject.has(ERROR_CODE) ? errorObject.get(ERROR_CODE).asInt() : 0;
        return new JsonRpcClientException(code, errorObject.get(ERROR_MESSAGE).asText(), errorObject.get(DATA));
    }


    @Override
    protected Object doUnpack(JsonNode jsonNode, Class<?> returnType, Type genericReturnType, JsonRpcInvocation jsonRpcInvocation, Serialization serialization, URL url) throws IOException {
        JsonNode result = jsonNode.get(RESULT);
        if (result == null || result.isNull()) {
            return null;
        }
        return serialization.deserializeString(url, result.toString(), genericReturnType);
    }


}
