package cn.huolala.arch.hermes.compatible.protocol.interceptor;

import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

/**
 * Client Interceptor
 */
@SPI
public interface ClientInterceptor {

    /**
     * ClientInterceptor invoke
     *
     * @return the return value
     * @throws Throwable on error
     */
    Result invoke(InterceptorChain chain, Invocation invocation) throws RpcException;

}
