package cn.huolala.arch.hermes.compatible.protocol.interceptor;

import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.NetUtils;
import cn.huolala.arch.hermes.compatible.protocol.JsonRpcInvocation;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.lalaframework.soa.annotation.SOAContext;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.huolala.arch.hermes.common.constants.Constants.HOST_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.PROTOCOL_HEADER_PREFIX;
import static java.util.Optional.ofNullable;

@Activate
public class ContextInterceptor implements ClientInterceptor {


    @Override
    public Result invoke(InterceptorChain chain, Invocation invocation) throws RpcException {

        JsonRpcInvocation jsonRpcInvocation = (JsonRpcInvocation) invocation.get(JsonRpcInvocation.class.getName());
        Map<String, String> extraHeader = ofNullable(jsonRpcInvocation.getExtraHeader()).orElse(new HashMap<>());
        jsonRpcInvocation.setExtraHeader(extraHeader);

        Map<String, Object> attachments = ofNullable(invocation.getAttachments()).orElse(new HashMap<>()).entrySet().stream().filter(v -> v.getValue() != null)
                .collect(Collectors.toMap(v -> PROTOCOL_HEADER_PREFIX + v.getKey(), Map.Entry::getValue));

        // 兼容老的框架类SOAContext，优先级SOAContext < Attachments < RawAttachments
        Map<String, String> mergeHeader = Stream.of(SOAContext.getHeader(), attachments, invocation.getRawAttachments())
                .filter(map -> CollectionUtils.isNotEmptyMap(map))
                .flatMap(map -> map.entrySet().stream())
                .filter(v -> v.getValue() != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        v -> String.valueOf(v.getValue()),
                        (v1, v2) -> v2));

        extraHeader.putAll(mergeHeader);

        String host = jsonRpcInvocation.getHost();
        if (NetUtils.isValidIp(host)) {
            extraHeader.put(HOST_KEY, jsonRpcInvocation.getApplication());
        }

        Map<String, Object> fixedParams = jsonRpcInvocation.getFixedParams();
        Map<String, Object> request = SOAContext.getRequest();
        if (CollectionUtils.isNotEmptyMap(request)) {
            fixedParams.putAll(request);
        }

        return chain.invoke(invocation);
    }


}
