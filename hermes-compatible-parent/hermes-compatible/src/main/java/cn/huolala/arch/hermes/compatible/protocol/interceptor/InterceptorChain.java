package cn.huolala.arch.hermes.compatible.protocol.interceptor;

import cn.huolala.arch.hermes.compatible.protocol.invoker.InvocationInvoker;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

/**
 * Interceptor Chain
 */
public class InterceptorChain {
    private final ClientInterceptor interceptor;
    private final InterceptorChain next;

    public static InterceptorChain of(ClientInterceptor interceptor, InterceptorChain next) {
        return new InterceptorChain(interceptor, next);
    }

    public static InterceptorChain last(InvocationInvoker invoker) {
        return new LastInterceptorChain(invoker);
    }

    public InterceptorChain(ClientInterceptor interceptor, InterceptorChain next) {
        this.interceptor = interceptor;
        this.next = next;
    }

    public Result invoke(Invocation invocation) throws RpcException {
        return interceptor.invoke(next, invocation);
    }

    /**
     * Last Chain Node
     */
    static class LastInter<PERSON><PERSON><PERSON>n extends Interceptor<PERSON>hain {
        private final InvocationInvoker invoker;

        public LastInterceptorChain(InvocationInvoker invoker) {
            super(null, null);
            this.invoker = invoker;
        }

        @Override
        public Result invoke(Invocation invocation) throws RpcException {
            return invoker.invoke(invocation);
        }
    }
}
