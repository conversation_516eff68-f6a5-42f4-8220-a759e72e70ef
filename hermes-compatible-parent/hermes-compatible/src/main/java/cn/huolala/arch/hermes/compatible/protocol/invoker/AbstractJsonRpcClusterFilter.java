package cn.huolala.arch.hermes.compatible.protocol.invoker;

import cn.huolala.arch.hermes.cluster.ClusterFilter;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.core.RpcInvocation;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.lalaframework.soa.annotation.SOAContext;


public abstract class AbstractJsonRpcClusterFilter implements ClusterFilter {


    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        try {
            return doInvoke(invoker, (RpcInvocation) invocation);
        } finally {
            SOAContext.clearAll();
        }
    }



    abstract Result doInvoke(Invoker<?> invoker, RpcInvocation invocation);
}
