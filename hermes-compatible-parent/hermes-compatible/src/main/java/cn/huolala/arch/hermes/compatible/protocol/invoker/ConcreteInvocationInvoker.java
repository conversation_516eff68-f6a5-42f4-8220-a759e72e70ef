package cn.huolala.arch.hermes.compatible.protocol.invoker;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.event.EventDispatcher;
import cn.huolala.arch.hermes.common.logger.Level;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.protocol.JsonRpcInvocation;
import cn.huolala.arch.hermes.compatible.protocol.handler.JsonRpcResponse;
import cn.huolala.arch.hermes.compatible.protocol.handler.ParamsHandler;
import cn.huolala.arch.hermes.compatible.protocol.support.JsonRpcRequestProducer;
import cn.huolala.arch.hermes.compatible.protocol.support.JsonRpcResponseConsumer;
import cn.huolala.arch.hermes.compatible.protocol.support.StopWatch;
import cn.huolala.arch.hermes.common.event.MetricsEvent;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.core.RpcResult;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.huolala.arch.hermes.protocol.support.FutureAdapter;
import cn.huolala.arch.hermes.serialize.Serialization;
import cn.lalaframework.soa.annotation.SOAParamsMode;
import io.micrometer.core.instrument.Tag;
import org.apache.hc.client5.http.async.methods.SimpleBody;
import org.apache.hc.client5.http.async.methods.SimpleHttpRequest;
import org.apache.hc.client5.http.async.methods.SimpleHttpResponse;
import org.apache.hc.client5.http.impl.async.CloseableHttpAsyncClient;
import org.apache.hc.core5.concurrent.FutureCallback;
import org.apache.hc.core5.http.HttpStatus;
import org.apache.hc.core5.http.NameValuePair;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.huolala.arch.hermes.common.constants.Constants.COMMA_SEPARATOR;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.METRIC_SOA_CALL_STATUS_COUNT;
import static cn.huolala.arch.hermes.protocol.exception.RpcException.FORBIDDEN_EXCEPTION;
import static cn.huolala.arch.hermes.protocol.exception.RpcException.NETWORK_EXCEPTION;
import static io.micrometer.core.instrument.Tag.of;

public class ConcreteInvocationInvoker implements InvocationInvoker {

    private final EventDispatcher eventDispatcher = EventDispatcher.getDefaultExtension();

    private final URL url;

    protected final Serialization serialization;

    private final CloseableHttpAsyncClient asyncClient;

    private final Map<SOAParamsMode, ParamsHandler> paramsModelHandler;

    public ConcreteInvocationInvoker(URL url, Serialization serialization, CloseableHttpAsyncClient asyncClient,
                                     Map<SOAParamsMode, ParamsHandler> paramsModelHandler) {
        this.url = url;
        this.serialization = serialization;
        this.asyncClient = asyncClient;
        this.paramsModelHandler = paramsModelHandler;
    }


    @Override
    public Result invoke(Invocation invocation) throws RpcException {
        JsonRpcInvocation jsonRpcInvocation = (JsonRpcInvocation) invocation.get(JsonRpcInvocation.class.getName());
        if (jsonRpcInvocation == null) {
            throw new IllegalArgumentException("JsonRpcInvocation should have already set in Invocation attribute, "
                    + "but failed to find it,Invocation:[" + invocation + "]");
        }
        SOAParamsMode paramsMode = jsonRpcInvocation.getParamsMode();
        ParamsHandler paramsHandler = paramsModelHandler.get(paramsMode);

        RpcResult result;
        JsonRpcResponse response;
        try {
            response = doInvoke(invocation, jsonRpcInvocation, paramsHandler);
            if (jsonRpcInvocation.isAsync() || jsonRpcInvocation.isOneway()) {
                result = new RpcResult(FutureAdapter.toResponseFuture(response.getFuture(), invocation), invocation);
            } else {
                result = RpcResult.newRpcResult(response.recreate(), invocation);
            }
        } catch (Throwable e) {
            result = RpcResult.newRpcResult(e, invocation);
        }
        return result;
    }


    public JsonRpcResponse doInvoke(Invocation invocation, JsonRpcInvocation jsonRpcInvocation, ParamsHandler paramsHandler) throws Exception {
        SimpleHttpRequest httpRequest = paramsHandler.pack(invocation, jsonRpcInvocation, serialization, url);
        invocation.putDMCLog("URL", httpRequest.getUri());
        invocation.putDMCLog("PARAMS_MODEL", jsonRpcInvocation.getParamsMode());
        invocation.putDMCLog("REQUEST_BODY", () -> Optional.ofNullable(httpRequest.getBody()).map(SimpleBody::getBodyBytes).map(bytes -> new String(bytes, StandardCharsets.UTF_8)).orElse(StringUtils.EMPTY_STRING), Level.DEBUG);
        invocation.putDMCLog("REQUEST_HEADER", () -> Arrays.stream(httpRequest.getHeaders()).collect(Collectors.toMap(NameValuePair::getName, NameValuePair::getValue, (v1, v2) -> v1 + COMMA_SEPARATOR + v2)), Level.TRACE);

        String remoteAppId = invocation.getRemoteApplication();

        StopWatch stopWatch  = new StopWatch();
        JsonRpcResponse response = new JsonRpcResponse(stopWatch,invocation);
        this.asyncClient.execute(JsonRpcRequestProducer.create(httpRequest, response, stopWatch), JsonRpcResponseConsumer.create(stopWatch), new FutureCallback<SimpleHttpResponse>() {
            @Override
            public void completed(SimpleHttpResponse httpResponse) {
                try {
                    Object result = paramsHandler.unpack(httpResponse, invocation, jsonRpcInvocation, serialization, url);
                    invocation.putDMCLog("HTTP_CODE", httpResponse.getCode());
                    invocation.putDMCLog("RESPONSE_HEADER", () -> Arrays.stream(httpResponse.getHeaders()).collect(Collectors.toMap(NameValuePair::getName, NameValuePair::getValue, (v1, v2) -> v1 + COMMA_SEPARATOR + v2)), Level.TRACE);
                    response.succeeded(result);
                } catch (Throwable e) {
                    invocation.putDMCLog("HTTP_CODE", httpResponse.getCode());
                    invocation.putDMCLog("UNPACK_FAILED", e::getMessage, Level.ERROR);
                    response.failed(e);
                }finally {
                    try {
                        soaCallStatusMetric(remoteAppId, httpResponse.getCode());
                    }catch (Exception ignoreException ){}
                }
            }

            @Override
            public void failed(Exception ex) {
                invocation.putDMCLog("HTTP_CODE", NETWORK_EXCEPTION);
                invocation.putDMCLog("REQUEST_FAILED", ex::getMessage, Level.ERROR);
                response.failed(ex);
            }

            @Override
            public void cancelled() {
                invocation.putDMCLog("HTTP_CODE", FORBIDDEN_EXCEPTION);
                invocation.putDMCLog("REQUEST_CANCELLED", () -> jsonRpcInvocation, Level.WARN);
                response.cancelled();
            }
        });
        return response;
    }

    private void soaCallStatusMetric(String remoteAppId,int code) {

        if(code >= HttpStatus.SC_INTERNAL_SERVER_ERROR) {
            eventDispatcher.dispatch(new MetricsEvent(meterRegistry -> {
                List<Tag> tags = Arrays.asList(
                        of("to_appid", remoteAppId),
                        of("code", String.valueOf(code))
                );
                meterRegistry.counter(METRIC_SOA_CALL_STATUS_COUNT, tags).increment();
            }));
        }
    }
}
