package cn.huolala.arch.hermes.compatible.protocol.invoker;

import cn.huolala.arch.hermes.api.fallback.DefaultFallbackFactory;
import cn.huolala.arch.hermes.cluster.fallback.support.DefaultFallback;
import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.compatible.protocol.JsonRpcInvocation;
import cn.huolala.arch.hermes.protocol.InvokeMode;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.core.RpcContext;
import cn.huolala.arch.hermes.protocol.core.RpcInvocation;

import java.util.concurrent.Future;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.FALLBACK_KEY;
import static cn.huolala.arch.hermes.cluster.ClusterConstants.FILTER_ORDER_IGENERIC_JSONRPC_CLUSTER_CONTEXT;
import static cn.huolala.arch.hermes.common.util.StringUtils.isEmpty;
import static cn.huolala.arch.hermes.common.util.StringUtils.isEmptyApplication;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.ARGUMENTS;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.CONNECTION_TIMEOUT;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC_FILTER;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC_GENERIC_CONSUMER_FILTER;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.READ_TIMEOUT;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.RETURN_TYPE;
import static java.util.Optional.ofNullable;

/**
 * Set the generic json rpc consumer invocation
 *
 * @see RpcContext
 */
@Activate(value = {JSONRPC_FILTER + ":" + JSONRPC_GENERIC_CONSUMER_FILTER}, order = FILTER_ORDER_IGENERIC_JSONRPC_CLUSTER_CONTEXT)
public class IGenericJsonRpcClusterContextFilter extends AbstractJsonRpcClusterFilter {


    @Override
    Result doInvoke(Invoker<?> invoker, RpcInvocation rpcInvocation) {

        JsonRpcInvocation jsonRpcInvocation = (JsonRpcInvocation) rpcInvocation.getArguments()[0];
        if (isEmptyApplication(jsonRpcInvocation.getApplication()) && isEmpty(jsonRpcInvocation.getHost())) {
            throw new IllegalArgumentException("Generic invoker does not specify an appId or target host,jsonRpcInvocation:[" + jsonRpcInvocation + "]");
        }

        rpcInvocation.setRemoteApplication(jsonRpcInvocation.getApplication());
        rpcInvocation.setCommandKey(jsonRpcInvocation.getCommandKey());

        Class<?> returnType = (Class<?>) jsonRpcInvocation.getAttributes().get(RETURN_TYPE);
        rpcInvocation.setReturnType(returnType);
        rpcInvocation.setReturnTypes(ReflectUtils.getReturnTypes(returnType, returnType));
        if (Future.class.isAssignableFrom(returnType)) {
            rpcInvocation.setInvokeMode(InvokeMode.FUTURE);
        }
        Object[] arguments = (Object[]) jsonRpcInvocation.getAttributes().get(ARGUMENTS);
        rpcInvocation.setArguments(arguments);

        ofNullable(jsonRpcInvocation.getAttributes().get(READ_TIMEOUT)).ifPresent(timeout -> rpcInvocation.setTimeout((Integer) timeout));
        ofNullable(jsonRpcInvocation.getAttributes().get(CONNECTION_TIMEOUT)).ifPresent(connectionTimeout -> rpcInvocation.setConnectionTimeout((Integer) connectionTimeout));

        rpcInvocation.put(FALLBACK_KEY, ofNullable(jsonRpcInvocation.getAttributes().get(FALLBACK_KEY)).orElse(new DefaultFallbackFactory<>(DefaultFallback.getInstance())));
        rpcInvocation.getAttributes().put(JsonRpcInvocation.class.getName(), jsonRpcInvocation);

        return invoker.invoke(rpcInvocation);
    }

}
