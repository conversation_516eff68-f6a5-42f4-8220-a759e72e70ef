package cn.huolala.arch.hermes.compatible.protocol.invoker;

import cn.huolala.arch.hermes.api.config.MethodConfig;
import cn.huolala.arch.hermes.api.config.ReferenceConfigBase;
import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.context.model.ConsumerModel;
import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.common.util.CommandKeyUtils;
import cn.huolala.arch.hermes.compatible.protocol.JsonRpcInvocation;
import cn.huolala.arch.hermes.protocol.InvokeMode;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.RpcConstants;
import cn.huolala.arch.hermes.protocol.core.RpcContext;
import cn.huolala.arch.hermes.protocol.core.RpcInvocation;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.FILTER_ORDER_JSONRPC_CLUSTER_CONTEXT;
import static cn.huolala.arch.hermes.common.constants.Constants.ALL;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.IS_HTTPS;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC_CONSUMER_FILTER;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC_FILTER;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.SERVICE_PATH;

/**
 * Set the json rpc consumer invocation
 *
 * @see RpcContext
 */
@Activate(value = {JSONRPC_FILTER + ":" + JSONRPC_CONSUMER_FILTER}, order = FILTER_ORDER_JSONRPC_CLUSTER_CONTEXT)
public class JsonRpcClusterContextFilter extends AbstractJsonRpcClusterFilter {


    @Override
    Result doInvoke(Invoker<?> invoker, RpcInvocation rpcInvocation) {
        ConsumerModel consumerModel = (ConsumerModel) rpcInvocation.get(RpcConstants.CONSUMER_MODEL);
        if (consumerModel == null) {
            throw new IllegalArgumentException("ConsumerModel should have already been export in consumer repository, "
                    + "but failed to find it. Invocation:[" + rpcInvocation + "]");
        }

        ReferenceConfigBase<?> referenceConfig = consumerModel.getReferenceConfig();
        MethodConfig methodConfig = referenceConfig.getMethodConfig(rpcInvocation.getMethodName(), rpcInvocation.getParameterTypesDesc());
        rpcInvocation.setCommandKey(methodConfig.getCommandKey());
        rpcInvocation.setServiceCommandKey(rebuildKeyInConsumerSide(rpcInvocation.getRemoteApplication(),referenceConfig.getPath(), ALL));

        URL url = invoker.getUrl();
        JsonRpcInvocation jsonRpcInvocation = JsonRpcInvocation.builder()
                .application(rpcInvocation.getRemoteApplication())
                .async(rpcInvocation.getInvokeMode() == InvokeMode.FUTURE)
                .oneway(methodConfig.getOneWay())
                .https(url.getParameter(IS_HTTPS, false))
                .servicePath(url.getParameter(SERVICE_PATH))
                .methodPath(methodConfig.getPath())
                .rawMethodPath(methodConfig.getRawPath())
                .paramsName(methodConfig.getParamNames())
                .commandKey(methodConfig.getCommandKey())
                .ofAttribute(methodConfig.getAttributes())
                .build();

        rpcInvocation.getAttributes().put(JsonRpcInvocation.class.getName(), jsonRpcInvocation);
        return invoker.invoke(rpcInvocation);
    }

    private String rebuildKeyInConsumerSide(String remoteApplication, String serviceName, String methodName){
       return CommandKeyUtils.buildInConsumerSide(remoteApplication, serviceName, methodName);
    }

}
