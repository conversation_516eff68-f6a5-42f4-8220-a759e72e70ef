package cn.huolala.arch.hermes.compatible.protocol.invoker;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static java.lang.ThreadLocal.withInitial;

public class JsonRpcProviderContext {

    /**
     * JsonRpc Local Context
     */
    private static final ThreadLocal<JsonRpcProviderContext> INNER = withInitial(JsonRpcProviderContext::new);

    private HttpServletRequest request;

    private HttpServletResponse response;


    public HttpServletRequest getRequest() {
        return request;
    }

    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    public HttpServletResponse getResponse() {
        return response;
    }

    public void setResponse(HttpServletResponse response) {
        this.response = response;
    }

    /**
     * get and Init
     */
    public static JsonRpcProviderContext getContext() {
        return INNER.get();
    }

    /**
     * removeContext
     */
    public static void removeContext() {
        INNER.remove();
    }

}
