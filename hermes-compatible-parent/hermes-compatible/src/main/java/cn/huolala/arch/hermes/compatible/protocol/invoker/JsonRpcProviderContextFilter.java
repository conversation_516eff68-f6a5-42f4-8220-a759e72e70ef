package cn.huolala.arch.hermes.compatible.protocol.invoker;

import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.common.logger.Level;
import cn.huolala.arch.hermes.protocol.Filter;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.core.RpcContext;
import cn.huolala.arch.hermes.protocol.core.context.ContextCompatible;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.FILTER_ORDER_JSONRPC_PROVIDER_CONTEXT;
import static cn.huolala.arch.hermes.common.constants.Constants.PROVIDER;
import static cn.huolala.arch.hermes.common.constants.Constants.UP_STREAM_APP_ID;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC_FILTER;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.JSONRPC_PROVIDER_FILTER;
import static java.util.Optional.ofNullable;

/**
 * Set the json rpc provider invocation
 *
 * @see RpcContext
 */
@Activate(group = PROVIDER, value = {JSONRPC_FILTER + ":" + JSONRPC_PROVIDER_FILTER}, order = FILTER_ORDER_JSONRPC_PROVIDER_CONTEXT)
public class JsonRpcProviderContextFilter implements Filter {


    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        JsonRpcProviderContext context = JsonRpcProviderContext.getContext();

        Map<String, String> contextAttachments = ContextCompatible.getDefaultExtension().attachments();
        Optional.ofNullable(contextAttachments.get(UP_STREAM_APP_ID)).ifPresent(invocation::setRemoteApplication);

        ofNullable(context.getRequest()).ifPresent(httpRequest -> {
            invocation.putDMCLog("REQUEST_ADDRESS", httpRequest.getRemoteAddr());
            invocation.putDMCLog("REQUEST_PARAMS", invocation::getArguments, Level.DEBUG);
            invocation.putDMCLog("REQUEST_HEADER", () -> {
                Enumeration<String> headerNames = httpRequest.getHeaderNames();
                Map<String, Object> headers = new HashMap<>();
                while (headerNames.hasMoreElements()) {
                    String key = headerNames.nextElement();
                    headers.put(key, httpRequest.getHeader(key));
                }
                return headers;
            }, Level.TRACE);
        });
        return invoker.invoke(invocation).whenComplete((response, throwable) -> invocation.putDMCLog("RESPONSE_BODY", response::getValue, Level.DEBUG));
    }
}
