package cn.huolala.arch.hermes.compatible.protocol.invoker;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants;
import cn.huolala.arch.hermes.compatible.protocol.JsonRpcInvocation;
import cn.huolala.arch.hermes.compatible.protocol.interceptor.InterceptorChain;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.huolala.arch.hermes.protocol.jsonrpc.refined.RefinedInvoker;

import static cn.huolala.arch.hermes.common.util.StringUtils.addSuffixIfNot;
import static cn.huolala.arch.hermes.common.util.StringUtils.isEmpty;
import static cn.huolala.arch.hermes.common.util.StringUtils.removeStart;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.FROM_NODE;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;

public class JsonRpcRefinedInvoker<T> implements RefinedInvoker<T> {

    private final Class<T> type;

    private final URL url;

    private final InterceptorChain chain;

    public JsonRpcRefinedInvoker(Class<T> type, URL url, InterceptorChain chain) {
        this.type = type;
        this.url = url;
        this.chain = chain;
    }

    @Override
    public URL getUrl() {
        return url;
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public void destroy() {

    }

    @Override
    public Class<T> getInterface() {
        return type;
    }

    @Override
    public Result invoke(Invocation invocation) throws RpcException {
        JsonRpcInvocation jsonRpcInvocation = (JsonRpcInvocation) invocation.get(JsonRpcInvocation.class.getName());
        if (isEmpty(jsonRpcInvocation.getHost())) {
            of(url.getHost()).ifPresent(jsonRpcInvocation::setHost);
        }
        if (jsonRpcInvocation.getPort() <= 0) {
            of(url.getPort()).ifPresent(jsonRpcInvocation::setPort);
        }
        boolean appendContextPath = url.getParameter(JsonRpcConstants.APPEND_CONTEXT_PATH, false);
        if (appendContextPath) {
            ofNullable(url.getParameter(Constants.CONTEXT_PATH)).filter(path -> !StringUtils.isNullStrOrEmpty(path)).ifPresent(contextPath -> {
                String servicePath = jsonRpcInvocation.getServicePath();
                servicePath = addSuffixIfNot(contextPath, JsonRpcConstants.PATH_PREFIX) + removeStart(servicePath, JsonRpcConstants.PATH_PREFIX);
                jsonRpcInvocation.setServicePath(servicePath);
            });
        }
        jsonRpcInvocation.setHttps(!url.getParameter(FROM_NODE, false) && jsonRpcInvocation.isHttps());
        return chain.invoke(invocation);
    }
}
