package cn.huolala.arch.hermes.compatible.protocol.support;

import cn.huolala.arch.hermes.compatible.protocol.handler.JsonRpcResponse;
import org.apache.hc.client5.http.async.methods.SimpleBody;
import org.apache.hc.client5.http.async.methods.SimpleHttpRequest;
import org.apache.hc.core5.http.HttpException;
import org.apache.hc.core5.http.HttpRequest;
import org.apache.hc.core5.http.nio.AsyncEntityProducer;
import org.apache.hc.core5.http.nio.DataStreamChannel;
import org.apache.hc.core5.http.nio.RequestChannel;
import org.apache.hc.core5.http.nio.entity.BasicAsyncEntityProducer;
import org.apache.hc.core5.http.nio.entity.StringAsyncEntityProducer;
import org.apache.hc.core5.http.nio.support.BasicRequestProducer;
import org.apache.hc.core5.http.protocol.HttpContext;
import org.apache.hc.core5.util.Args;

import java.io.IOException;

public class JsonRpcRequestProducer extends BasicRequestProducer {

    private final StopWatch stopWatch;

    private final AsyncEntityProducer dataProducer;

    private final JsonRpcResponse response;

    private JsonRpcRequestProducer(HttpRequest request, JsonRpcResponse response, AsyncEntityProducer dataProducer, StopWatch stopWatch) {
        super(request, dataProducer);
        this.dataProducer = dataProducer;
        this.stopWatch = stopWatch;
        this.response = response;
        this.stopWatch.start("RC");
    }


    public static JsonRpcRequestProducer create(SimpleHttpRequest request, JsonRpcResponse response, StopWatch stopWatch) {
        Args.notNull(request, "Request");
        SimpleBody body = request.getBody();
        Object entityProducer;
        if (body != null) {
            if (body.isText()) {
                entityProducer = new StringAsyncEntityProducer(body.getBodyText(), body.getContentType());
            } else {
                entityProducer = new BasicAsyncEntityProducer(body.getBodyBytes(), body.getContentType());
            }
        } else {
            entityProducer = null;
        }

        return new JsonRpcRequestProducer(request, response, (AsyncEntityProducer) entityProducer, stopWatch);
    }

    @Override
    public void sendRequest(RequestChannel requestChannel, HttpContext httpContext) throws HttpException, IOException {
        // RC stop
        if (this.stopWatch.isRunning()) {
            this.stopWatch.stop();
        }
        this.stopWatch.start("TX");
        super.sendRequest(requestChannel, httpContext);
        if (this.dataProducer == null) {
            postSendRequest();
        }
    }


    @Override
    public void produce(DataStreamChannel channel) throws IOException {
        super.produce(channel);
        if (this.dataProducer != null) {
            postSendRequest();
        }
    }

    private void postSendRequest() {
        if (this.stopWatch.isRunning()) {
            this.stopWatch.stop();
        }
        this.stopWatch.start("RX");

        if (this.response != null) {
            this.response.startTimeoutChecker();
        }
    }
}
