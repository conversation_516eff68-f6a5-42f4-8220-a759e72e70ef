package cn.huolala.arch.hermes.compatible.protocol.support;

import org.apache.hc.client5.http.async.methods.SimpleHttpResponse;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.HttpException;
import org.apache.hc.core5.http.HttpResponse;
import org.apache.hc.core5.http.nio.AsyncEntityConsumer;
import org.apache.hc.core5.http.nio.entity.AbstractBinAsyncEntityConsumer;
import org.apache.hc.core5.http.nio.support.AbstractAsyncResponseConsumer;
import org.apache.hc.core5.http.protocol.HttpContext;
import org.apache.hc.core5.util.ByteArrayBuffer;

import java.io.IOException;
import java.nio.ByteBuffer;

public class JsonRpcResponseConsumer extends AbstractAsyncResponseConsumer<SimpleHttpResponse, byte[]> {

    private final StopWatch stopWatch;

    JsonRpcResponseConsumer(AsyncEntityConsumer<byte[]> entityConsumer,StopWatch stopWatch) {
        super(entityConsumer);
        this.stopWatch = stopWatch;
    }

    public static JsonRpcResponseConsumer create(StopWatch stopWatch) {
        return new JsonRpcResponseConsumer(new AbstractBinAsyncEntityConsumer<byte[]>(){

            private final ByteArrayBuffer buffer = new ByteArrayBuffer(1024);

            @Override
            public void releaseResources() {
                buffer.clear();
            }

            @Override
            protected int capacityIncrement() {
                return Integer.MAX_VALUE;
            }

            @Override
            protected void data(ByteBuffer src, boolean endOfStream) throws IOException {
                if (src == null) {
                    return;
                }
                if (src.hasArray()) {
                    buffer.append(src.array(), src.arrayOffset() + src.position(), src.remaining());
                } else {
                    while (src.hasRemaining()) {
                        buffer.append(src.get());
                    }
                }
            }

            @Override
            protected void streamStart(ContentType contentType) throws HttpException, IOException {

            }

            @Override
            protected byte[] generateContent() throws IOException {
                return buffer.toByteArray();
            }
        },stopWatch);
    }

    public void informationResponse(HttpResponse response, HttpContext context) throws HttpException, IOException {
    }

    protected SimpleHttpResponse buildResult(HttpResponse response, byte[] entity, ContentType contentType) {
        if (stopWatch.isRunning()){
            stopWatch.stop();
        }
        SimpleHttpResponse simpleResponse = SimpleHttpResponse.copy(response);
        if (entity != null) {
            simpleResponse.setBody(entity, contentType);
        }

        return simpleResponse;
    }
}
