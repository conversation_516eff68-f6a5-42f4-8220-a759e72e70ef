package cn.huolala.arch.hermes.compatible.protocol.support;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.NANO_TIME_UNIT;

public class StopWatch extends org.springframework.util.StopWatch {

    public StopWatch() {
        super("");
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        double totalTimeMillis = nanosToMillis(getTotalTimeNanos());
        sb.append("Total = ").append(totalTimeMillis).append(" ms");

        for (org.springframework.util.StopWatch.TaskInfo task : getTaskInfo()) {
            if (task != null){
                double taskMillis = nanosToMillis(task.getTimeNanos());
                sb.append("; [").append(task.getTaskName()).append("] ").append(taskMillis).append(" ms");
                long percent = Math.round(100.0 * taskMillis / totalTimeMillis);
                sb.append(" = ").append(percent).append("%");
            }
        }

        return sb.toString();
    }

    private double nanosToMillis(long duration) {
        return BigDecimal.valueOf((double) duration / NANO_TIME_UNIT).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }


}
