package cn.huolala.arch.hermes.compatible.strategy;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.config.dynamic.DynamicConfiguration;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigEvent;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigEventType;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigurationListener;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.discovery.DiscoveryStrategy;
import cn.huolala.arch.hermes.discovery.support.DiscoveryUtils;

import static cn.huolala.arch.hermes.common.rule.RouterConstants.Namespace.GLOBAL_GOVERNANCE;
import static cn.huolala.arch.hermes.common.rule.RouterConstants.Namespace.GOVERNANCE;


public class JsonRpcDiscoveryStrategy implements DiscoveryStrategy, ConfigurationListener {

    private static final Set<String> DISCOVERY_STRATEGY_PREFIX_SET = Collections.singleton("discovery");

    private static final Type DEFAULT_DISCOVERY_STRATEGY = Type.NODE;

    private static final String UNKNOWN_APPID = "unknown";

    private static final String KEY_DISCOVERY_APP_DOMAIN = "discovery.app.domain";

    private static final String KEY_DISCOVERY_APP_NODE = "discovery.app.node";

    private static final String KEY_DISCOVERY_APP_STRATEGY = "discovery.app.strategy";

    private static final String KEY_DISCOVERY_GLOBAL_STRATEGY = "discovery.global.strategy";

    private static final String KEY_DISCOVERY_ALL_CALL_DOMAIN = "discovery.global.all_call_domain";

    /**
     * Depending on config {@value KEY_DISCOVERY_APP_STRATEGY}
     */
    private AtomicReference<Type> discoveryAppStrategy = new AtomicReference<>();

    /**
     * Depending on config {@value KEY_DISCOVERY_GLOBAL_STRATEGY}
     */
    private AtomicReference<Type> discoveryGlobalStrategy = new AtomicReference<>();

    /**
     * Depending on config {@value KEY_DISCOVERY_APP_NODE}
     */
    private final AtomicReference<List<String>> nodeAppIds = new AtomicReference<>(new ArrayList<>());

    /**
     * Depending on config {@value KEY_DISCOVERY_APP_DOMAIN}
     */
    private final AtomicReference<List<String>> domainAppIds = new AtomicReference<>(new ArrayList<>());

    /**
     * Depending on config {@value KEY_DISCOVERY_ALL_CALL_DOMAIN}
     */
    private final AtomicReference<List<String>> forceDomainAppIds = new AtomicReference<>(new ArrayList<>());

    public JsonRpcDiscoveryStrategy() {
        DynamicConfiguration.getDynamicConfiguration().addListener(this, GOVERNANCE.getValue(),
                Collections.emptySet(),
                DISCOVERY_STRATEGY_PREFIX_SET,
                true);
        DynamicConfiguration.getDynamicConfiguration().addListener(this, GLOBAL_GOVERNANCE.getValue(),
                Collections.emptySet(),
                DISCOVERY_STRATEGY_PREFIX_SET,
                true);
    }

    @Override
    public void onEvent(ConfigEvent event) {
        String namespace = event.getGroup();
        String key = event.getKey();
        String value = event.getContent();
        if (event.getChangeType() == ConfigEventType.DELETED) {
            handleDeleteEvent(namespace, key);
            return;
        }
        handleEvent(namespace, key, value);
    }

    private void handleDeleteEvent(String namespace, String key) {
        if (StringUtils.equals(GLOBAL_GOVERNANCE.getValue(), namespace)) {
            Optional.of(key).filter(KEY_DISCOVERY_ALL_CALL_DOMAIN::equals)
                    .ifPresent(s -> forceDomainAppIds.set(Collections.emptyList()));

            Optional.of(key).filter(KEY_DISCOVERY_GLOBAL_STRATEGY::equals)
                    .ifPresent(s -> discoveryGlobalStrategy.set(null));

        } else if (StringUtils.equals(GOVERNANCE.getValue(), namespace)) {
            Optional.of(key).filter(KEY_DISCOVERY_APP_STRATEGY::equals).ifPresent(s -> discoveryAppStrategy.set(null));

            Optional.of(key).filter(KEY_DISCOVERY_APP_NODE::equals)
                    .ifPresent(s -> nodeAppIds.set(Collections.emptyList()));

            Optional.of(key).filter(KEY_DISCOVERY_APP_DOMAIN::equals)
                    .ifPresent(s -> domainAppIds.set(Collections.emptyList()));
        }
    }

    private void handleEvent(String namespace, String key, String value) {
        if (StringUtils.equals(GLOBAL_GOVERNANCE.getValue(), namespace)) {
            Optional.of(key).filter(KEY_DISCOVERY_ALL_CALL_DOMAIN::equals)
                    .map(s -> Arrays.asList(StringUtils.split(value, StringUtils.COMMA))).
                    ifPresent(forceDomainAppIds::set);

            Optional.of(key).filter(KEY_DISCOVERY_GLOBAL_STRATEGY::equals)
                    .ifPresent(s -> discoveryGlobalStrategy.set(Type.getOrDefault(value, null)));
        } else if (StringUtils.equals(GOVERNANCE.getValue(), namespace)) {
            Optional.of(key).filter(KEY_DISCOVERY_APP_STRATEGY::equals)
                    .ifPresent(s -> discoveryAppStrategy.set(Type.getOrDefault(value, null)));

            Optional.of(key).filter(KEY_DISCOVERY_APP_NODE::equals)
                    .map(s -> Arrays.asList(StringUtils.split(value, StringUtils.COMMA)))
                    .ifPresent(nodeAppIds::set);

            Optional.of(key).filter(KEY_DISCOVERY_APP_DOMAIN::equals)
                    .map(s -> Arrays.asList(StringUtils.split(value, StringUtils.COMMA)))
                    .ifPresent(domainAppIds::set);
        }
    }

    @Override
    public List<Type> getTypes(URL consumerUrl) {
        List<Type> types = new ArrayList<>();
        String serviceName = DiscoveryUtils.getConsumerServiceName(consumerUrl);
        if (callByNode(serviceName)) {
            types.add(Type.NODE);
        }
        types.add(Type.DOMAIN);
        return types;
    }

    private boolean callByNode(String appId) {
        if (forceDomainAppIds.get().contains(appId)) {
            return false;
        }
        if (latestDiscoveryStrategy().equals(Type.DOMAIN)) {
            return nodeAppIds.get().contains(appId);
        }
        return !domainAppIds.get().contains(appId);
    }

    /**
     * priority : appLevel > global
     *
     * @return latest {@link DiscoveryStrategy}
     */
    private Type latestDiscoveryStrategy() {
        return Optional.ofNullable(discoveryAppStrategy.get()).orElse(
                Optional.ofNullable(discoveryGlobalStrategy.get()).orElse(DEFAULT_DISCOVERY_STRATEGY));
    }
}
