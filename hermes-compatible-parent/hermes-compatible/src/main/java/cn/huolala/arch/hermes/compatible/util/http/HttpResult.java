package cn.huolala.arch.hermes.compatible.util.http;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;

@ApiAudience.Private
public class HttpResult {
    public HttpResult() {
    }

    private int code;

    private String body;

    public HttpResult(int code, String body) {
        this.code = code;
        this.body = body;
    }

    public HttpResult(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }
}
