package cn.huolala.arch.hermes.compatible.util.http;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.classic.methods.HttpUriRequestBase;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.entity.EntityBuilder;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ClassicHttpRequest;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.HttpStatus;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.net.URIBuilder;

@ApiAudience.Private
public final class HttpUtils {

    private HttpUtils() {
    }


    /**
     * Default RequestConfig:
     * connectTimeout: 1s
     * responseTimeout: 6s
     */
    private static final RequestConfig DEFAULT_REQUEST_CONFIG = RequestConfig.custom()
            .setConnectTimeout(1000, TimeUnit.MILLISECONDS)
            .setResponseTimeout(6000, TimeUnit.MILLISECONDS)
            .build();


    public static HttpResult doGet(String url) throws Exception {
        return doGet(url, null, null, null);
    }

    public static HttpResult doPost(String url, String bodyString) throws Exception {
        return doPost(url, null, bodyString, null);
    }

    public static HttpResult doGet(String url, Map<String, String> params) throws Exception {
        return doGet(url, null, params, null);
    }

    public static HttpResult doGet(String url, Map<String, String> params, RequestConfig requestConfig) throws Exception {
        return doGet(url, null, params, requestConfig);
    }

    public static HttpResult doPost(String url, String bodyString, RequestConfig requestConfig) throws Exception {
        return doPost(url, null, bodyString, requestConfig);
    }


    public static HttpResult doGet(String url, Map<String, String> headers, Map<String, String> params,
                                   RequestConfig requestConfig) throws Exception {
        URIBuilder uriBuilder = new URIBuilder(url);
        if (Objects.nonNull(params)) {
            Set<Map.Entry<String, String>> entrySet = params.entrySet();
            for (Map.Entry<String, String> entry : entrySet) {
                uriBuilder.setParameter(entry.getKey(), entry.getValue());
            }
        }
        HttpGet httpGet = new HttpGet(uriBuilder.build());
        httpGet.setConfig(Optional.ofNullable(requestConfig).orElse(DEFAULT_REQUEST_CONFIG));
        addHeader(headers, httpGet);
        return execute(httpGet);
    }


    public static HttpResult doPost(String url, Map<String, String> headers, String bodyString,
                                    RequestConfig requestConfig) throws Exception {
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(Optional.ofNullable(requestConfig).orElse(DEFAULT_REQUEST_CONFIG));
        addHeader(headers, httpPost);
        HttpEntity httpEntity = EntityBuilder.create().setContentType(ContentType.APPLICATION_JSON).setText(bodyString).build();
        httpPost.setEntity(httpEntity);
        return execute(httpPost);
    }


    private static void addHeader(Map<String, String> header, HttpUriRequestBase httpMethod) {
        if (Objects.nonNull(header)) {
            Set<Map.Entry<String, String>> entrySet = header.entrySet();
            for (Map.Entry<String, String> entry : entrySet) {
                httpMethod.setHeader(entry.getKey(), entry.getValue());
            }
        }
    }

    private static HttpResult execute(ClassicHttpRequest httpMethod) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse httpResponse = null;
        try {
            httpResponse = httpClient.execute(httpMethod);
            if (Objects.nonNull(httpResponse)) {
                int code = httpResponse.getCode();
                HttpEntity entity = httpResponse.getEntity();
                String bodyString = "";
                if (Objects.nonNull(entity)) {
                    bodyString = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                }
                return new HttpResult(code, bodyString);
            }
        } finally {
            release(httpResponse, httpClient);
        }
        return new HttpResult(HttpStatus.SC_INTERNAL_SERVER_ERROR);
    }


    /**
     * release httpResponse and httpClient
     */
    private static void release(CloseableHttpResponse httpResponse, CloseableHttpClient httpClient) throws IOException {
        if (httpResponse != null) {
            httpResponse.close();
        }
        if (httpClient != null) {
            httpClient.close();
        }
    }
}
