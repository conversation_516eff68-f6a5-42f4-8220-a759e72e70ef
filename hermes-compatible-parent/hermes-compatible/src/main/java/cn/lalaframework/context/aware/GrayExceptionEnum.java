package cn.lalaframework.context.aware;

public enum GrayExceptionEnum {

    CALL_DEFAULT_BUT_CALL_GRAY(1, "call_default_but_call_gray"),
    INVALID_GRAY_VERSION(2, "invalid_gray_version"),
    CALL_INCONSISTENT_GRAY_VERSION(3, "call_inconsistent_gray_version"),

    CALL_GRAY_BUT_CALL_DEFAULT(4, "call_gray_but_call_default");

    private final int code;

    private final String memo;

    public int getCode() {
        return code;
    }

    public String getMemo() {
        return memo;
    }

    GrayExceptionEnum(int code, String memo) {
        this.code = code;
        this.memo = memo;
    }

}
