package cn.lalaframework.context.aware;

import cn.huolala.arch.hermes.rule.gray.GrayRuleRepository;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.event.EventDispatcher;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.common.event.MetricsEvent;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

import io.micrometer.core.instrument.Tag;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.RELEASE_VERSION_DEFAULT_VALUE;
import static cn.huolala.arch.hermes.common.constants.Constants.GRAY_VERSION_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.UP_STREAM_APP_ID;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.METRIC_GRAY_EXCEPTION_COUNT;
import static io.micrometer.core.instrument.Tag.of;
import static java.util.Optional.ofNullable;

public class GrayRouterGuard implements Guard {

    private static final Logger logger = LoggerFactory.getLogger(GrayRouterGuard.class);

    private final EventDispatcher eventDispatcher = EventDispatcher.getDefaultExtension();

    private static final GrayRouterGuard INSTANCE = new GrayRouterGuard();

    public static GrayRouterGuard getInstance() {
        return INSTANCE;
    }

    public void inspected(HttpServletRequest request, HttpServletResponse response) {
        String grayVersion = request.getHeader(GRAY_VERSION_KEY);
        String appId = ofNullable(request.getHeader(UP_STREAM_APP_ID)).orElse("-");
        String application = ApplicationContext.getApplicationConfig().getName();
        String releaseVersion = ApplicationContext.getApplicationConfig().getReleaseVersion();
        String requestURI = request.getRequestURI();

        //如果请求流量携带的灰度标识为空 -->  不做任何处理
        if (!StringUtils.isEmpty(grayVersion)) {
            //灰度标识不为空且等于DEFAULT值
            if (StringUtils.equals(grayVersion, RELEASE_VERSION_DEFAULT_VALUE)) {
                //正式流量请求到灰度节点
                if (!isFormalRouter(releaseVersion)) {
                    grayMetric(appId, releaseVersion, grayVersion, GrayExceptionEnum.CALL_DEFAULT_BUT_CALL_GRAY);
                    writeResponseAndThrow(response, grayVersion, appId, requestURI, releaseVersion, "The upstream request is a formal request, but the currently node is a gray node");
                }
            } else {
                GrayRuleRepository grayRuleRepository = GrayRuleRepository.getDefaultExtension();
                //检查灰度标识的合法性 --> 是否存在发布计划中
                if (!grayRuleRepository.isGrayRoute(grayVersion)) {
                    grayMetric(appId, releaseVersion, grayVersion, GrayExceptionEnum.INVALID_GRAY_VERSION);
                    writeResponseAndThrow(response, grayVersion, appId, requestURI, releaseVersion, "The upstream request is a gray request, but the gray version is not exists in any gray plan");
                }
                //当前节点是灰度节点并且与请求灰度标标识不一致
                if (!isFormalRouter(releaseVersion) && !StringUtils.equals(grayVersion, releaseVersion)) {
                    grayMetric(appId, releaseVersion, grayVersion, GrayExceptionEnum.CALL_INCONSISTENT_GRAY_VERSION);
                    writeResponseAndThrow(response, grayVersion, appId, requestURI, releaseVersion, "The upstream request is a gray request, but the request gray version is not equal to the currently release version");
                }

                //当前节点是正常节点,但是灰度标签所对应的发布计划中包含当前APPID
                if (isFormalRouter(releaseVersion) && grayRuleRepository.inGrayRoute(grayVersion, application)) {
                    grayMetric(appId, releaseVersion, grayVersion, GrayExceptionEnum.CALL_GRAY_BUT_CALL_DEFAULT);
                    writeResponseAndThrow(response, grayVersion, appId, requestURI, releaseVersion, "The upstream request is a gray request and gray plan contains the appId but the current node is not a gray node");
                }
            }
        }

    }


    private void grayMetric(String appId, String releaseVersion, String grayVersion, GrayExceptionEnum grayExceptionEnum) {
        eventDispatcher.dispatch(new MetricsEvent(meterRegistry -> {
            List<Tag> tags = Arrays.asList(
                    of("upstream_appid", appId),
                    of("release_version", releaseVersion),
                    of("gray_version", grayVersion),
                    of("error", grayExceptionEnum.getMemo())
            );
            meterRegistry.counter(METRIC_GRAY_EXCEPTION_COUNT, tags).increment();
        }));
    }


    private boolean isFormalRouter(String routerVersion) {
        return StringUtils.isEmpty(routerVersion) || StringUtils.equals(routerVersion, RELEASE_VERSION_DEFAULT_VALUE);
    }

    private void writeResponseAndThrow(HttpServletResponse response, String grayVersion, String appId, String requestURI, String releaseVersion, String errorMsg) {
        try {
            String template = "UP_GRAY_VERSION:[" + grayVersion + "], UP_APPID:[" + appId + "], URI:[" + requestURI + "], RELEASE_VERSION:[" + releaseVersion + "], MSG:["+ errorMsg +"]";
            response.getOutputStream().write(template.getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            logger.warn("unable to response gray error info : " + e.getMessage());
        }
        throw new RpcException(RpcException.FORBIDDEN_EXCEPTION, errorMsg);
    }

}
