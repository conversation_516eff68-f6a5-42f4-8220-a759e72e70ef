package cn.lalaframework.context.aware.filter;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.lalaframework.context.aware.interceptor.ContextAwareHandler;
import org.springframework.web.filter.OncePerRequestFilter;

public class InvocationContextFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        ContextAwareHandler.getInstance().retrieveContextParams(request);
        try {
            filterChain.doFilter(request, response);
        } finally {
            ContextAwareHandler.getInstance().clearContextParams();
        }
    }
}

