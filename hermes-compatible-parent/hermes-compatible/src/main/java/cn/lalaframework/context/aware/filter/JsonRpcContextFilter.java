package cn.lalaframework.context.aware.filter;

import cn.huolala.arch.hermes.protocol.core.RpcContext;
import cn.lalaframework.context.aware.GrayRouterGuard;
import cn.lalaframework.context.aware.interceptor.RpcContextAwareHandler;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;



public class JsonRpcContextFilter extends OncePerRequestFilter {

    private final GrayRouterGuard grayRouterGuard;

    public JsonRpcContextFilter(GrayRouterGuard grayRouterGuard) {
        this.grayRouterGuard = grayRouterGuard;
    }


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Filter<PERSON>hain filterChain) throws ServletException, IOException {
        try {
            grayRouterGuard.inspected(request, response);
            RpcContextAwareHandler.fillRpcContext(request);
            filterChain.doFilter(request, response);
        } finally {
            RpcContext.removeContext();
        }
    }
}

