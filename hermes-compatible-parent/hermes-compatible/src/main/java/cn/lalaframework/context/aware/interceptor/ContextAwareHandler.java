package cn.lalaframework.context.aware.interceptor;

import cn.lalaframework.context.core.ContextHolder;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * RPC上下文解析器
 */
public class ContextAwareHandler {

    private static final ContextAwareHandler INSTANCE = new ContextAwareHandler();

    private ContextAwareHandler() { }

    public static ContextAwareHandler getInstance() {
        return INSTANCE;
    }

    public void retrieveContextParams(HttpServletRequest request) {
        Map<String, String> contextHeaders = new HashMap<>();
        Enumeration<String> names = request.getHeaderNames();
        while (names.hasMoreElements()) {
            String name = names.nextElement();
            if (ContextHolder.isHdrTransmittable(name.toLowerCase())) {
                contextHeaders.put(name.toLowerCase(), request.getHeader(name));
            }
        }

        ContextHolder.getInstance().setContextHeaders(contextHeaders);
    }

    public void clearContextParams() {
        ContextHolder.getInstance().clear();
    }
}
