package cn.lalaframework.context.aware.interceptor;

import org.springframework.core.Ordered;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 请求访问拦截器，在调用handler之前、后执行
 */
public class InvocationContextInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        ContextAwareHandler.getInstance().retrieveContextParams(request);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        ContextAwareHandler.getInstance().clearContextParams();
    }

    /**
     * Mount current interceptor object as part of chain handling incoming request.
     */
    public void mount(InterceptorRegistry registry) {
        InterceptorRegistration registration = registry.addInterceptor(this);
        registration.order(Ordered.HIGHEST_PRECEDENCE);
        registration.addPathPatterns("/**");
        registration.excludePathPatterns(
                "/**/*.html",
                "/**/*.htm",
                "/**/*.xhtml",
                "/**/*.js",
                "/**/*.css",
                "/**/*.gif",
                "/**/*.jpeg",
                "/**/*.jpg",
                "/**/*.png",
                "/**/*.ico",
                "/**/*.svg",
                "/**/*.webp",
                "/**/*.bmp",
                "/**/*.eot",
                "/**/*.otf",
                "/**/*.fon",
                "/**/*.font",
                "/**/*.woff",
                "/**/*.ttf",
                "/**/*.ttc"
        );
    }
}
