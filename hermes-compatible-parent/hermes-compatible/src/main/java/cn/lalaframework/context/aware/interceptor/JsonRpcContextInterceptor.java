package cn.lalaframework.context.aware.interceptor;

import cn.huolala.arch.hermes.protocol.core.RpcContext;

import cn.lalaframework.context.aware.GrayRouterGuard;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class JsonRpcContextInterceptor extends InvocationContextInterceptor {

    private final GrayRouterGuard grayRouterGuard;

    public JsonRpcContextInterceptor(GrayRouterGuard grayRouterGuard) {
        this.grayRouterGuard = grayRouterGuard;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        this.grayRouterGuard.inspected(request, response);
        RpcContextAwareHandler.fillRpcContext(request);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        RpcContext.removeContext();
    }

}
