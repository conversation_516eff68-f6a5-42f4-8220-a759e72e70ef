package cn.lalaframework.context.aware.interceptor;

import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.protocol.core.RpcContext;
import cn.huolala.arch.hermes.protocol.core.context.ContextAttachment;
import cn.huolala.arch.hermes.protocol.core.context.ServiceContext;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

import static cn.huolala.arch.hermes.common.constants.Constants.GRAY_VERSION_KEY;

public class RpcContextAwareHandler {

    private static final String HEADER_PREFIX = Constants.PROTOCOL_HEADER_PREFIX;
    private static final String RPC_HEADER_PREFIX = Constants.RPC_CONTEXT_BASE_PREFIX;

    public static void fillRpcContext(HttpServletRequest request) {
        Enumeration<String> headerNames = request.getHeaderNames();
        final Map<String, Object> serverAttachments = new HashMap<>();
        final Map<String, Object> rpcAttachments = new HashMap<>();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement().toLowerCase();
            if (name.startsWith(HEADER_PREFIX)) {
                serverAttachments.put(name.substring(HEADER_PREFIX.length()), request.getHeader(name));
            } else if (name.toLowerCase().startsWith(RPC_HEADER_PREFIX) || name.equalsIgnoreCase(GRAY_VERSION_KEY)) {
                rpcAttachments.put(name, request.getHeader(name));
            }
        }

        ContextAttachment contextAttachment = new ContextAttachment();
        contextAttachment.putAllAttachments(serverAttachments);
        RpcContext.setServerAttachment(contextAttachment);

        ServiceContext serviceContext = new ServiceContext();
        serviceContext.putAllAttachments(rpcAttachments);
        serviceContext.setRemoteAddress(request.getRemoteAddr(), request.getRemotePort());
        RpcContext.setServiceContext(serviceContext);
    }

}
