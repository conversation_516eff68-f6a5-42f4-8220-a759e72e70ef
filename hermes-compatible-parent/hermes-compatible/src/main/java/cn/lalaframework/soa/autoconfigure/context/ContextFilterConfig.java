package cn.lalaframework.soa.autoconfigure.context;

import cn.lalaframework.context.aware.GrayRouterGuard;
import cn.lalaframework.context.aware.filter.InvocationContextFilter;
import cn.lalaframework.context.aware.filter.JsonRpcContextFilter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration;


@Configuration
@ConditionalOnMissingBean(value = DelegatingWebMvcConfiguration.class)
public class ContextFilterConfig {

    private static final String URL_PATTERNS_ALL = "/*";

    @Bean
    public FilterRegistrationBean<InvocationContextFilter> invocationContextFilter() {
        FilterRegistrationBean<InvocationContextFilter> filterBean = new FilterRegistrationBean<>();
        filterBean.setFilter(new InvocationContextFilter());
        filterBean.addUrlPatterns(URL_PATTERNS_ALL);
        filterBean.setEnabled(true);
        filterBean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return filterBean;
    }

    @Bean
    public FilterRegistrationBean<JsonRpcContextFilter> jsonRpcContextFilter() {
        FilterRegistrationBean<JsonRpcContextFilter> filterBean = new FilterRegistrationBean<>();
        filterBean.setFilter(new JsonRpcContextFilter(GrayRouterGuard.getInstance()));
        filterBean.addUrlPatterns(URL_PATTERNS_ALL);
        filterBean.setEnabled(true);
        filterBean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return filterBean;
    }
}
