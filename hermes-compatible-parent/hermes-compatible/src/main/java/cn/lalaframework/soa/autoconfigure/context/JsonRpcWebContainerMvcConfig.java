package cn.lalaframework.soa.autoconfigure.context;

import cn.lalaframework.context.aware.GrayRouterGuard;
import cn.lalaframework.context.aware.interceptor.JsonRpcContextInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


@Configuration
public class JsonRpcWebContainerMvcConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        JsonRpcContextInterceptor interceptor = new JsonRpcContextInterceptor(GrayRouterGuard.getInstance());
        interceptor.mount(registry);
    }
}
