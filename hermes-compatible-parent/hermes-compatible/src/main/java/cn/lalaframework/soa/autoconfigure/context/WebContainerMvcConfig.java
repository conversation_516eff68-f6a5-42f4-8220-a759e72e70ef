package cn.lalaframework.soa.autoconfigure.context;

import cn.lalaframework.context.aware.interceptor.InvocationContextInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 注册透传header的拦截器
 */
@Configuration
public class WebContainerMvcConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        InvocationContextInterceptor interceptor = new InvocationContextInterceptor();
        interceptor.mount(registry);
    }
}
