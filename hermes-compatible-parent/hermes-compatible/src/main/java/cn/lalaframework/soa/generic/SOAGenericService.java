package cn.lalaframework.soa.generic;

import cn.huolala.arch.hermes.compatible.protocol.IJsonRpcGenericServiceAdapter;
import cn.lalaframework.soa.annotation.SOAResultMode;

import java.util.HashMap;
import java.util.Map;

public class SOAGenericService {

    /**
     * extraHttpHeaders
     */
    private Map<String, String> extraHttpHeaders = new HashMap<>();


    private final SOAServiceAttrs soaServiceAttrs;

    /**
     * Generate a generic service
     * <p>
     * serviceId = provider appId
     */
    public static SOAGenericService getService(String serviceId) {
        return getService(serviceId, null);
    }

    /**
     * Generate a generic service
     * serviceId = provider appId
     * servicePath = provider request path
     */
    public static SOAGenericService getService(String serviceId, String servicePath) {
        return new SOAGenericService(serviceId, servicePath);
    }


    /**
     * Generate a generic service
     * soaServiceAttrs
     */
    public static SOAGenericService getService(SOAServiceAttrs soaServiceAttrs) {
        return new SOAGenericService(soaServiceAttrs);
    }


    /**
     * constructor
     */
    public SOAGenericService(SOAServiceAttrs soaServiceAttrs) {
        this.soaServiceAttrs = soaServiceAttrs;
    }

    /**
     * constructor
     */
    public SOAGenericService(String serviceId, String servicePath) {
        soaServiceAttrs = new SOAServiceAttrs();
        soaServiceAttrs.setServiceId(serviceId);
        soaServiceAttrs.setServicePath(servicePath);
    }


    public <T> T callForObject(Class<T> responseType, SOAMethodAttrs rpcAttrs, T fallbackObj, Object... argument) throws Throwable {
        return callForObject(responseType, rpcAttrs, makeFallback(fallbackObj, argument), argument);
    }

    public <T> T callForObject(Class<T> responseType, SOAMethodAttrs rpcAttrs, SOAFallback<T> fallback, Object... argument) throws Throwable {
        rpcAttrs.setResultMode(SOAResultMode.OBJECT);
        return call(responseType, rpcAttrs, fallback, argument);
    }

    public String callForString(SOAMethodAttrs rpcAttrs, String fallbackObj, Object... argument) throws Throwable {
        return callForString(rpcAttrs, makeFallback(fallbackObj, argument), argument);
    }

    public String callForString(SOAMethodAttrs rpcAttrs, SOAFallback<String> fallback, Object... argument) throws Throwable {
        rpcAttrs.setResultMode(SOAResultMode.STRING);
        return call(String.class, rpcAttrs, fallback, argument);
    }

    public <T> T call(Class<T> retType, SOAMethodAttrs rpcAttrs, T fallbackObj, Object... argument) throws Throwable {
        return call(retType, rpcAttrs, makeFallback(fallbackObj), argument);
    }

    /**
     * if method's type is oneWay,skip govern's logic ,directly invoke
     */
    @SuppressWarnings("checkstyle:IllegalThrows")
    public void callOneWay(SOAMethodAttrs rpcAttrs, Object... argument) throws Throwable {
        rpcAttrs.setOneWay(Boolean.TRUE);
        callForString(rpcAttrs, "", argument);
    }

    /**
     *
     */
    public <T> T call(Class<T> retType, SOAMethodAttrs rpcAttrs, SOAFallback<T> soaFallback, Object... argument) throws Throwable {
        return IJsonRpcGenericServiceAdapter.call(soaServiceAttrs.getServiceId(), soaServiceAttrs.getServicePath(), soaServiceAttrs.isHttps(),rpcAttrs, soaFallback, argument, extraHttpHeaders, retType);
    }


    private static <T> SOAFallback<T> makeFallback(T fallbackObj, Object... argument) {
        return argument1 -> fallbackObj;
    }

    @Deprecated
    public void setConnectionTimeoutMillis(int connectionTimeoutMillis) {

    }

    @Deprecated
    public void setReadTimeoutMillis(int readTimeoutMillis) {

    }


    public void setExtraHttpHeaders(Map<String, String> extraHttpHeaders) {
        this.extraHttpHeaders = extraHttpHeaders;
    }
}
