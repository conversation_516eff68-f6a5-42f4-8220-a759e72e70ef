package cn.lalaframework.soa.generic;

import cn.lalaframework.soa.annotation.SOAParamsMethod;
import cn.lalaframework.soa.annotation.SOAParamsMode;
import cn.lalaframework.soa.annotation.SOAResultMode;

import java.net.URL;
import java.util.HashMap;
import java.util.Map;

public class SOAMethodAttrs {

    /**
     * service name
     */
    private String serviceKey;

    /**
     * appId
     */
    private String serviceId;

    /**
     * The method name
     */
    private String methodName;

    /**
     * The method description
     */
    private String description;

    /**
     * The governance of commandKey
     */
    private String commandKey;

    /**
     * The request method param type
     */
    private SOAParamsMethod paramsMethod = SOAParamsMethod.POST;

    /**
     * The request method params model
     */
    private SOAParamsMode paramsMode = SOAParamsMode.PARAMS;

    /**
     * The result model
     * since 1.3.1 will resolve method result type
     */
    @Deprecated
    private SOAResultMode resultMode = SOAResultMode.OBJECT;

    /**
     * oneWay no mind about the result of request ,default is false
     */
    private Boolean isOneWay = Boolean.FALSE;

    /**
     * fixed params append to request suffix
     */
    private Map<String, String> fixedParams = new HashMap<>();

    /**
     * SOAMethod annotation value
     */
    private String methodPath;

    /**
     * true  -> full return type will be not do any unpacked
     * false -> depends on the implementer of remoting
     */
    private Boolean completeResult = true;

    /**
     * connectionTimeoutMillis
     * default 1000ms
     */
    private int connectionTimeoutMillis;

    /**
     * readTimeoutMillis
     * default 6000ms
     */
    private int readTimeoutMillis;

    /**
     * true fixedParams will append to request suffix
     */
    private boolean fixedParamsAppend = false;

    private URL url;

    public SOAMethodAttrs() {
    }

    public SOAMethodAttrs(String methodName, SOAParamsMode paramsMode, SOAResultMode resultMode) {
        this.methodName = methodName;
        this.paramsMode = paramsMode;
        this.resultMode = resultMode;
    }

    public SOAMethodAttrs(String methodName, SOAParamsMode paramsMode, SOAResultMode resultMode, String commandKey) {
        this.methodName = methodName;
        this.paramsMode = paramsMode;
        this.resultMode = resultMode;
        this.commandKey = commandKey;
    }


    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public SOAParamsMode getParamsMode() {
        return paramsMode;
    }

    public void setParamsMode(SOAParamsMode paramsMode) {
        this.paramsMode = paramsMode;
    }

    public SOAMethodAttrs(String methodName, SOAParamsMode paramsMode) {
        this.methodName = methodName;
        this.paramsMode = paramsMode;
    }

    public SOAMethodAttrs(String methodName) {
        this.methodName = methodName;
    }

    public SOAResultMode getResultMode() {
        return resultMode;
    }

    public void setResultMode(SOAResultMode resultMode) {
        this.resultMode = resultMode;
    }

    public String getServiceKey() {
        return serviceKey;
    }

    public void setServiceKey(String serviceKey) {
        this.serviceKey = serviceKey;
    }

    public Map<String, String> getFixedParams() {
        return fixedParams;
    }

    public void setFixedParams(Map<String, String> fixedParams) {
        this.fixedParams = fixedParams;
    }

    public String getMethodPath() {
        return methodPath;
    }

    public void setMethodPath(String methodPath) {
        this.methodPath = methodPath;
    }

    public SOAParamsMethod getParamsMethod() {
        return paramsMethod;
    }

    public void setParamsMethod(SOAParamsMethod paramsMethod) {
        this.paramsMethod = paramsMethod;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public Boolean getCompleteResult() {
        return completeResult;
    }

    public void setCompleteResult(Boolean completeResult) {
        this.completeResult = completeResult;
    }

    public URL getUrl() {
        return url;
    }

    public void setUrl(URL url) {
        this.url = url;
    }

    public int getConnectionTimeoutMillis() {
        return connectionTimeoutMillis;
    }

    public void setConnectionTimeoutMillis(int connectionTimeoutMillis) {
        this.connectionTimeoutMillis = connectionTimeoutMillis;
    }

    public int getReadTimeoutMillis() {
        return readTimeoutMillis;
    }

    public void setReadTimeoutMillis(int readTimeoutMillis) {
        this.readTimeoutMillis = readTimeoutMillis;
    }

    public boolean getFixedParamsAppend() {
        return fixedParamsAppend;
    }

    public void setFixedParamsAppend(boolean fixedParamsAppend) {
        this.fixedParamsAppend = fixedParamsAppend;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCommandKey() {
        return commandKey;
    }

    public void setCommandKey(String commandKey) {
        this.commandKey = commandKey;
    }

    public Boolean getOneWay() {
        return isOneWay;
    }

    public void setOneWay(Boolean oneWay) {
        isOneWay = oneWay;
    }
}
