package cn.lalaframework.soa.generic;

public class SOAServiceAttrs {

    /**
     * serviceModule
     */
    private String serviceModule;

    /**
     * serviceKey
     */
    private String serviceKey;

    /**
     * AppId
     */
    private String serviceId;

    /**
     * Downstream service Host
     */
    private String serviceHost;

    /**
     * Downstream service port
     */
    private String servicePort;

    /**
     * Downstream service path
     */
    private String servicePath;

    /**
     * https ?
     */
    private boolean isHttps = false;


    public String getServiceKey() {
        return serviceKey;
    }


    public void setServiceKey(String serviceKey) {
        this.serviceKey = serviceKey;
    }


    public String getServiceId() {
        return serviceId;
    }


    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }


    public String getServicePath() {
        return servicePath;
    }


    public void setServicePath(String servicePath) {
        this.servicePath = servicePath;
    }


    public String getServiceHost() {
        return serviceHost;
    }


    public void setServiceHost(String serviceHost) {
        this.serviceHost = serviceHost;
    }


    public String getServicePort() {
        return servicePort;
    }


    public void setServicePort(String servicePort) {
        this.servicePort = servicePort;
    }


    public boolean isHttps() {
        return isHttps;
    }


    public void setHttps(boolean isHttps) {
        this.isHttps = isHttps;
    }


    public String getServiceModule() {
        return serviceModule;
    }


    public void setServiceModule(String serviceModule) {
        this.serviceModule = serviceModule;
    }
}
