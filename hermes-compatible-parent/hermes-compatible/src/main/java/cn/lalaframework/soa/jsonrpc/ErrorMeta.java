package cn.lalaframework.soa.jsonrpc;

import cn.lalaframework.soa.annotation.SOAContext;

import java.util.Locale;

/**
 * 错误对象
 *
 * <AUTHOR>
 */
public class ErrorMeta implements Error {

    public ErrorMeta(String isvModule, Integer ret, String msg) {
        super();
        this.isvModule = isvModule;
        this.ret = ret;
        this.msg = msg;
    }

    public ErrorMeta(Integer ret, String msg) {
        super();
        this.ret = ret;
        this.msg = msg;
    }

    private String isvModule;
    private Integer ret;
    private String msg;

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public Integer getRet() {
        return ret;
    }

    public String getIsvModule() {
        return isvModule;
    }

    public void setIsvModule(String isvModule) {
        this.isvModule = isvModule;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public void setRet(Integer ret) {
        this.ret = ret;
    }

    /**
     * @param params i18n属性文件参数。顺序对应文件中的占位符{0},{1}
     * @return 返回exception
     */
    public JsonRpcServiceException getException(Object... params) {
        Locale locale = SOAContext.getLocal();
        if (locale == null) {
            locale = Locale.CHINA;
        }
        return this.getException(locale, params);
    }

    /**
     * 返回exception，并且附带数据
     *
     * @param data   数据
     * @param params i18n属性文件参数。顺序对应文件中的占位符{0},{1}
     * @return 返回exception
     */
    public JsonRpcServiceException getExceptionData(Object data, Object... params) {
        Locale locale = SOAContext.getLocal();
        if (locale == null) {
            locale = Locale.CHINA;
        }
        JsonRpcServiceException ex = this.getException(locale, params);
        ex.setData(data);
        return ex;
    }

    public JsonRpcServiceException getException(Locale locale, Object... params) {
        Error error = ErrorFactory.getError(this, locale, params);
        return new JsonRpcServiceException(error);
    }

}
