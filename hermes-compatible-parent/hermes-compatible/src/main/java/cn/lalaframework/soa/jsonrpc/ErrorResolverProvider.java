package cn.lalaframework.soa.jsonrpc;

import cn.huolala.arch.hermes.cluster.governance.GovernanceException;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.ClassUtils;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import com.googlecode.jsonrpc4j.ErrorResolver;

import java.util.HashMap;
import java.util.Map;

import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.EXCEPTION_TYPE_NAME;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.MESSAGE;
import static cn.huolala.arch.hermes.compatible.config.support.JsonRpcConstants.RET;
import static cn.lalaframework.soa.jsonrpc.ErrorResolver.JsonError.BUSINESS_ERROR;
import static cn.lalaframework.soa.jsonrpc.ErrorResolver.JsonError.FALLBACK_ERROR;
import static com.googlecode.jsonrpc4j.ErrorResolver.JsonError.ERROR_NOT_HANDLED;

public final class ErrorResolverProvider {

    private ErrorResolverProvider() {
    }

    private static volatile ErrorResolver errorResolver;

    private static final Logger logger = LoggerFactory.getLogger(ErrorResolverProvider.class);


    /**
     * singleton ErrorResolver instance
     */
    public static ErrorResolver getInstance(String resolver) {
        if (errorResolver == null) {
            synchronized (ErrorResolverProvider.class) {
                if (errorResolver == null) {
                    if (StringUtils.isNotEmpty(resolver)) {
                        errorResolver = adapterErrorResolver(resolver);
                    } else {
                        errorResolver = compatibleErrorResolver();
                    }
                }
            }
        }
        return errorResolver;
    }


    /**
     * compatible 1.3.x jsonrpc ErrorResolver
     */
    private static ErrorResolver compatibleErrorResolver() {
        return  (t, method, arguments) -> {
            String className = t.getClass().getName();
            boolean isSystemThrowable = className.startsWith("java.") || className.startsWith("javax.");
            String msg = t.getMessage();
            if (StringUtils.isEmpty(msg)) {
                try {
                    msg = (String) ReflectUtils.getFieldValue(t, "msg");
                } catch (Exception e) {
                    logger.warn("No msg field found in current object [" + t.getClass() + "]");
                }
            }
            Integer ret = null;
            try {
                ret = (Integer) ReflectUtils.getFieldValue(t, "ret");
            } catch (Exception e) {
                logger.debug("No ret field found in current object [" + t.getClass() + "]");
            }
            Map<String, Object> errorData = new HashMap<>();
            errorData.put(RET, ret);
            errorData.put(MESSAGE, msg);
            errorData.put(EXCEPTION_TYPE_NAME, className);

            if(t instanceof GovernanceException){
                return new ErrorResolver.JsonError(FALLBACK_ERROR.code, msg, errorData);
            }
            else if (isSystemThrowable) {
                return new ErrorResolver.JsonError(ERROR_NOT_HANDLED.code, msg, errorData);
            } else {
                return new ErrorResolver.JsonError(BUSINESS_ERROR.code, msg, errorData);
            }
        };
    }


    /**
     * adapter custom jsonrpc ErrorResolver
     */
    private static ErrorResolver adapterErrorResolver(String resolver) {
        try {
            Class<?> clazz = ClassUtils.classForName(resolver);
            if (!cn.lalaframework.soa.jsonrpc.ErrorResolver.class.isAssignableFrom(clazz) && !ErrorResolver.class.isAssignableFrom(clazz)) {
                throw new IllegalStateException("ErrorResolver [" + resolver + "] "
                        + " implementation at least cn.lalaframework.soa.jsonrpc.ErrorResolver or com.googlecode.jsonrpc4j.ErrorResolver");
            }
            logger.info("ErrorResolver load for name  [ " + resolver + "]");
            if (cn.lalaframework.soa.jsonrpc.ErrorResolver.class.isAssignableFrom(clazz)) {
                cn.lalaframework.soa.jsonrpc.ErrorResolver adapterErrorResolver =
                        (cn.lalaframework.soa.jsonrpc.ErrorResolver) clazz.newInstance();
                return (t, method, arguments) -> {
                    cn.lalaframework.soa.jsonrpc.ErrorResolver.JsonError jsonError = adapterErrorResolver.resolveError(t, method, arguments);
                    return new ErrorResolver.JsonError(jsonError.code, jsonError.message, jsonError.data);
                };
            }
            return (ErrorResolver) clazz.newInstance();
        } catch (Exception e) {
            throw new IllegalStateException("ErrorResolver load class[" + errorResolver + "] error case[" + e.getMessage() + "]");
        }
    }

}
