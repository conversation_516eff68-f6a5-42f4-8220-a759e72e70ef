package cn.lalaframework.soa.jsonrpc;

import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.ClassUtils;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.googlecode.jsonrpc4j.JsonRpcInterceptor;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public final class JsonRpcInterceptorProvider {

    private JsonRpcInterceptorProvider() {
    }

    private static volatile List<JsonRpcInterceptor> interceptors;

    private static final Logger logger = LoggerFactory.getLogger(JsonRpcInterceptorProvider.class);

    public static List<JsonRpcInterceptor> getInstance(Set<String> interceptorNames) {
        if (interceptors == null) {
            synchronized (JsonRpcInterceptorProvider.class) {
                if (interceptors == null) {
                    interceptors = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(interceptorNames)) {
                        for (String interceptorName : interceptorNames) {
                            JsonRpcInterceptor jsonRpcInterceptor = adapterInterceptor(interceptorName);
                            interceptors.add(jsonRpcInterceptor);
                        }
                    }

                }
            }
        }
        return interceptors;
    }


    private static JsonRpcInterceptor adapterInterceptor(String interceptorName) {
        try {
            Class<?> clazz = ClassUtils.classForName(interceptorName);
            if (!cn.lalaframework.soa.jsonrpc.JsonRpcInterceptor.class.isAssignableFrom(clazz) && !JsonRpcInterceptor.class.isAssignableFrom(clazz)) {
                throw new IllegalStateException("JsonRpcInterceptor [" + interceptorName + "] implementation at least cn.lalaframework.soa.jsonrpc.JsonRpcInterceptor or com.googlecode.jsonrpc4j.JsonRpcInterceptor");
            }
            logger.info("JsonRpcInterceptor load for name:[ " + interceptorName + "]");
            if (cn.lalaframework.soa.jsonrpc.JsonRpcInterceptor.class.isAssignableFrom(clazz)) {
                cn.lalaframework.soa.jsonrpc.JsonRpcInterceptor interceptorInstance = (cn.lalaframework.soa.jsonrpc.JsonRpcInterceptor) clazz.newInstance();
                return new JsonRpcInterceptor() {
                    @Override
                    public void preHandleJson(JsonNode json) {
                        interceptorInstance.preHandleJson(json);
                    }

                    @Override
                    public void preHandle(Object target, Method method, List<JsonNode> params) {
                        interceptorInstance.preHandle(target, method, params);
                    }

                    @Override
                    public void postHandle(Object target, Method method, List<JsonNode> params, JsonNode result) {
                        interceptorInstance.postHandle(target, method, params, result);
                    }

                    @Override
                    public void postHandleJson(JsonNode json) {
                        interceptorInstance.postHandleJson(json);
                    }
                };
            }
            return (JsonRpcInterceptor) clazz.newInstance();
        } catch (Exception e) {
            throw new IllegalStateException("JsonRpcInterceptor load class[" + interceptorName + "] error case[" + e.getMessage() + "]");
        }
    }

}
