package cn.lalaframework.soa.jsonrpc;

/**
 * Unchecked Exception thrown by a JSON-RPC client when
 * an error occurs.
 */
public class JsonRpcServiceException extends RuntimeException {

	private static final long serialVersionUID = 1L;
	
	private int ret;
	private String msg;
	private Object data;
	
	public JsonRpcServiceException(int ret, String msg) {
		super(msg);
		this.ret = ret;
		this.msg = msg;
	}
		
	public int getRet() {
		return ret;
	}
	
	public String getMsg() {
		return msg;
	}

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}

	public void setRet(int ret) {
		this.ret = ret;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}
	
	public JsonRpcServiceException(String msg) {
        super(msg);
    }

    public JsonRpcServiceException(Exception e) {
        super(e);
    }

    public JsonRpcServiceException(Error error) {
        this(error.getMsg());
        this.ret = error.getRet();
    }
    
    public JsonRpcServiceException(int ret, String msg, Object data) {
        super(msg);
        this.ret = ret;
        this.msg = msg;
        this.data = data;
    }

    public JsonRpcServiceException(Error error, Object data) {
        this(error);
        this.data = data;
    }
	
}
