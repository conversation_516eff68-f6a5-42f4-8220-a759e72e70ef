package cn.lalaframework.soa.jsonrpc.helpers;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * ObjectMapperProvider
 *
 * @see cn.huolala.arch.hermes.config.spring.util.ObjectMapperProvider
 */
public final class ObjectMapperProvider extends cn.huolala.arch.hermes.config.spring.util.ObjectMapperProvider {

    public static final String SOA_OBJECT_MAPPER = cn.huolala.arch.hermes.config.spring.util.ObjectMapperProvider.SOA_OBJECT_MAPPER;
    public static final String OBJECT_MAPPER = cn.huolala.arch.hermes.config.spring.util.ObjectMapperProvider.OBJECT_MAPPER;

    private ObjectMapperProvider() {
    }

    public static ObjectMapper getInstance() {
        return cn.huolala.arch.hermes.config.spring.util.ObjectMapperProvider.getInstance();
    }

}
