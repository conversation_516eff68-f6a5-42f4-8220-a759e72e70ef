
package cn.lalaframework.soa.jsonrpc.spring.reference;

import cn.huolala.arch.hermes.spec.classification.ApiStability;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @deprecated as 2.1.0, use {@link cn.huolala.arch.hermes.api.annotation.HermesReference } instead
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.ANNOTATION_TYPE})
@ApiStability.Deprecated
public @interface ReferenceMethod {

    /**
     * the method's name.
     */
    String value();

    /**
     * oneway
     * true --> ignore return
     */
    boolean oneWay() default false;

    /**
     *  tcp connectionTimeoutMillis
     */
    int connectionTimeoutMillis() default 0;

    /**
     * tcp stream readTimeoutMillis
     */
    int readTimeoutMillis() default 0;
}
