package cn.lalaframework.soa.jsonrpc.spring.reference;

import cn.huolala.arch.hermes.spec.classification.ApiStability;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @deprecated as 2.1.0, use {@link cn.huolala.arch.hermes.api.annotation.HermesReference } instead
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.METHOD, ElementType.ANNOTATION_TYPE})
@ApiStability.Deprecated
public @interface SOAReference {

    /**
     * appId
     */
    String appId() default "";

    /**
     * https
     */
    boolean https() default false;

    /**
     * oneWay
     * If the method has the same properties is ignored
     */
    boolean oneWay() default false;

    /**
     *  tcp connectionTimeoutMillis
     *  If the method has the same attributes  is ignored
     *
     */
    int connectionTimeoutMillis() default 0;

    /**
     * tcp stream readTimeoutMillis
     * If the method has the same attributes  is ignored
     */
    int readTimeoutMillis() default 0;

    /**
     * methods reference
     */
    ReferenceMethod[] methods() default {};
}
