package cn.huolala.arch.hermes.config.builder;

import cn.huolala.arch.hermes.api.config.ApplicationConfig;
import cn.huolala.arch.hermes.config.builder.support.AbstractBuilder;

/**
 * ApplicationConfig Builder
 */
public final class ApplicationBuilder extends AbstractBuilder<ApplicationConfig, ApplicationBuilder> {
    private String name;
    private String version;
    private String environment;

    private ApplicationBuilder() {
    }

    public static ApplicationBuilder newBuilder() {
        return new ApplicationBuilder();
    }

    public ApplicationBuilder name(String name) {
        this.name = name;
        return getThis();
    }

    public ApplicationBuilder version(String version) {
        this.version = version;
        return getThis();
    }

    public ApplicationBuilder environment(String environment) {
        this.environment = environment;
        return getThis();
    }

    @Override
    public ApplicationConfig build() {
        ApplicationConfig config = new ApplicationConfig();
        super.build(config);

        config.setName(name);
        config.setVersion(version);
        config.setEnvironment(environment);
        return config;
    }

    @Override
    protected ApplicationBuilder getThis() {
        return this;
    }
}
