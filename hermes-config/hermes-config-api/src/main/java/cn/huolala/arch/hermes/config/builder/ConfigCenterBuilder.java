package cn.huolala.arch.hermes.config.builder;

import cn.huolala.arch.hermes.api.config.ConfigCenterConfig;
import cn.huolala.arch.hermes.config.builder.support.AbstractBuilder;

/**
 * ConfigCenterConfig Builder
 */
public final class ConfigCenterBuilder extends AbstractBuilder<ConfigCenterConfig, ConfigCenterBuilder> {

    private String protocol;
    private String address;
    private Integer port;
    private String cluster;

    private ConfigCenterBuilder() {
    }

    public static ConfigCenterBuilder newBuilder() {
        return new ConfigCenterBuilder();
    }

    public ConfigCenterBuilder protocol(String protocol) {
        this.protocol = protocol;
        return getThis();
    }

    public ConfigCenterBuilder address(String address) {
        this.address = address;
        return getThis();
    }

    public ConfigCenterBuilder port(Integer port) {
        this.port = port;
        return getThis();
    }

    public ConfigCenterBuilder cluster(String cluster) {
        this.cluster = cluster;
        return getThis();
    }

    @Override
    public ConfigCenterConfig build() {
        ConfigCenterConfig config = new ConfigCenterConfig();
        super.build(config);

        config.setProtocol(protocol);
        config.setAddress(address);
        config.setPort(port);
        config.setCluster(cluster);
        return config;
    }

    @Override
    protected ConfigCenterBuilder getThis() {
        return this;
    }
}
