package cn.huolala.arch.hermes.config.builder;

import cn.huolala.arch.hermes.api.config.ExecutorConfig;
import cn.huolala.arch.hermes.api.config.ProtocolConfig;
import cn.huolala.arch.hermes.config.builder.support.AbstractBuilder;

/**
 * Protocol Builder
 */
public final class ProtocolBuilder extends AbstractBuilder<ProtocolConfig, ProtocolBuilder> {
    private String name;
    private String host;
    private Integer port;
    private String dispatcher;
    private ExecutorConfig executor;
    private Integer accepts;
    private Integer payload;

    private ProtocolBuilder() {
    }

    public static ProtocolBuilder newBuilder() {
        return new ProtocolBuilder();
    }

    public ProtocolBuilder name(String name) {
        this.name = name;
        return getThis();
    }

    public ProtocolBuilder host(String host) {
        this.host = host;
        return getThis();
    }

    public ProtocolBuilder port(Integer port) {
        this.port = port;
        return getThis();
    }

    public ProtocolBuilder dispatcher(String dispatcher) {
        this.dispatcher = dispatcher;
        return getThis();
    }

    public ProtocolBuilder executor(ExecutorConfig executor) {
        this.executor = executor;
        return getThis();
    }

    public ProtocolBuilder accepts(Integer accepts) {
        this.accepts = accepts;
        return getThis();
    }

    public ProtocolBuilder payload(Integer payload) {
        this.payload = payload;
        return getThis();
    }

    @Override
    public ProtocolConfig build() {
        ProtocolConfig config = new ProtocolConfig();
        super.build(config);

        config.setName(name);
        config.setHost(host);
        config.setPort(port);
        config.setDispatcher(dispatcher);
        config.setExecutor(executor);
        config.setAccepts(accepts);
        config.setPayload(payload);
        return config;
    }

    @Override
    protected ProtocolBuilder getThis() {
        return this;
    }
}
