package cn.huolala.arch.hermes.config.spring;

import cn.huolala.arch.hermes.config.spring.context.EnableComponentScan;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Enable Framework spring autoconfiguration
 */
@Documented
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@EnableComponentScan
public @interface EnableHermes {
    /**
     * Base packages to scan for annotated @SOAService classes.
     * <p>
     * Use {@link #scanBasePackageClasses()} for a type-safe alternative to String-based package names
     *
     * @return the base packages to scan
     * @see EnableComponentScan#basePackages()
     */
    @AliasFor(annotation = EnableComponentScan.class, attribute = "basePackages")
    String[] scanBasePackages() default {};

    /**
     * Type-safe alternative to {@link #scanBasePackages()} for specifying the packages to
     * scan for annotated @SOAService classes. The package of each class specified will be scanned
     *
     * @return classes from the base packages to scan
     * @see EnableComponentScan#basePackageClasses
     */
    @AliasFor(annotation = EnableComponentScan.class, attribute = "basePackageClasses")
    Class<?>[] scanBasePackageClasses() default {};
}
