package cn.huolala.arch.hermes.config.spring;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.context.ConfigManager;
import cn.huolala.arch.hermes.config.ReferenceConfig;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import static org.springframework.beans.factory.BeanFactoryUtils.beansOfTypeIncludingAncestors;

/**
 * Spring FactoryBean for {@link ReferenceConfig}
 *
 * @param <T> type
 */
@ApiAudience.Private
public class ReferenceBean<T> extends ReferenceConfig<T>
        implements ApplicationContextAware, InitializingBean, DisposableBean {

    private static final long serialVersionUID = 7072072753200241877L;

    protected transient ApplicationContext applicationContext;

    @Override
    public void afterPropertiesSet() throws Exception {
        // init configs before @SOAReference bean autowiring
        beansOfTypeIncludingAncestors(applicationContext, ConfigManager.class, true, false);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void addIntoConfigManager() {
        // overrides the default behavior in spring bean
    }
}
