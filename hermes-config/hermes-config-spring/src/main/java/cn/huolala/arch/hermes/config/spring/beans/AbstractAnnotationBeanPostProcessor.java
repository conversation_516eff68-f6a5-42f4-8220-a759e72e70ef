package cn.huolala.arch.hermes.config.spring.beans;

import cn.huolala.arch.hermes.config.spring.ReferenceBean;
import cn.huolala.arch.hermes.config.spring.support.AnnotationUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.InjectionMetadata;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.util.ObjectUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static cn.huolala.arch.hermes.config.spring.support.AnnotationUtils.getAttribute;
import static cn.huolala.arch.hermes.config.spring.support.AnnotationUtils.getAttributes;
import static org.springframework.util.StringUtils.hasText;

public abstract class AbstractAnnotationBeanPostProcessor extends com.alibaba.spring.beans.factory.annotation.AbstractAnnotationBeanPostProcessor implements
        ApplicationContextAware {

    private static final int CACHE_SIZE = Integer.getInteger("referenceAnnotationPostProcessor.cache.size", 32);

    private final Map<String, ReferenceBean<?>> referenceBeanCache = new ConcurrentHashMap<>(CACHE_SIZE);

    private final Map<InjectionMetadata.InjectedElement, ReferenceBean<?>> injectedFieldReferenceBeanCache = new ConcurrentHashMap<>(CACHE_SIZE);

    private final Map<InjectionMetadata.InjectedElement, ReferenceBean<?>> injectedMethodReferenceBeanCache = new ConcurrentHashMap<>(CACHE_SIZE);

    protected ApplicationContext applicationContext;

    public AbstractAnnotationBeanPostProcessor(boolean ignoreDefaultValue, Class<? extends Annotation>... annotationTypes) {
        super(annotationTypes);
        setIgnoreDefaultValue(ignoreDefaultValue);
        setClassValuesAsString(false);
        setNestedAnnotationsAsMap(false);
    }

    @Override
    protected Object doGetInjectedBean(AnnotationAttributes attributes, Object bean, String beanName, Class<?> injectedType,
                                       InjectionMetadata.InjectedElement injectedElement) throws Exception {
        String referenceBeanName = getReferenceBeanName(attributes, injectedType);
        ReferenceBean<?> referenceBean = referenceBeanCache.get(referenceBeanName);
        if (referenceBean == null) {
            referenceBean = buildReferenceBean(referenceBeanName, attributes, injectedType);
            referenceBeanCache.put(referenceBeanName, referenceBean);
        } else if (!injectedType.isAssignableFrom(referenceBean.getInterfaceClass())) {
            throw new IllegalArgumentException("reference bean name " + referenceBeanName + " has been duplicated, but interfaceClass "
                    + referenceBean.getInterfaceClass().getName() + " cannot be assigned to " + injectedType.getName());
        }
        ConfigurableListableBeanFactory beanFactory = getBeanFactory();
        if (!beanFactory.containsBean(referenceBeanName)) {
            beanFactory.registerSingleton(referenceBeanName, referenceBean);
        }
        cacheInjectedReferenceBean(referenceBean, injectedElement);

        return beanFactory.applyBeanPostProcessorsAfterInitialization(referenceBean.get(), referenceBeanName);
    }

    protected abstract ReferenceBean<?> buildReferenceBean(String referenceBeanName, AnnotationAttributes attributes,
                                                           Class<?> referencedType) throws Exception;


    @Override
    protected String buildInjectedObjectCacheKey(AnnotationAttributes attributes, Object bean, String beanName,
                                                 Class<?> injectedType, InjectionMetadata.InjectedElement injectedElement) {
        String serviceBeanName = ServiceBeanNameBuilder.newBuilder(attributes, injectedType, getEnvironment()).build();

        return serviceBeanName + "#source=" + injectedElement.getMember()
                + "#attributes=" + getAttributes(attributes, getEnvironment());
    }

    /**
     * Gets all beans of {@link ReferenceBean}
     */
    public Collection<ReferenceBean<?>> getReferenceBeans() {
        return referenceBeanCache.values();
    }

    /**
     * Get {@link ReferenceBean} {@link Map} in injected field.
     */
    public Map<InjectionMetadata.InjectedElement, ReferenceBean<?>> getInjectedFieldReferenceBeanMap() {
        return Collections.unmodifiableMap(injectedFieldReferenceBeanCache);
    }

    /**
     * Get {@link ReferenceBean} {@link Map} in injected method.
     */
    public Map<InjectionMetadata.InjectedElement, ReferenceBean<?>> getInjectedMethodReferenceBeanMap() {
        return Collections.unmodifiableMap(injectedMethodReferenceBeanCache);
    }


    protected void cacheInjectedReferenceBean(ReferenceBean<?> referenceBean, InjectionMetadata.InjectedElement injectedElement) {
        if (injectedElement.getMember() instanceof Field) {
            injectedFieldReferenceBeanCache.put(injectedElement, referenceBean);
        } else if (injectedElement.getMember() instanceof Method) {
            injectedMethodReferenceBeanCache.put(injectedElement, referenceBean);
        }
    }


    @Override
    public void destroy() throws Exception {
        super.destroy();
        this.referenceBeanCache.clear();
        this.injectedFieldReferenceBeanCache.clear();
        this.injectedMethodReferenceBeanCache.clear();
    }

    /**
     * Get the bean name of {@link ReferenceBean} if id attribute is present,
     * or {@link #generateReferenceBeanName(AnnotationAttributes, Class) generate}
     */
    protected String getReferenceBeanName(AnnotationAttributes attributes, Class<?> interfaceClass) {
        // id attribute appears since 2.7.3
        String beanName = getAttribute(attributes, "id");
        if (!hasText(beanName)) {
            beanName = generateReferenceBeanName(attributes, interfaceClass);
        }
        return beanName;
    }

    /**
     * Build the bean name of {@link ReferenceBean}
     */
    protected String generateReferenceBeanName(AnnotationAttributes attributes, Class<?> interfaceClass) {
        StringBuilder beanNameBuilder = new StringBuilder("ReferenceBean");

        if (!attributes.isEmpty()) {
            beanNameBuilder.append('(');
            for (Map.Entry<String, Object> entry : attributes.entrySet()) {
                String value;
                if ("parameters".equals(entry.getKey())) {
                    ArrayList<String> pairs = getParameterPairs(entry);
                    value = convertAttribute(pairs.stream().sorted().toArray());
                } else {
                    value = convertAttribute(entry.getValue());
                }
                beanNameBuilder.append(entry.getKey()).append('=').append(value).append(',');
            }
            // replace the latest "," to be ")"
            beanNameBuilder.setCharAt(beanNameBuilder.lastIndexOf(","), ')');
        }

        beanNameBuilder.append(" ").append(interfaceClass.getName());

        return beanNameBuilder.toString();
    }


    private ArrayList<String> getParameterPairs(Map.Entry<String, Object> entry) {
        String[] entryValues = (String[]) entry.getValue();
        ArrayList<String> pairs = new ArrayList<>();
        // parameters spec is {key1,value1,key2,value2}
        for (int i = 0; i < entryValues.length / 2 * 2; i = i + 2) {
            pairs.add(entryValues[i] + "=" + entryValues[i + 1]);
        }
        return pairs;
    }

    private String convertAttribute(Object obj) {
        if (obj == null) {
            return null;
        }
        if (obj instanceof Annotation) {
            AnnotationAttributes attributes = AnnotationUtils.getAnnotationAttributes((Annotation) obj, true);
            for (Map.Entry<String, Object> entry : attributes.entrySet()) {
                entry.setValue(convertAttribute(entry.getValue()));
            }
            return String.valueOf(attributes);
        } else if (obj.getClass().isArray()) {
            Object[] array = ObjectUtils.toObjectArray(obj);
            String[] newArray = new String[array.length];
            for (int i = 0; i < array.length; i++) {
                newArray[i] = convertAttribute(array[i]);
            }
            return Arrays.toString(Arrays.stream(newArray).sorted().toArray());
        } else {
            return String.valueOf(obj);
        }
    }



    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
