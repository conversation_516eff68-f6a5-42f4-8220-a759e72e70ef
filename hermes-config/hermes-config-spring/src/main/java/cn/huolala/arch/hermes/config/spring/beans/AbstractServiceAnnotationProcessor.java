package cn.huolala.arch.hermes.config.spring.beans;

import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.config.spring.support.AbstractBeanDefinitionScanner;
import cn.huolala.arch.hermes.config.spring.support.AnnotationUtils;
import cn.huolala.arch.hermes.config.spring.support.BeanDefinitionScanner;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanClassLoaderAware;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.BeanDefinitionHolder;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.config.SingletonBeanRegistry;
import org.springframework.beans.factory.support.*;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.context.annotation.AnnotationBeanNameGenerator;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Nonnull;
import java.lang.annotation.Annotation;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.huolala.arch.hermes.common.constants.Constants.COMMA_SEPARATOR;
import static org.springframework.context.annotation.AnnotationConfigUtils.CONFIGURATION_BEAN_NAME_GENERATOR;
import static org.springframework.core.annotation.AnnotationUtils.getAnnotationAttributes;

public abstract class AbstractServiceAnnotationProcessor
        implements BeanDefinitionRegistryPostProcessor, EnvironmentAware, ResourceLoaderAware, BeanClassLoaderAware, ApplicationContextAware {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    protected final Set<String> packagesToScan;

    protected final Class<? extends Annotation> annotationType;

    protected final Class<?> serviceBeanClass;

    protected Environment environment;

    protected ResourceLoader resourceLoader;

    private ClassLoader classLoader;

    public AbstractServiceAnnotationProcessor(Set<String> packagesToScan, Class<? extends Annotation> annotationType, Class<?> serviceBeanClass) {
        this.packagesToScan = packagesToScan;
        this.annotationType = annotationType;
        this.serviceBeanClass = serviceBeanClass;
    }


    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        Set<String> resolvedPackagesToScan = resolvePackagesToScan(packagesToScan);

        if (!CollectionUtils.isEmpty(resolvedPackagesToScan)) {
            registerServiceBeans(resolvedPackagesToScan, registry);
        } else {
            if (logger.isWarnEnabled()) {
                logger.warn("The packagesToScan is empty , ServiceBean registry will be ignored");
            }
        }
    }


    private Set<String> resolvePackagesToScan(Set<String> packagesToScan) {
        Set<String> resolvedPackagesToScan = new LinkedHashSet<>(packagesToScan.size());
        for (String packageToScan : packagesToScan) {
            if (StringUtils.hasText(packageToScan)) {
                String resolvedPackageToScan = environment.resolvePlaceholders(packageToScan.trim());
                if (StringUtils.hasText(resolvedPackageToScan)) {
                    Stream.of(resolvedPackageToScan.split(COMMA_SEPARATOR))
                            .map(String::trim)
                            .filter(StringUtils::hasText)
                            .forEach(resolvedPackagesToScan::add);
                }
            }
        }
        return resolvedPackagesToScan;
    }


    protected void registerServiceBeans(Set<String> packagesToScan, BeanDefinitionRegistry registry) {
        BeanDefinitionScanner scanner = new BeanDefinitionScanner(registry, environment, resourceLoader);

        BeanNameGenerator beanNameGenerator = resolveBeanNameGenerator(registry);
        scanner.setBeanNameGenerator(beanNameGenerator);
        scanner.addIncludeFilter(new AnnotationTypeFilter(this.annotationType));

        for (String packageToScan : packagesToScan) {
            // Registers Bean first
            scanner.scan(packageToScan);
            // Finds all BeanDefinitionHolders of annotationType
            Set<BeanDefinitionHolder> beanDefinitionHolders = scanner.findCandidateComponents(packageToScan)
                    .stream()
                    .map(definition -> {
                        String beanName = beanNameGenerator.generateBeanName(definition, registry);
                        return new BeanDefinitionHolder(definition, beanName);
                    }).collect(Collectors.toSet());

            if (!CollectionUtils.isEmpty(beanDefinitionHolders)) {
                for (BeanDefinitionHolder beanDefinitionHolder : beanDefinitionHolders) {
                    registerServiceBean(beanDefinitionHolder, registry, scanner);
                }
                logger.info("The annotated" + annotationType + "Components { "
                        + beanDefinitionHolders + " } were scanned under package[" + packageToScan + "], size:[" + beanDefinitionHolders.size() + "]");
            } else {
                logger.warn("The No Spring Bean annotating " + annotationType + " was found under package[" + packageToScan + "]");
            }
        }
    }


    protected void registerServiceBean(BeanDefinitionHolder beanDefinitionHolder,
                                       BeanDefinitionRegistry registry,
                                       AbstractBeanDefinitionScanner scanner) {
        Class<?> beanClass = resolveClass(beanDefinitionHolder.getBeanDefinition());
        Annotation service = AnnotatedElementUtils.findMergedAnnotation(beanClass, this.annotationType);
        Assert.notNull(service, "The class[" + beanClass.getName() + "]@" + ClassUtils.getShortName(this.annotationType) + "annotation must be present");

        AnnotationAttributes serviceAttributes = annotationAttributes(service);
        Class<?> interfaceClass = resolveServiceInterfaceClass(serviceAttributes, beanClass);

        AbstractBeanDefinition serviceBeanDefinition =
                buildServiceBeanDefinition(service, serviceAttributes, interfaceClass, beanDefinitionHolder.getBeanName());

        // Supports @Lazy annotation
        Lazy lazyAnnotation = beanClass.getAnnotation(Lazy.class);
        if (lazyAnnotation != null) {
            serviceBeanDefinition.setLazyInit(lazyAnnotation.value());
        }
        serviceBeanDefinition.setPrimary(true);

        String beanName = generateBeanName(serviceAttributes, interfaceClass);
        if (scanner.checkCandidate(beanName, serviceBeanDefinition)) {
            registry.registerBeanDefinition(beanName, serviceBeanDefinition);
            if (!serviceBeanDefinition.getPropertyValues().contains("id")) {
                serviceBeanDefinition.getPropertyValues().addPropertyValue("id", beanName);
            }
            logger.info("The BeanDefinition[" + serviceBeanDefinition
                    + "] of ServiceBean has been registered with name : " + beanName);
        } else {
            logger.warn("The Duplicated BeanDefinition[" + serviceBeanDefinition + "] of ServiceBean[ bean name : "
                    + beanName + "] was be found , Did scan to same package in many times?");
        }
    }


    public AnnotationAttributes annotationAttributes(Annotation annotation) {
        return getAnnotationAttributes(annotation, false, false);
    }

    protected String generateBeanName(AnnotationAttributes serviceAttributes, Class<?> interfaceClass) {
        return ServiceBeanNameBuilder.newBuilder(serviceAttributes, interfaceClass, environment).build();
    }


    /**
     * Build the {@link AbstractBeanDefinition Bean Definition}
     */
    protected AbstractBeanDefinition buildServiceBeanDefinition(Annotation service,
                                                                AnnotationAttributes serviceAttributes,
                                                                Class<?> interfaceClass,
                                                                String beanName) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.rootBeanDefinition(this.serviceBeanClass);

        // References "ref" property to annotated-@SOAService Bean
        addPropertyReference(builder, "ref", beanName);
        builder.addPropertyValue("interfaceClass", interfaceClass);

        return builder.getBeanDefinition();
    }


    private void addPropertyReference(BeanDefinitionBuilder builder, String propertyName, String beanName) {
        String resolvedBeanName = environment.resolvePlaceholders(beanName);
        builder.addPropertyReference(propertyName, resolvedBeanName);
    }


    protected Class<?> resolveClass(BeanDefinition beanDefinition) {
        String beanClassName = beanDefinition.getBeanClassName();
        Assert.notNull(beanClassName, "BeanClassName:[" + beanClassName + "]" + " must not be null");
        return ClassUtils.resolveClassName(beanClassName, classLoader);
    }


    /**
     * resolveBeanNameGenerator
     *
     * @see SingletonBeanRegistry
     * @see AnnotationBeanNameGenerator
     */
    public BeanNameGenerator resolveBeanNameGenerator(BeanDefinitionRegistry registry) {
        BeanNameGenerator beanNameGenerator = null;

        if (registry instanceof SingletonBeanRegistry) {
            SingletonBeanRegistry singletonBeanRegistry = (SingletonBeanRegistry) registry;
            beanNameGenerator = (BeanNameGenerator) singletonBeanRegistry.getSingleton(CONFIGURATION_BEAN_NAME_GENERATOR);
        }

        if (beanNameGenerator == null) {
            beanNameGenerator = new AnnotationBeanNameGenerator();
        }

        return beanNameGenerator;
    }

    protected Class<?> resolveServiceInterfaceClass(AnnotationAttributes serviceAttributes, Class<?> beanClass) {
        return AnnotationUtils.resolveServiceInterfaceClass(serviceAttributes, beanClass);
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        // NOTHING TO DO
    }


    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    @Override
    public void setResourceLoader(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    @Override
    public void setBeanClassLoader(ClassLoader classLoader) {
        this.classLoader = classLoader;
    }

    @Override
    public void setApplicationContext(@Nonnull ApplicationContext applicationContext) throws BeansException {
        cn.huolala.arch.hermes.common.context.ApplicationContext.getAttributes().put(ApplicationContext.class.getName(), applicationContext);
    }
}
