package cn.huolala.arch.hermes.config.spring.beans;

import static cn.huolala.arch.hermes.common.constants.Constants.JSONRPC_PROTOCOL;
import static cn.huolala.arch.hermes.common.constants.Constants.GRPC_PROTOCOL;
import static cn.huolala.arch.hermes.common.util.StringUtils.isEmpty;
import static cn.huolala.arch.hermes.common.util.StringUtils.isNoneEmpty;

import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationAttributes;

import cn.huolala.arch.hermes.api.annotation.HermesReference;
import cn.huolala.arch.hermes.api.grpc.IGrpc;
import static cn.huolala.arch.hermes.common.constants.Constants.PROTOCOL_KEY;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.config.spring.ReferenceBean;
import cn.huolala.arch.hermes.config.spring.support.Compatible;
import cn.lalaframework.soa.annotation.SOAService;

/**
 * {@link org.springframework.beans.factory.config.BeanPostProcessor} implementation
 * that Consumer service {@link HermesReference} annotated fields
 *
 * @see HermesReference
 */
public class ReferenceAnnotationPostProcessor extends AbstractAnnotationBeanPostProcessor implements ApplicationContextAware {

    public static final String BEAN_NAME = "hermesReferenceAnnotationPostProcessor";

    private final Compatible compatible;

    public ReferenceAnnotationPostProcessor() {
        super(false, HermesReference.class);
        compatible = Compatible.getDefaultExtension();
    }

    @Override
    protected ReferenceBean<?> buildReferenceBean(String referenceBeanName, AnnotationAttributes attributes,
            Class<?> referencedType) throws Exception {
        String protocol = attributes.getString(PROTOCOL_KEY);
        String p2pProtocol = attributes.getString("url");

        if (isUnspecified(referencedType, protocol, p2pProtocol)
                || isJsonrpcP2pProtocol(p2pProtocol)
                || isJsonrpcProtocol(protocol, p2pProtocol)
                || !IGrpc.class.isAssignableFrom(referencedType)) {
            attributes.put(PROTOCOL_KEY, JSONRPC_PROTOCOL);
            return compatible.buildReferenceBean(referenceBeanName, applicationContext, attributes, referencedType);
        }
        attributes.put(PROTOCOL_KEY, GRPC_PROTOCOL);
        return ReferenceBeanBuilder.newBuilder(attributes, applicationContext).interfaceClass(referencedType)
                .beanName(referenceBeanName).build();
    }

    private boolean isJsonrpcProtocol(String protocol, String p2pProtocol) {
        return isEmpty(p2pProtocol) && isNoneEmpty(protocol) && StringUtils.equals(protocol, JSONRPC_PROTOCOL);
    }

    private boolean isJsonrpcP2pProtocol(String p2pProtocol) {
        return isNoneEmpty(p2pProtocol) && p2pProtocol.startsWith(JSONRPC_PROTOCOL);
    }

    private boolean isUnspecified(Class<?> referencedType, String protocol, String p2pProtocol) {
        return isEmpty(protocol) && isEmpty(p2pProtocol) && referencedType.isAnnotationPresent(SOAService.class);
    }
}
