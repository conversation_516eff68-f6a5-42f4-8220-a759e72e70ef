package cn.huolala.arch.hermes.config.spring.beans;

import static cn.huolala.arch.hermes.common.constants.Constants.GRPC_SERVICE;
import static cn.huolala.arch.hermes.common.constants.Constants.SERVICE_FILTRATE_TAG_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.SERVICE_KEY;
import static cn.huolala.arch.hermes.config.spring.support.AnnotationUtils.getAttribute;
import static org.springframework.util.ClassUtils.getAllInterfacesForClass;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.util.Assert;

import cn.huolala.arch.hermes.api.annotation.Method;
import cn.huolala.arch.hermes.api.config.MethodConfig;
import cn.huolala.arch.hermes.api.fallback.DefaultFallbackFactory;
import cn.huolala.arch.hermes.api.fallback.FallbackFactory;
import cn.huolala.arch.hermes.api.http.HttpMethod;
import cn.huolala.arch.hermes.cluster.fallback.Fallback;
import cn.huolala.arch.hermes.cluster.fallback.support.GlobalFallback;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.config.spring.ReferenceBean;
import cn.huolala.arch.hermes.config.spring.support.AnnotationUtils;

/**
 * {@link ReferenceBean} Builder
 */
public final class ReferenceBeanBuilder {
    private final AnnotationAttributes attributes;

    private final ApplicationContext applicationContext;

    private final ClassLoader classLoader;

    private Class<?> interfaceClass;

    private String beanName;

    private ReferenceBeanBuilder(AnnotationAttributes attributes, ApplicationContext applicationContext) {
        Assert.notNull(attributes, "The Annotation attributes must not be null!");
        Assert.notNull(applicationContext, "The ApplicationContext must not be null!");

        this.attributes = attributes;
        this.applicationContext = applicationContext;
        this.classLoader = applicationContext.getClassLoader() != null ? applicationContext.getClassLoader()
                : Thread.currentThread().getContextClassLoader();
    }

    public static ReferenceBeanBuilder newBuilder(AnnotationAttributes attributes, ApplicationContext applicationContext) {
        return new ReferenceBeanBuilder(attributes, applicationContext);
    }

    private void configureBean(ReferenceBean<?> referenceBean) throws Exception {
        referenceBean.setId(beanName);
        configureInterface(referenceBean);
        configureApplication(referenceBean);

        configureIfPresent(referenceBean::setPath, "path");
        configureIfPresent(referenceBean::setVersion, "version");
        configureIfPresent(referenceBean::setUrl, "url");
        configureIfPresent(referenceBean::setProtocolName, "protocol");
        configureIfPresent(referenceBean::setCluster, "cluster");
        configureIfPresent(referenceBean::setGroup, "group");
        configureIfPresent(referenceBean::setLoadbalance, "loadbalance");
        configureIfPresent(referenceBean::setTimeout, "timeout");
        configureIfPresent(referenceBean::setConnectionTimeout, "connectionTimeout");

        configureFallback(referenceBean);
        configureMethods(referenceBean);
        configureParameters(referenceBean);
    }

    private void postConfigureBean(AnnotationAttributes attributes, ReferenceBean<?> referenceBean) throws Exception {
        referenceBean.setApplicationContext(applicationContext);
        referenceBean.afterPropertiesSet();
        applicationContext.getAutowireCapableBeanFactory()
                .applyBeanPostProcessorsAfterInitialization(referenceBean, beanName);
    }

    private <T> void configureIfPresent(Consumer<T> consumer, String name) {
        T attribute = getAttribute(attributes, name);
        if (attribute == null
                || attribute instanceof Number && ((Number) attribute).intValue() == 0) {
            return;
        }

        if (!(attribute instanceof CharSequence) || !StringUtils.isBlank((CharSequence) attribute)) {
            consumer.accept(attribute);
        }
    }

    private void configureInterface(ReferenceBean<?> referenceBean) {
        Class<?>[] allInterfacesClass = getAllInterfacesForClass(interfaceClass);
        Class<?> clazz = allInterfacesClass.length > 0 ? allInterfacesClass[0] : interfaceClass;

        referenceBean.setInterface(clazz.getName());
    }

    private void configureApplication(ReferenceBean<?> referenceBean) {
        Stream.of("value", "application")
                .map(name -> getAttribute(attributes, name, ""))
                .filter(StringUtils::isNotBlank)
                .findFirst().ifPresent(referenceBean::setApplication);
    }

    private void configureFallback(ReferenceBean<?> reference) throws InstantiationException, IllegalAccessException {
        Class<?> fallback = attributes.getClass("fallback");

        Optional<FallbackFactory<?>> fallbackFactory = getFallbackFactory(fallback, reference.getInterfaceClass());
        if (fallbackFactory.isPresent()) {
            reference.setFallbackFactory(fallbackFactory.get());
        } else {
            // get app level FallbackFactory or throw IllegalStateException
            getGlobalFallbackFactory().ifPresent(reference::setFallbackFactory);
        }
    }

    private void configureMethods(ReferenceBean<?> reference) {
        List<MethodConfig> methodConfigs = null;

        // reference methods first
        Method[] methods = attributes.getAnnotationArray("methods", Method.class);
        if (methods.length > 0) {
            methodConfigs = Arrays.stream(methods).
                    map(this.methodConfigByAnnotation(reference))
                    .collect(Collectors.toList());
        }

        // interface methods
        List<MethodConfig> interfaceMethods = Arrays.stream(reference.getInterfaceClass().getMethods())
                .filter(method -> method.isAnnotationPresent(Method.class))
                .map(this.methodConfigByMethod(reference))
                .collect(Collectors.toList());

        if (methodConfigs != null) {
            methodConfigs.addAll(interfaceMethods);
        } else {
            methodConfigs = interfaceMethods;
        }

        if (!methodConfigs.isEmpty()) {
            reference.setMethods(methodConfigs);
        }
    }

    private void configureParameters(ReferenceBean<?> referenceBean) {
        Map<String, String> parameters = new HashMap<>();
        parameters.put(SERVICE_FILTRATE_TAG_KEY, SERVICE_KEY + "=" + GRPC_SERVICE);
        referenceBean.setParameters(parameters);
    }

    private Function<Method, MethodConfig> methodConfigByAnnotation(ReferenceBean<?> reference) {
        return method -> {
            try {
                FallbackFactory<?> factory = getFallbackFactory(method.fallback(), reference.getInterfaceClass()).orElse(null);
                MethodConfig methodConfig = new MethodConfig(method, factory);
                methodConfig.setRawPath(method.path());
                return methodConfig;
            } catch (InstantiationException | IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        };
    }

    private Function<java.lang.reflect.Method, MethodConfig> methodConfigByMethod(ReferenceBean<?> reference) {
        return m -> {
            try {
                Method method = m.getAnnotation(Method.class);

                // FallbackFactory
                FallbackFactory<?> factory = getFallbackFactory(method.fallback(), reference.getInterfaceClass()).orElse(null);

                HttpMethod http = method.http();
                Map<String, Object> attributes = AnnotationUtils.getAttributes(http, false);
                String paramDesc = ReflectUtils.getDesc(m.getParameterTypes());

                MethodConfig methodConfig = new MethodConfig(method, m.getName(), paramDesc, factory, attributes);
                methodConfig.setRawPath(method.path());
                return methodConfig;
            } catch (InstantiationException | IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        };
    }

    private Optional<FallbackFactory<?>> getGlobalFallbackFactory() {
        return getFallbackBean(GlobalFallback.class).map(DefaultFallbackFactory::new);
    }

    private Optional<FallbackFactory<?>> getFallbackFactory(Class<?> fallback, Class<?> interfaceClass)
            throws IllegalArgumentException, InstantiationException, IllegalAccessException {
        if (void.class.isAssignableFrom(fallback)) {
            return Optional.empty();
        }

        if (FallbackFactory.class.isAssignableFrom(fallback)) {
            return Optional.of((FallbackFactory<?>) getFallbackBean(fallback).orElse(fallback.newInstance()));
        } else if (Fallback.class.isAssignableFrom(fallback)
                || interfaceClass.isAssignableFrom(fallback)) {
            return Optional.of(new DefaultFallbackFactory<>(getFallbackBean(fallback).orElse(fallback.newInstance())));
        } else {
            throw new IllegalArgumentException("Unknown FallbackFactory config");
        }
    }

    private Optional<Object> getFallbackBean(Class<?> fallback) {
        Map<String, ?> beansOfType = applicationContext.getBeansOfType(fallback);
        return beansOfType.values().stream().map(v -> (Object) v).findFirst();
    }

    public ReferenceBeanBuilder interfaceClass(Class<?> interfaceClass) {
        this.interfaceClass = interfaceClass;
        return this;
    }

    public ReferenceBeanBuilder beanName(String beanName) {
        this.beanName = beanName;
        return this;
    }

    public ReferenceBean<?> build() throws Exception {
        ReferenceBean<?> referenceBean = new ReferenceBean<>();

        configureBean(referenceBean);
        postConfigureBean(attributes, referenceBean);

        return referenceBean;
    }
}
