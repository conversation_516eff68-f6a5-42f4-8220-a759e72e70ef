package cn.huolala.arch.hermes.config.spring.beans;

import cn.huolala.arch.hermes.api.annotation.HermesService;
import cn.huolala.arch.hermes.config.spring.ServiceBean;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;

import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.Set;

import static java.util.Arrays.asList;

/**
 * {@link HermesService} Annotation {@link BeanDefinitionRegistryPostProcessor Bean Definition Registry Post Processor}
 *
 * @see HermesService
 */
public class ServiceAnnotationPostProcessor extends AbstractServiceAnnotationProcessor {

    public ServiceAnnotationPostProcessor(String... packagesToScan) {
        this(asList(packagesToScan));
    }

    public ServiceAnnotationPostProcessor(Collection<String> packagesToScan) {
        this(new LinkedHashSet<>(packagesToScan));
    }

    public ServiceAnnotationPostProcessor(Set<String> packagesToScan) {
        super(packagesToScan, HermesService.class, ServiceBean.class);
    }

}
