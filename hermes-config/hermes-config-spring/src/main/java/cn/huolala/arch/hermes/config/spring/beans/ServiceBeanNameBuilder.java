package cn.huolala.arch.hermes.config.spring.beans;

import cn.huolala.arch.hermes.api.annotation.HermesReference;
import cn.huolala.arch.hermes.api.annotation.HermesService;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;

import static cn.huolala.arch.hermes.config.spring.support.AnnotationUtils.getAttribute;
import static org.springframework.core.annotation.AnnotationUtils.getAnnotationAttributes;

/**
 * {@link HermesService @SOAService} Bean Builder
 *
 * @see HermesService
 * @see HermesReference
 */
public final class ServiceBeanNameBuilder {

    private static final String SEPARATOR = ":";

    private final Environment environment;

    // Required
    private final String interfaceClassName;

    // Optional
    private String version;

    // Optional
    private String group;

    private ServiceBeanNameBuilder(Class<?> interfaceClass, Environment environment) {
        this(interfaceClass.getName(), environment);
    }

    private ServiceBeanNameBuilder(String interfaceClassName, Environment environment) {
        this.interfaceClassName = interfaceClassName;
        this.environment = environment;
    }

    private ServiceBeanNameBuilder(AnnotationAttributes attributes, Class<?> interfaceClass, Environment environment) {
        this(interfaceClass, environment);

        this.group(getAttribute(attributes, "group"));
        this.version(getAttribute(attributes, "version"));
    }

    public static ServiceBeanNameBuilder newBuilder(AnnotationAttributes attributes,
                                                    Class<?> interfaceClass, Environment environment) {
        return new ServiceBeanNameBuilder(attributes, interfaceClass, environment);
    }

    public static ServiceBeanNameBuilder newBuilder(Class<?> interfaceClass, Environment environment) {
        return new ServiceBeanNameBuilder(interfaceClass, environment);
    }

    public static ServiceBeanNameBuilder newBuilder(HermesService service, Class<?> interfaceClass, Environment environment) {
        AnnotationAttributes annotationAttributes = getAnnotationAttributes(service, false, false);
        return newBuilder(annotationAttributes, interfaceClass, environment);
    }

    public static ServiceBeanNameBuilder newBuilder(HermesReference reference, Class<?> interfaceClass, Environment environment) {
        AnnotationAttributes annotationAttributes = getAnnotationAttributes(reference, false, false);
        return newBuilder(annotationAttributes, interfaceClass, environment);
    }

    private static void append(StringBuilder builder, String value) {
        if (StringUtils.hasText(value)) {
            builder.append(SEPARATOR).append(value);
        }
    }

    public ServiceBeanNameBuilder group(String group) {
        this.group = group;
        return this;
    }

    public ServiceBeanNameBuilder version(String version) {
        this.version = version;
        return this;
    }

    public String build() {
        StringBuilder beanNameBuilder = new StringBuilder("ServiceBean");
        // Required
        append(beanNameBuilder, interfaceClassName);
        // Optional
        append(beanNameBuilder, version);
        append(beanNameBuilder, group);
        // Build and remove last ":"
        String rawBeanName = beanNameBuilder.toString();
        // Resolve placeholders
        return environment.resolvePlaceholders(rawBeanName);
    }
}
