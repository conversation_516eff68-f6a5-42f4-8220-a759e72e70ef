package cn.huolala.arch.hermes.config.spring.context;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;

import static org.springframework.util.TypeUtils.isAssignable;

/**
 * {@link ApplicationListener Spring ApplicationListeners} Registrar
 */
public class ApplicationListenerRegistrar implements ApplicationContextAware {
    public static final String BEAN_NAME = "applicationListenerRegistrar";

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        if (!isAssignable(ConfigurableApplicationContext.class, applicationContext.getClass())) {
            throw new IllegalArgumentException("The argument of ApplicationContext must be ConfigurableApplicationContext");
        }
        ConfigurableApplicationContext context = (ConfigurableApplicationContext) applicationContext;
        BootstrapApplicationListener bootstrapApplicationListener = new BootstrapApplicationListener(applicationContext);
        context.getBeanFactory().registerSingleton(BootstrapApplicationListener.BEAN_NAME, bootstrapApplicationListener);
        context.addApplicationListener(bootstrapApplicationListener);
    }
}
