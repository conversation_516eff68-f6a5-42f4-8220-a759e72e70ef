package cn.huolala.arch.hermes.config.spring.context;

import cn.huolala.arch.hermes.config.bootstrap.Bootstrap;
import com.alibaba.spring.context.OnceApplicationContextEventListener;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.SmartLifecycle;
import org.springframework.context.event.ApplicationContextEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.Ordered;

/**
 * The {@link ApplicationListener} for {@link Bootstrap}'s lifecycle when the {@link ContextRefreshedEvent}
 * and {@link ContextClosedEvent} raised
 */
public class BootstrapApplicationListener extends OnceApplicationContextEventListener implements SmartLifecycle, Ordered {
    public static final String BEAN_NAME = "bootstrapApplicationListener";

    private final Bootstrap bootstrap;

    public BootstrapApplicationListener(ApplicationContext context) {
        super(context);
        this.bootstrap = Bootstrap.getInstance();
    }

    @Override
    protected void onApplicationContextEvent(ApplicationContextEvent event) {
        if (event instanceof ContextRefreshedEvent) {
            bootstrap.starter().withRegister(true).withWait(false).withHookRegister(false).start();
        } else if (event instanceof ContextClosedEvent) {
            bootstrap.unregisterServiceInstance();
        }
    }

    @Override
    public int getOrder() {
        return LOWEST_PRECEDENCE - 1;
    }

    @Override
    public void start() {
        //do nothing
    }

    @Override
    public void stop() {
        bootstrap.stopper().withUnregister(false).stop();
    }

    @Override
    public boolean isRunning() {
        return bootstrap.isStarted();
    }

    /**
     * The smaller the value, the earlier the start method is executed and the later the stop method is executed
     */
    @Override
    public int getPhase() {
        return Integer.MIN_VALUE;
    }
}
