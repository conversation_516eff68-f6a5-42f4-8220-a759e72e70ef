package cn.huolala.arch.hermes.config.spring.context;

import cn.huolala.arch.hermes.api.config.ProtocolConfig;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.context.ConfigManager;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.config.spring.beans.ReferenceAnnotationPostProcessor;
import cn.huolala.arch.hermes.config.spring.beans.ServiceAnnotationPostProcessor;
import cn.huolala.arch.hermes.config.spring.definition.ReferenceBeanDefinition;
import cn.huolala.arch.hermes.config.spring.definition.ServiceBeanDefinition;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.type.AnnotationMetadata;

import static cn.huolala.arch.hermes.common.constants.SpiConstants.DEFINITION_DEFAULT;
import static cn.huolala.arch.hermes.common.constants.SpiConstants.DEFINITION_JSON_RPC;
import static com.alibaba.spring.util.BeanRegistrar.registerInfrastructureBean;

/**
 * {@link EnableComponentScan} Bean Registrar
 *
 * @see EnableComponentScan
 * @see ServiceAnnotationPostProcessor
 * @see ReferenceAnnotationPostProcessor
 */
public class ComponentScanRegistrar implements ImportBeanDefinitionRegistrar, BeanFactoryAware {

    private BeanFactory beanFactory;


    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
        ConfigManager configManager = ApplicationContext.getConfigManager();
        String protocolName = configManager.getProtocols()
                .stream()
                .filter(v -> StringUtils.isNotEmpty(v.getName()))
                .findFirst()
                .filter(v -> DEFINITION_JSON_RPC.equals(v.getName()))
                .map(ProtocolConfig::getName)
                .orElse(DEFINITION_DEFAULT);

        ExtensionLoader.getExtensionLoader(ServiceBeanDefinition.class).getExtension(protocolName).registerServiceAnnotationPostProcessor(importingClassMetadata, registry, this.beanFactory);

        //Register Jsonrpc Annotation Bean Processor as an infrastructure Bean
        ReferenceBeanDefinition.getDefaultExtension().registerReferenceAnnotationPostProcessor(registry);

        // Register @HermesReference Annotation Bean Processor as an infrastructure Bean
        registerInfrastructureBean(registry, ReferenceAnnotationPostProcessor.BEAN_NAME, ReferenceAnnotationPostProcessor.class);
        registerInfrastructureBean(registry, ApplicationListenerRegistrar.BEAN_NAME, ApplicationListenerRegistrar.class);
    }


    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }

}