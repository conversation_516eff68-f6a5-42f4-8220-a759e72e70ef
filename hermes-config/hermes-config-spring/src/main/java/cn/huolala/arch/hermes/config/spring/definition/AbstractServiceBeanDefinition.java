package cn.huolala.arch.hermes.config.spring.definition;

import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.config.spring.beans.ServiceAnnotationPostProcessor;
import cn.huolala.arch.hermes.config.spring.context.EnableComponentScan;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionReaderUtils;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;

public abstract class AbstractServiceBeanDefinition implements ServiceBeanDefinition {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void registerServiceAnnotationPostProcessor(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry, BeanFactory beanFactory) {
        Set<String> packagesToScan = getPackagesToScan(importingClassMetadata, registry);
        registerServiceAnnotationPostProcessor(packagesToScan, registry);
    }

    public Set<String> getPackagesToScan(AnnotationMetadata metadata, BeanDefinitionRegistry registry) {
        Map<String, Object> annotationAttributes = metadata.getAnnotationAttributes(EnableComponentScan.class.getName());
        AnnotationAttributes attributes = AnnotationAttributes.fromMap(annotationAttributes);
        Assert.notNull(attributes, "@EnableComponentScan annotation must not be null");

        String[] basePackages = attributes.getStringArray("basePackages");
        Class<?>[] basePackageClasses = attributes.getClassArray("basePackageClasses");
        String[] value = attributes.getStringArray("value");

        // Appends value array attributes
        Set<String> packagesToScan = new LinkedHashSet<>(Arrays.asList(value));
        packagesToScan.addAll(Arrays.asList(basePackages));
        for (Class<?> basePackageClass : basePackageClasses) {
            packagesToScan.add(ClassUtils.getPackageName(basePackageClass));
        }
        if (packagesToScan.isEmpty()) {
            return Collections.singleton(ClassUtils.getPackageName(metadata.getClassName()));
        }
        return packagesToScan;
    }

    /**
     * register
     */
    public abstract void registerServiceAnnotationPostProcessor(Set<String> packagesToScan, BeanDefinitionRegistry registry);

    /**
     * register {@link ServiceAnnotationPostProcessor}
     */
    public void registerServiceAnnotationPostProcessor(Set<String> packagesToScan, BeanDefinitionRegistry registry, Class<?> rootBeanDefinitionClass) {
        AbstractBeanDefinition beanDefinition = BeanDefinitionBuilder.rootBeanDefinition(rootBeanDefinitionClass)
                .addConstructorArgValue(packagesToScan)
                .setRole(BeanDefinition.ROLE_INFRASTRUCTURE)
                .getBeanDefinition();

        BeanDefinitionReaderUtils.registerWithGeneratedName(beanDefinition, registry);
    }

}
