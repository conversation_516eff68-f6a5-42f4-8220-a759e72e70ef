package cn.huolala.arch.hermes.config.spring.definition;

import cn.huolala.arch.hermes.config.spring.beans.ServiceAnnotationPostProcessor;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;

import java.util.Set;


public class DefaultServiceBeanDefinition extends AbstractServiceBeanDefinition {

    @Override
    public void registerServiceAnnotationPostProcessor(Set<String> packagesToScan, BeanDefinitionRegistry registry) {
        super.registerServiceAnnotationPostProcessor(packagesToScan, registry, ServiceAnnotationPostProcessor.class);
    }

}
