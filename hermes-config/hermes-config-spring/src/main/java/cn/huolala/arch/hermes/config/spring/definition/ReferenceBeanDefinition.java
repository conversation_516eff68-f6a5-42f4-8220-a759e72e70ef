package cn.huolala.arch.hermes.config.spring.definition;

import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.extension.SPI;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;

import static cn.huolala.arch.hermes.common.constants.SpiConstants.DEFINITION_JSON_RPC;

@SPI(DEFINITION_JSON_RPC)
public interface ReferenceBeanDefinition {

    void registerReferenceAnnotationPostProcessor(BeanDefinitionRegistry registry);

    /**
     * The default extension of {@link ReferenceBeanDefinition}
     */
    static ReferenceBeanDefinition getDefaultExtension() {
        return ExtensionLoader.getExtensionLoader(ReferenceBeanDefinition.class).getDefaultExtension();
    }

}
