package cn.huolala.arch.hermes.config.spring.definition;

import cn.huolala.arch.hermes.common.extension.SPI;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.core.type.AnnotationMetadata;

import static cn.huolala.arch.hermes.common.constants.SpiConstants.DEFINITION_DEFAULT;

@SPI(DEFINITION_DEFAULT)
public interface ServiceBeanDefinition {

    void registerServiceAnnotationPostProcessor(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry, BeanFactory beanFactory);

}
