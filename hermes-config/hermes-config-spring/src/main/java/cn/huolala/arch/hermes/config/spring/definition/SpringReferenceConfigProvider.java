package cn.huolala.arch.hermes.config.spring.definition;

import static cn.huolala.arch.hermes.common.constants.Constants.GRPC_PROTOCOL;
import static cn.huolala.arch.hermes.common.constants.Constants.JSONRPC_PROTOCOL;
import static cn.huolala.arch.hermes.common.constants.Constants.PROTOCOL_KEY;
import static cn.huolala.arch.hermes.common.context.ApplicationContext.getAttributes;
import static com.alibaba.spring.util.AnnotationUtils.getAnnotationAttributes;
import static org.springframework.core.annotation.AnnotationUtils.synthesizeAnnotation;

import java.util.Map;

import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.AnnotationAttributes;

import cn.huolala.arch.hermes.api.annotation.HermesReference;
import cn.huolala.arch.hermes.api.grpc.IGrpc;
import cn.huolala.arch.hermes.config.ReferenceConfig;
import cn.huolala.arch.hermes.config.spring.beans.ReferenceBeanBuilder;
import cn.huolala.arch.hermes.config.spring.support.Compatible;
import cn.huolala.arch.hermes.config.support.ReferenceConfigProvider;

/**
 * 
 * Spring ReferenceConfig provider
 *
 */
public class SpringReferenceConfigProvider implements ReferenceConfigProvider {

    private final ApplicationContext ctx;

    private final Compatible compatible;

    public SpringReferenceConfigProvider() {
        this.ctx = (ApplicationContext) getAttributes().get(ApplicationContext.class.getName());
        this.compatible = Compatible.getDefaultExtension();
    }

    @Override
    public ReferenceConfig<?> buildReferenceConfig(String beanName, Map<String, Object> configs,
            Class<?> referencedType) throws Exception {
        AnnotationAttributes attributes = attributesGenerator(configs);
        if (!IGrpc.class.isAssignableFrom(referencedType)) {
            attributes.put(PROTOCOL_KEY, JSONRPC_PROTOCOL);
            return compatible.buildReferenceBean(beanName, ctx, attributes, referencedType);
        }
        attributes.put(PROTOCOL_KEY, GRPC_PROTOCOL);
        return ReferenceBeanBuilder.newBuilder(attributes, ctx)
                .interfaceClass(referencedType)
                .beanName(beanName)
                .build();
    }

    /**
     * @HermesReference attributes generator
     * 
     * @param attributes
     * @return
     */
    private AnnotationAttributes attributesGenerator(Map<String, Object> attributes) {
        HermesReference hermesReference = synthesizeAnnotation(attributes, HermesReference.class, null);
        return getAnnotationAttributes(hermesReference, false);

    }
}
