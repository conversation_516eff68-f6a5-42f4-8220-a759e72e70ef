package cn.huolala.arch.hermes.config.spring.support;

import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ClassPathBeanDefinitionScanner;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ResourceLoader;

/**
 * BeanDefinitionScanner
 * <p>
 * {@link ClassPathBeanDefinitionScanner} that exposes some methods to be public
 */
public class BeanDefinitionScanner extends AbstractBeanDefinitionScanner {

    public BeanDefinitionScanner(BeanDefinitionRegistry registry, Environment environment, ResourceLoader resourceLoader) {
        this(registry, false, environment, resourceLoader);
    }

    public BeanDefinitionScanner(BeanDefinitionRegistry registry, boolean useDefaultFilters,
                                 Environment environment, ResourceLoader resourceLoader) {
        super(registry, useDefaultFilters, environment, resourceLoader);
    }

}
