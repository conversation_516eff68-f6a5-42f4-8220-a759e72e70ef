package cn.huolala.arch.hermes.config.spring.support;

import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.common.support.Marker;
import cn.huolala.arch.hermes.config.spring.ReferenceBean;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.AnnotationAttributes;

/**
 * Compatible Marker
 */
@SPI(Constants.JSONRPC_PROTOCOL)
public interface Compatible extends Marker {

    /**
     * buildReferenceBean
     */
    ReferenceBean<?> buildReferenceBean(String referenceBeanName, ApplicationContext applicationContext, AnnotationAttributes attributes, Class<?> referencedType) throws Exception;


    /**
     * The default extension of {@link Compatible}
     */
    static Compatible getDefaultExtension() {
        return ExtensionLoader.getExtensionLoader(Compatible.class).getDefaultExtension();
    }
}
