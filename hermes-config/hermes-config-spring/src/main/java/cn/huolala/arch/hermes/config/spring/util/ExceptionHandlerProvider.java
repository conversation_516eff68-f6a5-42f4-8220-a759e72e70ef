package cn.huolala.arch.hermes.config.spring.util;

import cn.huolala.arch.hermes.cluster.error.support.GlobalConsumerExceptionHandler;
import cn.huolala.arch.hermes.cluster.error.support.GlobalProviderExceptionHandler;
import cn.huolala.arch.hermes.common.context.support.ContextAttributeProvider;
import org.springframework.context.ApplicationContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * ExceptionHandler Provider
 */
public class ExceptionHandlerProvider implements ContextAttributeProvider {
    @Override
    public Map<String, Object> getAttributes() {
        HashMap<String, Object> exceptionHandlers = new HashMap<>();
        getFirstInstance(GlobalProviderExceptionHandler.class)
                .ifPresent(handler -> exceptionHandlers.put(GlobalProviderExceptionHandler.class.getName(), handler));
        getFirstInstance(GlobalConsumerExceptionHandler.class)
                .ifPresent(handler -> exceptionHandlers.put(GlobalConsumerExceptionHandler.class.getName(), handler));
        return exceptionHandlers;
    }

    private <T> Optional<T> getFirstInstance(Class<T> clazz) {
        ApplicationContext applicationContext = (ApplicationContext) cn.huolala.arch.hermes.common.context.ApplicationContext
                .getAttributes().get(ApplicationContext.class.getName());
        if (applicationContext == null) {
            throw new IllegalStateException("Spring ApplicationContext should have already been set in ApplicationContextAttributes, "
                    + "but failed to find it.");
        }

        return applicationContext.getBeansOfType(clazz).values().stream().findFirst();
    }
}
