package cn.huolala.arch.hermes.config.spring.util;

import cn.huolala.arch.hermes.api.config.ConsumerConfig;
import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.context.ConfigManager;
import cn.huolala.arch.hermes.common.context.support.ContextAttributeProvider;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.config.spring.util.serialization.ObjectMapperFactory;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.ApplicationContext;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * ObjectMapper Provider
 */
public class ObjectMapperProvider implements ContextAttributeProvider {
    private static final Logger logger = LoggerFactory.getLogger(ObjectMapperProvider.class);

    public static final String SOA_OBJECT_MAPPER = "soaObjectMapper";
    public static final String OBJECT_MAPPER = "objectMapper";

    private static volatile ObjectMapper mapper;

    @Override
    public Map<String, Object> getAttributes() {
        Map<String, Object> attributesMap = new HashMap<>();
        attributesMap.put(ObjectMapper.class.getName(), getInstance());
        addCustomizeSerialization(attributesMap);
        return attributesMap;
    }

    private void addCustomizeSerialization(Map<String, Object> attributesMap) {
        cn.huolala.arch.hermes.common.context.ApplicationContext.getConfigManager().getConsumer()
                .ifPresent(consumerConfig -> Optional.ofNullable(consumerConfig.getSerializations())
                        .ifPresent(serializationMap -> serializationMap.forEach((appId, beanName) -> {
            if(!StringUtils.isEmpty(appId) && !StringUtils.isEmpty(beanName)) {
                ApplicationContext applicationContext = (ApplicationContext) cn.huolala.arch.hermes.common.context.ApplicationContext
                        .getAttributes().get(ApplicationContext.class.getName());
                if (applicationContext == null) {
                    throw new IllegalStateException("Spring ApplicationContext should have already been set in ApplicationContextAttributes, "
                            + "but failed to find it.");
                }
                if(applicationContext.containsBean(beanName)) {
                    ObjectMapperFactory mapperObjFactory = applicationContext.getBean(beanName, ObjectMapperFactory.class);
                    logger.info("Retrieved ObjectMapperFactory from the SpringContext context by["+ beanName +"]name");
                    ObjectMapper mapperObj = mapperObjFactory.getObjectMapper();
                    if(mapperObj != null) {
                        mapperObj.configure(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN, true);
                        attributesMap.put(String.format(Constants.CONSUMER_APP_SERIALIZATION_KEY, appId), mapperObj);
                    }
                }
            }
        })));
    }

    public static ObjectMapper getInstance() {
        if (mapper == null) {
            synchronized (ObjectMapperProvider.class) {
                ApplicationContext applicationContext = (ApplicationContext) cn.huolala.arch.hermes.common.context.ApplicationContext
                        .getAttributes().get(ApplicationContext.class.getName());
                if (mapper == null) {
                    if (applicationContext == null) {
                        throw new IllegalStateException("Spring ApplicationContext should have already been set in ApplicationContextAttributes, "
                                + "but failed to find it.");
                    }
                    if (applicationContext.containsBean(SOA_OBJECT_MAPPER)) {
                        Object mapperObj = applicationContext.getBean(SOA_OBJECT_MAPPER);
                        logger.info("Retrieved from the SpringContext context by soaObjectMapper name");
                        mapper = (ObjectMapper) mapperObj;
                    } else if (applicationContext.containsBean(OBJECT_MAPPER)) {
                        Object mapperObj = applicationContext.getBean(OBJECT_MAPPER);
                        logger.info("Retrieved from the SpringContext context by objectMapper name");
                        mapper = (ObjectMapper) mapperObj;
                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    } else {
                        try {
                            mapper = applicationContext.getBean(ObjectMapper.class);
                            mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
                            logger.info("Retrieved from the SpringContext context by ObjectMapper type");
                        } catch (Exception e) {
                            mapper = new ObjectMapper();
                            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                            mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
                            mapper.registerModule(new JavaTimeModule());
                            logger.info("Failed to get ObjectMapper for ApplicationContext ,user the default custom ObjectMapper");
                        }
                    }
                    mapper.configure(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN, true);
                }
            }
        }
        return mapper;
    }
}
