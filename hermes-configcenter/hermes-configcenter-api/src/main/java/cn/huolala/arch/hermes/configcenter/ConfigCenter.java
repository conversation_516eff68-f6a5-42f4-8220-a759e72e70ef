package cn.huolala.arch.hermes.configcenter;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.config.dynamic.DynamicConfiguration;
import cn.huolala.arch.hermes.common.config.dynamic.DynamicConfigurationFactory;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.extension.SPI;

import java.util.Optional;

import static cn.huolala.arch.hermes.common.constants.Constants.PROTOCOL_KEY;
import static cn.huolala.arch.hermes.common.constants.SpiConstants.CONFIG_CENTER_DEFAULT;

/**
 * ConfigCenter: DynamicConfigurationFactory extension
 *
 * @see DynamicConfiguration
 * @see DynamicConfigurationFactory
 */
@SPI(CONFIG_CENTER_DEFAULT)
@ApiAudience.Private
public interface ConfigCenter extends DynamicConfigurationFactory {

    /**
     * Get an instance of {@link ConfigCenter} by the URL. If not found, take the default
     *
     * @param url the url of extension of {@link ConfigCenter}
     */
    static ConfigCenter getConfigCenter(URL url) {
        String name = Optional.ofNullable(url)
                .map(u -> u.getParameter(PROTOCOL_KEY))
                .orElse(CONFIG_CENTER_DEFAULT);

        return ExtensionLoader.getExtensionLoader(ConfigCenter.class)
                .getOrDefaultExtension(name);
    }
}
