package cn.huolala.arch.hermes.demo.consumer;

import cn.huolala.arch.hermes.api.annotation.HermesReference;
import cn.huolala.arch.hermes.demo.api.BodyJsonDTO;
import cn.huolala.arch.hermes.demo.api.BodyJsonService;
import cn.huolala.arch.hermes.demo.api.HermesStandardService;
import cn.huolala.arch.hermes.demo.api.StandardDTO;
import cn.huolala.arch.hermes.demo.api.StandardService;
import cn.huolala.arch.hermes.demo.api.greeter.GreeterService;
import cn.huolala.arch.hermes.demo.api.greeter.HelloReply;
import cn.huolala.arch.hermes.demo.api.greeter.HelloRequest;
import cn.lalaframework.soa.annotation.SOAParamsMode;
import cn.lalaframework.soa.annotation.SOAResultMode;
import cn.lalaframework.soa.generic.SOAGenericService;
import cn.lalaframework.soa.generic.SOAMethodAttrs;
import cn.lalaframework.soa.jsonrpc.spring.reference.SOAReference;
import io.grpc.stub.StreamObserver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import static cn.huolala.arch.hermes.common.constants.Constants.NAME;

/**
 * JsonRpc consumer ConsumerApplication via springboot
 */
@SpringBootApplication(scanBasePackages = {"cn.huolala.arch.hermes.demo"})
public class ConsumerApplication {

    private static final Logger logger = LoggerFactory.getLogger(ConsumerApplication.class);

    @SOAReference
    private StandardService standardService;

    @Autowired
    private BodyJsonService bodyJsonService;

    @HermesReference(value = "ci-jsonrpc-demo2-grpc-svc")
    private GreeterService.Interface greeterService;

    @HermesReference("jsonrpc-provider-svc")
    private HermesStandardService hermesStandardService;

    public static void main(String[] args) {
        SpringApplication.run(ConsumerApplication.class, args);
    }

    @Bean
    public ApplicationRunner runner() {
        return args -> {
            queryNormal();
            getStandard();
            getHermesStandard();
            queryNormalBodyJsonService();
            grpcRunner();
            grpcRunnerThread();
        };
    }

    private void queryNormal() {
        Long id = 2020L;
        String name = "bodyJson-queryNormal";
        Map<Object, Object> argument = new HashMap<>();
        argument.put("id", id);
        argument.put("name", name);
        SOAGenericService bodyJsonService = SOAGenericService.getService("jsonrpc-provider-svc", "/bj");
        SOAMethodAttrs queryNormalAttrs = new SOAMethodAttrs("queryNormal", SOAParamsMode.BODY_JSON, SOAResultMode.OBJECT);
        try {
            BodyJsonDTO result = bodyJsonService.call(BodyJsonDTO.class, queryNormalAttrs, new BodyJsonDTO(), argument);
            logger.info("SOAGenericService rely: " + result);
        } catch (Throwable e) {
            logger.error("SOAGenericService error: ", e);
        }
    }

    private void getStandard() {
        try {
            StandardDTO standard = standardService.getStandard(2222L);
            logger.info("standardService rely: " + standard);
        } catch (Exception e) {
            logger.error("standardService error: ", e);
        }
    }

    private void getHermesStandard() {
        try {
            StandardDTO standard = hermesStandardService.getStandard(2222L);
            logger.info("hermes standardService rely: " + standard);
        } catch (Exception e) {
            logger.error("hermes standardService error: ", e);
        }
    }

    private void queryNormalBodyJsonService() {
        Long id = 2020L;
        String name = "bodyJson-queryNormal";
        try {
            BodyJsonDTO bodyJsonDTO = bodyJsonService.queryNormal(id, name);
            logger.info("bodyJsonService rely: " + bodyJsonDTO);
        } catch (Exception e) {
            logger.error("bodyJsonService error: ", e);
        }
    }

    private void grpcRunner() throws InterruptedException, ExecutionException {
        HelloRequest request = HelloRequest.newBuilder().setName(NAME).build();

        // sync
        HelloReply reply = greeterService.sayHello(request);
        logger.info("greeterService reply: " + reply.getMessage());

        // async
        CompletableFuture<HelloReply> replyFuture = greeterService.sayHelloAsync(request);
        while (!replyFuture.isDone()) {
            logger.info("waiting for async call...");
            TimeUnit.MILLISECONDS.sleep(3);
        }
        logger.info("greeterService async reply: " + replyFuture.get().getMessage());
    }

    private void grpcRunnerThread() throws InterruptedException, ExecutionException {
        int streamCount = 3;
        CountDownLatch countDownLatch = new CountDownLatch(streamCount);
        // bidi stream
        StreamObserver<HelloRequest> requestStream = greeterService.sayHelloBidiStream(new StreamObserver<HelloReply>() {
            @Override
            public void onNext(HelloReply helloReply) {
                logger.info("greeterService stream reply: " + helloReply.getMessage());
                countDownLatch.countDown();
            }

            @Override
            public void onError(Throwable t) {
                logger.warn("onError", t);
            }

            @Override
            public void onCompleted() {
            }
        });

        new Thread(() -> {
            for (int i = 1; i <= streamCount; i++) {
                HelloRequest request = HelloRequest.newBuilder().setName(NAME + '-' + i).build();
                requestStream.onNext(request);
            }
        }).start();

        countDownLatch.await();
        requestStream.onCompleted();
    }
}
