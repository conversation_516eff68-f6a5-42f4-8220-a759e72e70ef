package cn.huolala.arch.hermes.demo.consumer;

import cn.huolala.arch.hermes.api.annotation.HermesReference;
import cn.huolala.arch.hermes.demo.api.HermesStandardService;
import cn.huolala.arch.hermes.demo.api.StandardDTO;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/test")
public class DemoController {

    @HermesReference(value = "jsonrpc-provider-svc", fallback = FallbackHermesStandardService.class)
    private HermesStandardService hermesStandardService;

    @RequestMapping(value = "/standard")
    public StandardDTO standard( @RequestParam(name = "id") Long id) {
        StandardDTO standard = hermesStandardService.getStandard(id);
        System.out.println("客户端收到返回结果：" + standard);
        return standard;
    }

    @RequestMapping(value = "/timeout")
    public StandardDTO timeout( @RequestParam(name = "timeout") Long timeout) {
        StandardDTO standard = hermesStandardService.delayTime(timeout);
        System.out.println("客户端收到返回结果：" + standard);
        return standard;
    }

}
