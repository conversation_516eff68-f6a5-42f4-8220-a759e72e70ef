package cn.huolala.arch.hermes.demo.consumer;

import cn.huolala.arch.hermes.demo.api.HermesStandardService;
import cn.huolala.arch.hermes.demo.api.StandardDTO;

public class FallbackHermesStandardService implements HermesStandardService {
    @Override
    public StandardDTO getStandard(Long id) {

        StandardDTO standardDTO = new StandardDTO();
        standardDTO.setId(id);
        standardDTO.setStandard(false);
        standardDTO.setName("Hermes Fallback");
        return standardDTO;
    }

    @Override
    public StandardDTO delayTime(long timeout) {
        StandardDTO standardDTO = new StandardDTO();
        standardDTO.setId(timeout);
        standardDTO.setStandard(false);
        standardDTO.setName("Hermes delayTime Fallback");
        return standardDTO;
    }
}
