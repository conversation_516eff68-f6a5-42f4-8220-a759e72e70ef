spring:
  application:
    name: ci-jsonrpc-demo1-svr
  main:
    web-application-type: none

lala:
  soa:
    discovery:
      protocol: consul
      address: "consul-stg.huolala.work:80"
      token: "2c9d32f5-015e-d9aa-d777-0851b5f7fb3e"
      register: false
    config-center:
      protocol: apollo
      address: "apollo-stg.myhll.cn:8080"
    consumer:
      check: false
      validation: true
      connectionTimeout: 1000
      timeout: 1000
      scan-base-packages: cn.huolala.arch.hermes.demo.api
    consumerProtocols:
      - name: jsonrpc
        executor:
          threadname: test1
      - name: grpc
        payload: 4194304
        executor:
          threadname: test2
    hostPorts:
      jsonrpc-provider-svc:
        host: 127.0.0.1
        port: 9001

server:
  port: 9002
