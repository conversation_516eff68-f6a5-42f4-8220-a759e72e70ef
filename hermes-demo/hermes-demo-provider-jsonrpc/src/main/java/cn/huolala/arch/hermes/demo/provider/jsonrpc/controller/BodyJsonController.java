package cn.huolala.arch.hermes.demo.provider.jsonrpc.controller;

import cn.huolala.arch.hermes.demo.api.BodyJsonDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/bj")
public class BodyJsonController {
    private static final  Logger logger = LoggerFactory.getLogger(BodyJsonController.class);

    @RequestMapping(value = "queryNormal")
    public BodyJsonDTO queryNormal(@RequestBody BodyJsonDTO bodyJsonDTO) {
        bodyJsonDTO.setName(String.format(bodyJsonDTO.getName()));
        return bodyJsonDTO;
    }
}
