package cn.huolala.arch.hermes.demo.provider.jsonrpc.impl;


import cn.huolala.arch.hermes.api.annotation.HermesService;
import cn.huolala.arch.hermes.demo.api.HermesStandardService;
import cn.huolala.arch.hermes.demo.api.StandardDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

@HermesService
public class HermesStandardServiceImpl implements HermesStandardService {
    private static final Logger logger = LoggerFactory.getLogger(HermesStandardServiceImpl.class);


    @Override
    public StandardDTO getStandard(Long id) {
        StandardDTO standardDTO = new StandardDTO();
        standardDTO.setId(id);
        standardDTO.setStandard(true);
        standardDTO.setName("Hermes:服务端已经接收getStandard");
        return standardDTO;
    }

    @Override
    public StandardDTO delayTime(long timeout) {
        try{
            TimeUnit.MILLISECONDS.sleep(timeout);
        } catch (InterruptedException e) {

        }
        StandardDTO standardDTO = new StandardDTO();
        standardDTO.setId(timeout);
        standardDTO.setStandard(true);
        standardDTO.setName("Hermes:服务端delayTime已经接收延时时间:" + timeout + "ms");
        return standardDTO;
    }
}
