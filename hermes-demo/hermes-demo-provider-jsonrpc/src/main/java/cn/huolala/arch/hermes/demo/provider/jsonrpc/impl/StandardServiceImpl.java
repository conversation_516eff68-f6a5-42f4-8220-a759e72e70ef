package cn.huolala.arch.hermes.demo.provider.jsonrpc.impl;


import cn.huolala.arch.hermes.demo.api.StandardDTO;
import cn.huolala.arch.hermes.demo.api.StandardService;
import cn.lalaframework.soa.annotation.spring.SOAServiceAutoImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@SOAServiceAutoImpl(additionalPaths = {"add1", "add2"})
public class StandardServiceImpl implements StandardService {
    private static final Logger logger = LoggerFactory.getLogger(StandardServiceImpl.class);

    @Override
    public StandardDTO getStandard(Long id) {
        StandardDTO standardDTO = new StandardDTO();
        standardDTO.setId(id);
        standardDTO.setStandard(true);
        standardDTO.setName("服务端已经接收getStandard");
        return standardDTO;
    }
}
