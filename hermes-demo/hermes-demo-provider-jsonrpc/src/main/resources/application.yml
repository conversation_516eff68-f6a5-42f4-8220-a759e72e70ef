spring:
  application:
    name: jsonrpc-provider-svc

lala:
  soa:
    provider:
      scan-base-packages: "cn.huolala.arch.hermes.demo"
      accesslog: true
    discovery:
      protocol: consul
      address: "consul-stg.huolala.work:80"
      token: "2c9d32f5-015e-d9aa-d777-0851b5f7fb3e"
    config-center:
      protocol: apollo
      address: "apollo-stg.myhll.cn:8080"
    hostPorts: 
      test1: 
        host: 127.0.0.1
        port: 8081
    protocols: 
      - name: jsonrpc

server:
  port: 9001
