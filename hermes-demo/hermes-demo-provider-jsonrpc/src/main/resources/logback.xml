<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder charset="UTF-8">
            <pattern>###|||%d{yyyy-MM-dd HH:mm:ss.SSS}|||%level|||%X{HLL_TID:--}|||%X{TRACE_ID:--}|||%thread|||%logger{0}--->%msg%n</pattern>
        </encoder>
    </appender>

    <appender name ="Async" class= "ch.qos.logback.classic.AsyncAppender">
		<discardingThreshold >0</discardingThreshold>
		<queueSize>10240</queueSize>
		<appender-ref ref ="Console"/>
	</appender>

    <root level="INFO">
        <appender-ref ref="Async" />
    </root>

</configuration>
