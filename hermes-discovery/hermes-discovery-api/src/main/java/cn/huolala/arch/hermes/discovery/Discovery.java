package cn.huolala.arch.hermes.discovery;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.discovery.event.DiscoveryListener;
import cn.huolala.arch.hermes.discovery.exception.DiscoveryException;

import java.util.List;

/**
 * The common operations of Service Discovery
 */
@SPI
@ApiAudience.Private
public interface Discovery {

    /**
     * Initializes the {@link Discovery}
     */
    void initialize() throws DiscoveryException;

    /**
     * Destroy the {@link Discovery}
     */
    void destroy() throws DiscoveryException;

    /**
     * {@link #destroy()}
     */
    boolean isDestroyed();

    /**
     * Registers an instance of {@link ServiceInstance}.
     *
     * @throws DiscoveryException if failed
     */
    void register(ServiceInstance serviceInstance) throws DiscoveryException;

    /**
     * Updates the registered {@link ServiceInstance}.
     *
     * @param serviceInstance the registered {@link ServiceInstance}
     * @throws DiscoveryException if failed
     */
    void update(ServiceInstance serviceInstance) throws DiscoveryException;

    /**
     * Unregisters an instance of {@link ServiceInstance}.
     *
     * @throws DiscoveryException if failed
     */
    void unregister(ServiceInstance serviceInstance) throws DiscoveryException;

    /**
     * Register a discovery listener
     *
     * @param serviceName {@link ServiceInstance#getServiceName()}
     * @param listener    Discovery Listener
     * @throws DiscoveryException if failed
     */
    void addListener(String serviceName, List<String> filterTags, DiscoveryListener listener) throws DiscoveryException;

    /**
     * Stops one discovery listener
     *
     * @param serviceName {@link ServiceInstance#getServiceName()}
     * @param listener    Discovery Listener
     * @throws DiscoveryException if failed
     */
    void removeListener(String serviceName, List<String> filterTags, DiscoveryListener listener) throws DiscoveryException;

    /**
     * Gets all {@link ServiceInstance service instances} by the specified service name.
     *
     * @param serviceName {@link ServiceInstance#getServiceName()}
     * @throws DiscoveryException if failed
     */
    List<ServiceInstance> getInstances(String serviceName, List<String> filterTags) throws DiscoveryException;

    /**
     * get Discovery Url
     *
     * @return Discovery Url
     */
    URL getUrl();

    /**
     * A human-readable description of the implementation
     *
     * @return The description.
     */
    String toString();

    DiscoveryStrategy.Type type();

    /**
     *  Registration status
     *  @return true: registration successful, false: registration failed
     */
    boolean isRegistered(ServiceInstance serviceInstance);
}
