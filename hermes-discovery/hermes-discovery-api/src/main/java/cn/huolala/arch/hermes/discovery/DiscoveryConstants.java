package cn.huolala.arch.hermes.discovery;

import cn.huolala.arch.hermes.common.constants.ConstantsMarker;

import static cn.huolala.arch.hermes.common.constants.Constants.TAGS_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.TOKEN_KEY;

/**
 * Discovery Constants
 */
public interface DiscoveryConstants extends ConstantsMarker {
    String DISCOVERY_IP_KEY = "discover.ip";

    String REGISTER = "register";

    String UNREGISTER = "unregister";

    String SUBSCRIBE = "subscribe";

    String UNSUBSCRIBE = "unsubscribe";

    String DISCOVERY_ADMIN = "admin";

    String FROM_NODE = "from_node";

    int INVALID_PORT = 0;
    int DEFAULT_PORT = 8500;

    String TAGS = TAGS_KEY;

    String ACL_TOKEN = TOKEN_KEY;
    String DEFAULT_ACL_TOKEN = "";

    String CHECK_PASS_INTERVAL = "check-pass-interval";
    long DEFAULT_CHECK_PASS_INTERVAL = 9L;

    String CHECK_INTERVAL = "check-interval";
    long DEFAULT_CHECK_INTERVAL = DEFAULT_CHECK_PASS_INTERVAL / 3;

    String POLLING_WAIT_SEC = "polling-wait";
    int DEFAULT_POLLING_WAIT_SEC = 10;

    //3day
    int DEREGISTER_CRITICAL_SERVICE_AFTER_HOUR = 24 * 3;

    // 5 m
    long CACHE_MAX_AGE = 60 * 5;
    // 3 days
    long CACHE_MAX_STALE = 60 * 60 * 24 * 3;
}
