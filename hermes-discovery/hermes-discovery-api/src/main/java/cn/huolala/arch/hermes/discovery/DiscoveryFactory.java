package cn.huolala.arch.hermes.discovery;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.extension.SPI;

import static cn.huolala.arch.hermes.common.constants.Constants.ADMIN_HOST_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.PROTOCOL_KEY;
import static cn.huolala.arch.hermes.common.constants.SpiConstants.DISCOVERY_DEFAULT;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.DISCOVERY_ADMIN;

/**
 * The factory to create Service {@link Discovery}
 */
@SPI
@ApiAudience.Private
public interface DiscoveryFactory {

    /**
     * Get the instance of {@link Discovery}
     *
     * @param discoveryUrl the {@link URL} to connect the discovery
     * @return non-null
     */
    Discovery getServiceDiscovery(URL discoveryUrl);
    
    
    /**
     * Get the extension instance of {@link DiscoveryFactory} by {@link URL#getProtocol() the protocol}
     *
     * @param discoveryUrl the {@link URL} to connect the discovery
     * @return non-null
     */
    static List<DiscoveryFactory> getExtension(URL discoveryUrl) {
        List<DiscoveryFactory> discoveryFactories = new ArrayList<>();
        String protocol = discoveryUrl.getParameter(PROTOCOL_KEY, DISCOVERY_DEFAULT);
        ExtensionLoader<DiscoveryFactory> loader = ExtensionLoader.getExtensionLoader(DiscoveryFactory.class);
        discoveryFactories.add(loader.getOrDefaultExtension(protocol));
        //compatible load inner adminDiscovery whether admin.host config exist
        if (Optional.ofNullable(ApplicationContext.getAttributes().get(ADMIN_HOST_KEY)).isPresent()) {
            if (loader.hasExtension(DISCOVERY_ADMIN)) {
                discoveryFactories.add(loader.getExtension(DISCOVERY_ADMIN));
            }
        }
        return discoveryFactories;
    }
}
