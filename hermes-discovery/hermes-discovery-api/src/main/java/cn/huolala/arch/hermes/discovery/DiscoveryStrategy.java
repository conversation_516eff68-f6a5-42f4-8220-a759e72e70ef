package cn.huolala.arch.hermes.discovery;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.common.util.StringUtils;

import static cn.huolala.arch.hermes.common.constants.Constants.GRPC_PROTOCOL;
import static cn.huolala.arch.hermes.common.constants.Constants.JSONRPC_PROTOCOL;

@SPI("default")
@ApiAudience.Private
public interface DiscoveryStrategy {

    List<Type> getTypes(URL consumerUrl);

    enum Type {
        NODE("node", 1, Arrays.asList(JSONRPC_PROTOCOL, GRPC_PROTOCOL)),
        DOMAIN("domain", 2, Collections.singletonList(JSONRPC_PROTOCOL));

        private final String name;
        private final int priority;

        private final List<String> protocols;

        Type(String name, int priority, List<String> protocols) {
            this.name = name;
            this.priority = priority;
            this.protocols = protocols;
        }

        public int getPriority() {
            return priority;
        }

        public List<Type> getProtocolDiscoveryStrategy(String protocol) {
            List<Type> types = new ArrayList<>();
            for (Type value : values()) {
                if (value.protocols.contains(protocol)) {
                    types.add(value);
                }
            }
            return types;
        }

        public static Type getOrDefault(String typeName, Type defaultReturn) {
            for (Type value : values()) {
                if (StringUtils.equals(value.name, typeName)) {
                    return value;
                }
            }
            return defaultReturn;
        }

        public String getName() {
            return name;
        }
    }
}
