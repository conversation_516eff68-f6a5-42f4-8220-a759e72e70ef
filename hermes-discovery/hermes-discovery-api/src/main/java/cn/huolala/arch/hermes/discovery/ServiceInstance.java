package cn.huolala.arch.hermes.discovery;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;

import java.io.Serializable;
import java.util.Map;

/**
 * The model class of an instance of a service, which is used for service registration and discovery
 */
@ApiAudience.Private
public interface ServiceInstance extends Serializable {
    /**
     * The name of service that current instance belongs to (app id)
     */
    String getServiceName();

    /**
     * The hostname of the registered service instance
     */
    String getHost();

    /**
     * The port of the registered service instance
     */
    int getPort();

    String getAddress();

    /**
     * The enabled status of the registered service instance
     */
    default boolean isEnabled() {
        return true;
    }

    /**
     * The registered service instance is health or not
     */
    default boolean isHealthy() {
        return true;
    }

    /**
     * The key / value pair metadata associated with the service instance.
     *
     * @return non-null, mutable and unsorted {@link Map}
     */
    Map<String, String> getMetadata();

    /**
     * The key / value pair tag associated with the service instance.
     *
     * @return non-null, mutable and unsorted {@link Map}
     */
    Map<String, String> getTags();

    /**
     * Get the value of metadata by the specified name
     */
    default String getMetadata(String name) {
        return getMetadata(name, null);
    }

    /**
     * Get the value of metadata by the specified name
     */
    default String getMetadata(String name, String defaultValue) {
        return getMetadata().getOrDefault(name, defaultValue);
    }

    /**
     * is valid ServiceInstance
     *
     * @see #isHealthy()
     * @see #isEnabled()
     */
    static boolean isValid(ServiceInstance instance) {
        return instance != null
                && instance.isHealthy()
                && instance.isEnabled();
    }
}