package cn.huolala.arch.hermes.discovery.core;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.discovery.Discovery;
import cn.huolala.arch.hermes.discovery.DiscoveryFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Abstract {@link DiscoveryFactory} implementation with cache(key: {@link URL#toServiceStringWithoutResolving()}),
 * the subclass should implement {@link #createDiscovery(URL)} method to create an instance of {@link DiscoveryFactory}
 */
public abstract class AbstractDiscoveryFactory implements DiscoveryFactory {
    private static final ConcurrentMap<String, Discovery> DISCOVERIES = new ConcurrentHashMap<>();

    /**
     * get all Discovery
     */
    public static List<Discovery> getServiceDiscoveries() {
        return new ArrayList<>(DISCOVERIES.values());
    }
    
    /**
     * DiSCOVERYS size
     * @return size
     */
    public static int getDiscoverysSize() {
        return DISCOVERIES.size();
    }

    @Override
    public Discovery getServiceDiscovery(URL discoveryUrl) {
        final Discovery discovery = createDiscovery(discoveryUrl);
        DISCOVERIES.put(discovery.getUrl().toServiceStringWithoutResolving(), discovery);
        return discovery;
    }

    protected abstract Discovery createDiscovery(URL discoveryUrl);
}
