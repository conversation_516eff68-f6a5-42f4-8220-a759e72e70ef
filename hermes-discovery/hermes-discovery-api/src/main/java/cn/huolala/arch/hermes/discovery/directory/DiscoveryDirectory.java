package cn.huolala.arch.hermes.discovery.directory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.support.function.ThrowableAction;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.discovery.DiscoveryStrategy;
import cn.huolala.arch.hermes.discovery.ServiceInstance;
import cn.huolala.arch.hermes.discovery.event.DiscoveryEvent;
import cn.huolala.arch.hermes.discovery.event.DiscoveryListener;
import cn.huolala.arch.hermes.protocol.Invoker;

/**
 * Directory via Service Discovery
 *
 * @param <T> Directory type
 * @see DiscoveryListener
 */
@ApiAudience.Private
public class DiscoveryDirectory<T> extends DynamicDirectory<T> implements DiscoveryListener {

    private static final Logger logger = LoggerFactory.getLogger(DiscoveryDirectory.class);

    /**
     * {@link ServiceInstance#getAddress()} > Invoker
     */
    private final Map<DiscoveryStrategy.Type, Map<String, Invoker<T>>> invokerMap = new ConcurrentHashMap<>();

    public DiscoveryDirectory(Class<T> serviceType, URL url) {
        super(serviceType, url);
    }

    @Override
    public void onEvent(DiscoveryEvent event) {
        List<ServiceInstance> serviceInstances = event.getServiceInstances();
        refreshInvokers(event.getType(), serviceInstances);
    }


    /**
     * refresh Invokers
     *
     * @param serviceInstances not empty List
     */
    public synchronized void refreshInvokers(DiscoveryStrategy.Type type, List<ServiceInstance> serviceInstances) {
        Map<String, Invoker<T>> newInvokerMap = toInvokerMap(type, serviceInstances);
        Map<String, Invoker<T>> oldInvokerMap = this.invokerMap.getOrDefault(type, new ConcurrentHashMap<>());
        List<Invoker<T>> newInvokers = Collections.unmodifiableList(new ArrayList<>(newInvokerMap.values()));
        this.invokers.put(type, newInvokers);
        this.invokerMap.put(type, newInvokerMap);
        this.routerChain.setInvokers(confirmInvokers());
        destroyUnusedInvokers(oldInvokerMap, newInvokerMap);
        forbidden = forbiddenCheck();
    }

    private Map<String, Invoker<T>> toInvokerMap(DiscoveryStrategy.Type type, List<ServiceInstance> serviceInstances) {
        if (CollectionUtils.isEmpty(serviceInstances)) {
            return new ConcurrentHashMap<>();
        }

        Map<String, Invoker<T>> newInvokerMap = new HashMap<>(serviceInstances.size());
        for (ServiceInstance instance : serviceInstances) {
            String address = instance.getAddress();
            URL newUrl = buildProtocolUrl(instance, type);

            Invoker<T> oldInvoker = this.invokerMap.getOrDefault(type, CollectionUtils.emptyHashMap()).get(address);
            if (oldInvoker == null || urlChanged(oldInvoker, newUrl)) {
                try {
                    // changed, need refer again
                    Invoker<T> newInvoker = this.protocol.refer(serviceType, newUrl);
                    if (newInvoker != null) {
                        newInvokerMap.put(address, newInvoker);
                    }
                } catch (Exception e) {
                    logger.error("Failed to refer invoker for interface:" + serviceType + ", url: " + newUrl, e);
                }
            } else {
                // unChange
                newInvokerMap.put(address, oldInvoker);
            }
        }

        return newInvokerMap;
    }

    /**
     * {@link URL#getProtocolServiceKey()}
     */
    private boolean urlChanged(Invoker<T> invoker, URL newUrl) {
        URL oldUrl = invoker.getUrl();
        if (!oldUrl.getProtocolServiceKey().equals(newUrl.getProtocolServiceKey())) {
            return true;
        }

        Map<String, String> oldParams = oldUrl.getParameters();
        Map<String, String> newParams = newUrl.getParameters();
        if (oldParams == null || newParams == null) {
            return oldParams != newParams;
        }
        // fast fail
        if (oldParams.size() != newParams.size()) {
            return true;
        }
        return !oldParams.equals(newParams);
    }

    /**
     * Check whether the invoker in the cache needs to be destroyed
     */
    private void destroyUnusedInvokers(Map<String, Invoker<T>> oldInvokerMap, Map<String, Invoker<T>> newInvokerMap) {
        if (CollectionUtils.isEmptyMap(newInvokerMap)) {
            destroyAllInvokers(oldInvokerMap.values());
            return;
        }

        oldInvokerMap.values()
                .stream()
                .filter(invoker -> !newInvokerMap.containsValue(invoker))
                .forEach(this::safeDestroyInvoker);

        ThrowableAction.execute(oldInvokerMap::clear, true);
    }

    protected boolean forbiddenCheck() {
        for (List<Invoker<T>> value : invokers.values()) {
            if (CollectionUtils.isNotEmpty(value)) {
                return false;
            }
        }
        return true;
    }
}
