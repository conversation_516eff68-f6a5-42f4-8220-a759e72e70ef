package cn.huolala.arch.hermes.discovery.directory;

import cn.huolala.arch.hermes.cluster.directory.AbstractDirectory;
import cn.huolala.arch.hermes.common.Node;
import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.event.EventDispatcher;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.support.function.ThrowableAction;
import cn.huolala.arch.hermes.common.url.URLBuilder;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.NetUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.discovery.DiscoveryStrategy;
import cn.huolala.arch.hermes.discovery.ServiceInstance;
import cn.huolala.arch.hermes.discovery.support.DiscoveryUtils;
import cn.huolala.arch.hermes.common.event.MetricsEvent;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Protocol;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import com.google.common.collect.Lists;
import io.micrometer.core.instrument.Tags;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.GRPC_PORT_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.GRPC_PROTOCOL;
import static cn.huolala.arch.hermes.common.constants.Constants.PROTOCOL_KEY;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.FROM_NODE;
import static cn.huolala.arch.hermes.discovery.DiscoveryStrategy.Type.DOMAIN;
import static cn.huolala.arch.hermes.discovery.DiscoveryStrategy.Type.NODE;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.LABEL_GRAY_VERSION;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.METRIC_DOMAIN_DISPATCHER_COUNT;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.METRIC_NODE_DISPATCHER_COUNT;
import static cn.huolala.arch.hermes.protocol.exception.RpcException.FORBIDDEN_EXCEPTION;

/**
 * Dynamic Directory
 *
 * @param <T> Directory type
 * @see cn.huolala.arch.hermes.cluster.Directory
 */
@ApiAudience.Private
public abstract class DynamicDirectory<T> extends AbstractDirectory<T> {
    private static final Logger logger = LoggerFactory.getLogger(DynamicDirectory.class);

    protected final String serviceKey;
    protected final Class<T> serviceType;
    protected DiscoveryStrategy discoveryStrategy;
    protected EventDispatcher eventDispatcher;
    protected volatile Protocol protocol;
    protected volatile boolean forbidden = false;

    protected volatile Map<DiscoveryStrategy.Type, List<Invoker<T>>> invokers = new HashMap<>();


    public DynamicDirectory(Class<T> serviceType, URL url) {
        super(url);
        this.discoveryStrategy = ExtensionLoader.getExtensionLoader(DiscoveryStrategy.class)
                .getOrDefaultExtension(DiscoveryUtils.getConsumerProtocol(consumerUrl));
        this.eventDispatcher = EventDispatcher.getDefaultExtension();
        this.serviceType = serviceType;
        this.serviceKey = getConsumerUrl().getServiceKey();
        if (this.serviceType == null) {
            throw new IllegalArgumentException("service type is null.");
        }
        if (StringUtils.isEmpty(this.serviceKey)) {
            throw new IllegalArgumentException("discovery serviceKey is null.");
        }

    }

    @Override
    public List<Invoker<T>> doList(Invocation invocation) {
        if (forbidden) {
//             1. No service provider
//             2. Service providers are disabled
            throw new RpcException(FORBIDDEN_EXCEPTION, "No provider: " + invocation.getRemoteApplication()
                    + " available from discovery " + getUrl().getAddress()
                    + " for service " + getConsumerUrl().getServiceKey() + " on consumer " + NetUtils.getLocalHost());
        }
        List<Invoker<T>> routeInvokers = null;
        List<DiscoveryStrategy.Type> types = discoveryStrategy.getTypes(getConsumerUrl());
        if (types.contains(NODE)) {
            List<Invoker<T>> nodeInvokers = invokers.get(NODE);
            if (CollectionUtils.isNotEmpty(nodeInvokers)) {
                routerChain.setInvokers(Lists.newArrayList(nodeInvokers));
                // Get invokers from cache, only runtime routers will be executed.
                routeInvokers = routerChain.route(getConsumerUrl(), invocation);
            }
        }

        if (CollectionUtils.isNotEmpty(routeInvokers)) {
            incrNodeDispatchCount(invocation);
        } else {
            //domain backup
            routeInvokers = invokers.get(DOMAIN);
            if (CollectionUtils.isNotEmpty(routeInvokers)) {
                incrDomainDispatchCount(invocation);
            }
        }
        return routeInvokers == null ? Collections.emptyList() : routeInvokers;
    }

    @Override
    public Class<T> getInterface() {
        return serviceType;
    }

    @Override
    public void destroy() {
        if (isDestroyed()) {
            return;
        }

        super.destroy();
        try {
            invokers.values().forEach(this::destroyAllInvokers);
            invokers = null;
        } catch (Throwable t) {
            logger.warn("Failed to destroy service " + serviceKey, t);
        }
    }

    @Override
    public boolean isAvailable() {
        if (isDestroyed()) {
            return false;
        }

        List<Invoker<T>> checkInvokers = new ArrayList<>();
        discoveryStrategy.getTypes(consumerUrl)
                .forEach(type -> checkInvokers.addAll(this.invokers.getOrDefault(type, new ArrayList<>())));
        return checkInvokers.stream().anyMatch(Node::isAvailable);
    }
    
    protected void destroyAllInvokers(Collection<Invoker<T>> invokers) {
        if (invokers != null) {
            invokers.forEach(this::safeDestroyInvoker);
            ThrowableAction.execute(invokers::clear, true);
        }
    }

    protected void safeDestroyInvoker(Invoker<T> invoker) {
        try {
            invoker.destroy();
            if (logger.isDebugEnabled()) {
                logger.debug("Destroy invoker[" + invoker.getUrl() + "] success. ");
            }
        } catch (Exception e) {
            logger.warn("Failed to destroy service " + serviceKey + " to provider " + invoker.getUrl(), e);
        }
    }

    public URL buildProtocolUrl(ServiceInstance instance, DiscoveryStrategy.Type type) {
        String protocol = consumerUrl.getParameter(PROTOCOL_KEY);
        String interfaceName = consumerUrl.getPath();

        String host = instance.getHost();
        int port = Optional.ofNullable(instance.getTags().get(GRPC_PORT_KEY))
                .filter(s -> StringUtils.equals(protocol, GRPC_PROTOCOL))
                .map(Integer::parseInt)
                .orElse(instance.getPort());
        return new URLBuilder(protocol, host, port)
                .setPath(interfaceName)
                .addParameters(consumerUrl.getParameters())
                .addParameters(instance.getTags())
                .addParameter(FROM_NODE, Objects.equals(type, NODE))
                .build();
    }

    /**
     * confirm the latest invoker
     */
    protected List<Invoker<T>> confirmInvokers() {
        LinkedList<List<Invoker<T>>> confirmInvokers = new LinkedList<>();
        discoveryStrategy.getTypes(getConsumerUrl()).forEach(type -> Optional.ofNullable(invokers.get(type))
                .filter(CollectionUtils::isNotEmpty).ifPresent(confirmInvokers::addLast));
        if (CollectionUtils.isEmpty(confirmInvokers)) {
            return new ArrayList<>();
        }
        return confirmInvokers.getFirst();
    }

    public void setProtocol(Protocol protocol) {
        this.protocol = protocol;
    }

    private void incrDomainDispatchCount(Invocation invocation) {
        String grayVersion = StringUtils.emptyIfNull(invocation.getGrayVersion());
        eventDispatcher.dispatch(new MetricsEvent(meterRegistry -> {
            Tags tags = Tags.of("downstream_id", DiscoveryUtils.getConsumerServiceName(consumerUrl),
                    LABEL_GRAY_VERSION, grayVersion);
            meterRegistry.counter(METRIC_DOMAIN_DISPATCHER_COUNT, tags).increment();
        }));
    }

    private void incrNodeDispatchCount(Invocation invocation) {
        String grayVersion = StringUtils.emptyIfNull(invocation.getGrayVersion());
        eventDispatcher.dispatch(new MetricsEvent(meterRegistry -> {
            Tags tags = Tags.of("downstream_id", DiscoveryUtils.getConsumerServiceName(consumerUrl),
                    LABEL_GRAY_VERSION, grayVersion);
            meterRegistry.counter(METRIC_NODE_DISPATCHER_COUNT, tags).increment();
        }));
    }
}
