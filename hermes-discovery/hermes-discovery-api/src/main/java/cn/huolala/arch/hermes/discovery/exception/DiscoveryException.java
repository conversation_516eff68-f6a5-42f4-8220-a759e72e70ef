package cn.huolala.arch.hermes.discovery.exception;

import cn.huolala.arch.hermes.common.exception.BaseRuntimeException;

/**
 * DiscoveryException
 */
public class DiscoveryException extends BaseRuntimeException {
    private static final long serialVersionUID = 7511945795837059389L;

    public DiscoveryException() {
    }

    public DiscoveryException(String message) {
        super(message);
    }

    public DiscoveryException(String message, Throwable cause) {
        super(message, cause);
    }

    public DiscoveryException(Throwable cause) {
        super(cause);
    }
}
