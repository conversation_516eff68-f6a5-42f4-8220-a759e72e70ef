package cn.huolala.arch.hermes.discovery.integration;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.thread.NamedThreadFactory;
import cn.huolala.arch.hermes.discovery.support.DiscoveryUtils;
import cn.huolala.arch.hermes.protocol.Exporter;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Protocol;
import cn.huolala.arch.hermes.protocol.support.InvokerAdapter;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

import static cn.huolala.arch.hermes.common.constants.Constants.WAIT_KEY;

/**
 * DiscoveryProtocol Exporter<br/>
 * exporter proxy, establish the corresponding relationship between the returned exporter and the exporter exported by the protocol,
 * and can modify the relationship at the time of override
 *
 * @param <T> Exporter type
 */
public class DiscoveryExporter<T> implements Exporter<T> {
    private static final Logger logger = LoggerFactory.getLogger(DiscoveryExporter.class);

    private static final int DEFAULT_SHUTDOWN_WAIT = Constants.DEFAULT_SHUTDOWN_WAIT;

    private static final ExecutorService executor = Executors
            .newCachedThreadPool(new NamedThreadFactory("Exporter-UnExport", true));

    private final URL discoveryUrl;
    private final Protocol protocol;
    private final DiscoveryManager manager;
    private final Invoker<T> originInvoker;

    private URL providerUrl;
    private Exporter<T> exporter;

    private final AtomicBoolean unExported = new AtomicBoolean(false);

    public DiscoveryExporter(Protocol protocol, Invoker<T> originInvoker) {
        this.discoveryUrl = originInvoker.getUrl();
        this.protocol = protocol;
        this.originInvoker = originInvoker;
        this.manager = DiscoveryManager.getInstance(discoveryUrl);

        doExport();
    }

    private void doExport() {
        this.providerUrl = DiscoveryUtils.getProviderUrl(originInvoker);
        this.exporter = protocol.export(new InvokerAdapter<>(originInvoker, providerUrl));

        // register
        manager.register(providerUrl);
    }

    @Override
    public Invoker<T> getInvoker() {
        return exporter.getInvoker();
    }

    public Invoker<T> getOriginInvoker() {
        return originInvoker;
    }

    @Override
    public void unExport() {
        if (!unExported.compareAndSet(false, true)) {
            return;
        }

        // unregister
        manager.unregister(providerUrl);

        // graceful unExport
        executor.submit(() -> {
            try {
                int timeout = getShutdownWaitTimeout();
                if (timeout > 0) {
                    logger.info("Waiting " + timeout + "ms for discovery to notify all consumers before unExport.");
                    Thread.sleep(timeout);
                }
                exporter.unExport();
            } catch (Throwable t) {
                logger.warn(t.getMessage(), t);
            }
        });
    }

    public URL getDiscoveryUrl() {
        return discoveryUrl;
    }

    public URL getProviderUrl() {
        return providerUrl;
    }

    /**
     * get ShutdownWaitTimeout: MILLISECONDS
     */
    private int getShutdownWaitTimeout() {
        return discoveryUrl.getParameter(WAIT_KEY, DEFAULT_SHUTDOWN_WAIT);
    }
}
