package cn.huolala.arch.hermes.discovery.integration;

import cn.huolala.arch.hermes.cluster.Cluster;
import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.discovery.directory.DiscoveryDirectory;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Protocol;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

import static cn.huolala.arch.hermes.common.constants.Constants.CLUSTER_KEY;

/**
 * DiscoveryProtocol Invoker
 *
 * @param <T> Exporter type
 */
public class DiscoveryInvoker<T> implements Invoker<T> {
    private static final Logger logger = LoggerFactory.getLogger(DiscoveryInvoker.class);

    protected final URL discoveryUrl;
    protected final Protocol protocol;
    protected final DiscoveryManager manager;
    protected final Class<T> type;

    protected URL consumerUrl;
    protected DiscoveryDirectory<T> directory;
    protected Invoker<T> invoker;

    public DiscoveryInvoker(Protocol protocol, Class<T> type, URL url) {
        this.discoveryUrl = url;
        this.protocol = protocol;
        this.type = type;
        this.manager = DiscoveryManager.getInstance(url);

        buildInvoker();
    }

    protected void buildInvoker() {
        this.directory = new DiscoveryDirectory<>(type, discoveryUrl);
        this.directory.setProtocol(protocol);
        this.consumerUrl = this.directory.getConsumerUrl();

        // subscribe
        manager.subscribe(consumerUrl, directory);
        // init service instances
        this.manager.getServiceInstancesAndRefresh(this.consumerUrl, directory);

        Cluster cluster = Cluster.getCluster(consumerUrl.getParameter(CLUSTER_KEY));
        this.invoker = cluster.join(directory);
    }


    @Override
    public Class<T> getInterface() {
        return type;
    }

    @Override
    public URL getUrl() {
        if (invoker != null) {
            return invoker.getUrl();
        }
        return consumerUrl;
    }

    @Override
    public boolean isAvailable() {
        return invoker != null && invoker.isAvailable();
    }

    @Override
    public Result invoke(Invocation invocation) throws RpcException {
        return invoker.invoke(invocation);
    }

    @Override
    public void destroy() {
        // unsubscribe
        manager.unsubscribe(consumerUrl, directory);

        // FIXME must be executed after unsubscribing, also destroy the Directory
        invoker.destroy();
    }

}
