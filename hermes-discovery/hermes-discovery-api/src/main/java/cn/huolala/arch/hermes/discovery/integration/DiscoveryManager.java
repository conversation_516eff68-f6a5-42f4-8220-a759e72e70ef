package cn.huolala.arch.hermes.discovery.integration;

import cn.huolala.arch.hermes.cluster.Directory;
import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.config.dynamic.DynamicConfiguration;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.discovery.Discovery;
import cn.huolala.arch.hermes.discovery.DiscoveryFactory;
import cn.huolala.arch.hermes.discovery.ServiceInstance;
import cn.huolala.arch.hermes.discovery.directory.DiscoveryDirectory;
import cn.huolala.arch.hermes.discovery.event.DiscoveryListener;
import cn.huolala.arch.hermes.metadata.Metadata;
import cn.huolala.arch.hermes.metadata.WritableMetadata;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static cn.huolala.arch.hermes.common.constants.Constants.INTERFACE_KEY;
import static cn.huolala.arch.hermes.discovery.support.DiscoveryUtils.getConsumerServiceFilterTags;
import static cn.huolala.arch.hermes.discovery.support.DiscoveryUtils.getConsumerServiceName;
import static java.lang.String.format;

/**
 * Discovery Manager
 *
 * @see Discovery
 * @see Directory
 * @see Metadata
 * @see DynamicConfiguration
 */
@ApiAudience.Private
public class DiscoveryManager {
    private static final Logger logger = LoggerFactory.getLogger(DiscoveryManager.class);

    private static final Map<String, DiscoveryManager> MANAGERS = new ConcurrentHashMap<>();

    private final URL discoveryUrl;
    private final List<Discovery> discoveries;
    private final WritableMetadata metadata;

    /**
     * get cached DiscoveryManager instance
     *
     * @see #getCacheKey(URL)
     */
    public static DiscoveryManager getInstance(URL discoveryUrl) {
        return MANAGERS.computeIfAbsent(getCacheKey(discoveryUrl), k -> new DiscoveryManager(discoveryUrl));
    }

    private static String getCacheKey(URL discoveryUrl) {
        return discoveryUrl.toServiceStringWithoutResolving();
    }

    /**
     * destroy all instances
     */
    public static synchronized void destroyAll() {
        MANAGERS.forEach((k, v) -> v.destroy());
        MANAGERS.clear();
    }

    DiscoveryManager(URL discoveryUrl) {
        this.discoveryUrl = discoveryUrl;
        this.metadata = WritableMetadata.getDefaultExtension();
        this.discoveries = createServiceDiscovery(discoveryUrl);
    }

    // register & unregister for Exporter

    public void register(URL providerUrl) {
        if (metadata.exportURL(providerUrl)) {
            if (logger.isInfoEnabled()) {
                logger.info(format("The URL[%s] registered successfully.", providerUrl.toString()));
            }

            metadata.publishServiceDefinition(providerUrl);
        } else {
            if (logger.isInfoEnabled()) {
                logger.info(format("The URL[%s] has been registered.", providerUrl.toString()));
            }
        }
    }

    public void unregister(URL providerUrl) {
        if (metadata.unExportURL(providerUrl)) {
            if (logger.isInfoEnabled()) {
                logger.info(format("The URL[%s] unregister successfully.", providerUrl.toString()));
            }
        } else {
            if (logger.isWarnEnabled()) {
                logger.info(format("The URL[%s] has been unregister.", providerUrl.toString()));
            }
        }
    }

    // subscribe & unsubscribe for Invoker

    public void subscribe(URL consumerUrl, DiscoveryListener listener) {
        discoveries.forEach(discovery -> discovery
                .addListener(getConsumerServiceName(consumerUrl), getConsumerServiceFilterTags(consumerUrl), listener));
        metadata.subscribeURL(consumerUrl);
    }

    public void unsubscribe(URL consumerUrl, DiscoveryListener listener) {
        discoveries.forEach(discovery -> discovery
                .removeListener(getConsumerServiceName(consumerUrl), getConsumerServiceFilterTags(consumerUrl), listener));
        metadata.unsubscribeURL(consumerUrl);
    }

    public void getServiceInstancesAndRefresh(URL consumerUrl, DiscoveryDirectory<?> directory) {
        for (Discovery discovery : discoveries) {
            List<ServiceInstance> instances = discovery
                    .getInstances(getConsumerServiceName(consumerUrl), getConsumerServiceFilterTags(consumerUrl));
            if (CollectionUtils.isNotEmpty(instances)) {
                directory.refreshInvokers(discovery.type(), instances);
            }
        }
    }

    public void destroy() {
        discoveries.stream()
                .filter(discovery -> !discovery.isDestroyed())
                .forEach(Discovery::destroy);

    }

    public URL getDiscoveryUrl() {
        return discoveryUrl;
    }

    private List<Discovery> createServiceDiscovery(URL discoveryUrl) {
        discoveryUrl.addParameter(INTERFACE_KEY, Discovery.class.getName());
        return DiscoveryFactory.getExtension(discoveryUrl).stream()
                .map(discoveryFactory -> {
                    Discovery serviceDiscovery = discoveryFactory.getServiceDiscovery(discoveryUrl);
                    serviceDiscovery.initialize();
                    return serviceDiscovery;
                }).collect(Collectors.toList());
    }
}
