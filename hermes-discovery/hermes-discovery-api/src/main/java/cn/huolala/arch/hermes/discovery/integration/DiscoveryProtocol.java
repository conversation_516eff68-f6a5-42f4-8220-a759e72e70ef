package cn.huolala.arch.hermes.discovery.integration;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.extension.Inject;
import cn.huolala.arch.hermes.common.extension.Wrapper;
import cn.huolala.arch.hermes.discovery.support.DiscoveryUtils;
import cn.huolala.arch.hermes.protocol.Exporter;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Protocol;
import cn.huolala.arch.hermes.protocol.ProtocolServer;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Service Discovery decorator for Protocol
 */
@Wrapper.Ignore
public class DiscoveryProtocol implements Protocol {
    protected volatile Protocol protocol;

    private final ConcurrentMap<String, DiscoveryExporter<?>> exporters = new ConcurrentHashMap<>();

    @SuppressWarnings("unchecked")
    @Override
    public <T> Exporter<T> export(Invoker<T> invoker) throws RpcException {
        return (DiscoveryExporter<T>) exporters.computeIfAbsent(getCacheKey(invoker), k ->
                new DiscoveryExporter<>(protocol, invoker));
    }

    String getCacheKey(final Invoker<?> originInvoker) {
        URL providerUrl = DiscoveryUtils.getProviderUrl(originInvoker);
        return providerUrl.removeParameters("dynamic", "enabled").toFullString();
    }

    @Override
    public <T> Invoker<T> refer(Class<T> type, URL url) throws RpcException {
        return new DiscoveryInvoker<>(protocol, type, url);
    }

    @Override
    public void destroy() {
        DiscoveryManager.destroyAll();
        // cannot call Adaptive protocol.destroy();
    }

    @Override
    public int getDefaultPort() {
        return protocol.getDefaultPort();
    }

    @Override
    public List<ProtocolServer> getServers() {
        return protocol.getServers();
    }


    @Inject
    public void setProtocol(Protocol protocol) {
        this.protocol = protocol;
    }

}
