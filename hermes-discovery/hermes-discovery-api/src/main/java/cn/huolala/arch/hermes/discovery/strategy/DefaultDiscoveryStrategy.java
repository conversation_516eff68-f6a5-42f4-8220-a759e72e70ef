package cn.huolala.arch.hermes.discovery.strategy;

import java.util.Collections;
import java.util.List;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.discovery.DiscoveryStrategy;

@ApiAudience.Private
public class DefaultDiscoveryStrategy implements DiscoveryStrategy {

    /**
     * GRPC Default NODE
     */
    @Override
    public List<Type> getTypes(URL consumerUrl) {
        return Collections.singletonList(Type.NODE);
    }
}
