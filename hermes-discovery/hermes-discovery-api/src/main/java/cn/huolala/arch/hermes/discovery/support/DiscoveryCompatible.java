package cn.huolala.arch.hermes.discovery.support;

import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.discovery.ServiceInstance;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.spec.classification.ApiStability;

/**
 * Discovery Compatible SPI<br/>
 * !WARNING: @ApiAudience.LimitedPrivate
 */
@SPI(internal = false)
@ApiAudience.LimitedPrivate({"CI", "MAP"})
@ApiStability.Evolving
public interface DiscoveryCompatible {
    /**
     * Hooks before ServiceInstance registration
     */
    void preRegister(ServiceInstance instance);

    /**
     * Hooks after ServiceInstance discovery，such as: service reference, referenced service instance changes
     *
     * @param origin origin obj from register, such as: consul `ServiceHealth`
     */
    void postDiscovery(Object origin, ServiceInstance instance);
}
