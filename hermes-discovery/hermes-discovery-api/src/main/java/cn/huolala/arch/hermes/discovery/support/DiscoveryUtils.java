package cn.huolala.arch.hermes.discovery.support;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.tool.Assert;
import cn.huolala.arch.hermes.common.url.URLStrParser;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.protocol.Invoker;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.EXPORT_KEY;
import static cn.huolala.arch.hermes.cluster.ClusterConstants.REFER_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.PROTOCOL_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.REMOTE_APPLICATION_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.SERVICE_FILTRATE_DISABLED;
import static cn.huolala.arch.hermes.common.constants.Constants.SERVICE_FILTRATE_TAG_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.SERVICE_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.UNDERLINE_SEPARATOR;

/**
 * Discovery Utils
 */
public final class DiscoveryUtils {
    private DiscoveryUtils() {
    }

    /**
     * Get the address of the providerUrl
     *
     * @see cn.huolala.arch.hermes.cluster.ClusterConstants#EXPORT_KEY
     */
    public static URL getProviderUrl(final Invoker<?> invoker) {
        String export = invoker.getUrl().getParameterAndDecoded(EXPORT_KEY);
        if (export == null || export.length() == 0) {
            throw new IllegalArgumentException("The discovery export url is null! discovery url: " + invoker.getUrl());
        }
        return URLStrParser.parseDecodedStr(export);
    }

    public static String getConsumerServiceName(URL consumerUrl) {
        String serviceName = consumerUrl.getParameter(REMOTE_APPLICATION_KEY);
        Assert.notEmpty(serviceName, "Consumer URL must contain '" + REMOTE_APPLICATION_KEY + "' parameter");
        return serviceName;
    }

    public static String getConsumerProtocol(URL consumerUrl) {
        String protocol = consumerUrl.getParameter(PROTOCOL_KEY);
        Assert.notEmpty(protocol, "Consumer URL must contain '" + PROTOCOL_KEY + "' parameter");
        return protocol;
    }

    public static List<String> getConsumerServiceFilterTags(URL consumerUrl) {
        if (Boolean.toString(true).equalsIgnoreCase(System.getProperty(SERVICE_FILTRATE_DISABLED))) {
            return new ArrayList<>();
        } else {
            return new ArrayList<>(Collections.singletonList(consumerUrl.getParameter(SERVICE_FILTRATE_TAG_KEY, SERVICE_KEY)));
        }
    }

    public static String listenerKey(List<String> filterTags, String serviceName) {
        List<String> joinTags = CollectionUtils.isEmpty(filterTags) ? Collections.emptyList() : filterTags;
        return serviceName + UNDERLINE_SEPARATOR + String.join(UNDERLINE_SEPARATOR, joinTags);
    }

    /**
     * get Parameters from URL refer QueryString
     *
     * @see cn.huolala.arch.hermes.cluster.ClusterConstants#REFER_KEY
     */
    public static Map<String, String> getReferParameters(URL url) {
        return StringUtils.parseQueryString(url.getParameterAndDecoded(REFER_KEY));
    }
}
