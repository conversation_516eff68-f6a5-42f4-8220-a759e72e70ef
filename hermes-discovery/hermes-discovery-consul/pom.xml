<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.huolala.arch.hermes</groupId>
        <artifactId>hermes-discovery</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>hermes-discovery-consul</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>cn.huolala.arch.hermes</groupId>
            <artifactId>hermes-discovery-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.orbitz.consul</groupId>
            <artifactId>consul-client</artifactId>
        </dependency>
    </dependencies>
</project>