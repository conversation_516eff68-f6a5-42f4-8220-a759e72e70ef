package cn.huolala.arch.hermes.discovery.consul;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.context.support.HealthIndicator;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.discovery.DiscoveryStrategy;
import cn.huolala.arch.hermes.discovery.ServiceInstance;
import cn.huolala.arch.hermes.discovery.core.AbstractDiscovery;
import cn.huolala.arch.hermes.discovery.core.DefaultServiceInstance;
import cn.huolala.arch.hermes.discovery.event.DiscoveryListener;
import cn.huolala.arch.hermes.discovery.exception.DiscoveryException;
import cn.huolala.arch.hermes.discovery.support.DiscoveryUtils;
import cn.huolala.arch.hermes.metadata.event.AppRunTimeEvent;
import com.google.common.net.HostAndPort;
import com.orbitz.consul.Consul;
import com.orbitz.consul.NotRegisteredException;
import com.orbitz.consul.cache.ServiceHealthCache;
import com.orbitz.consul.config.CacheConfig;
import com.orbitz.consul.config.ClientConfig;
import com.orbitz.consul.model.agent.ImmutableRegCheck;
import com.orbitz.consul.model.agent.ImmutableRegistration;
import com.orbitz.consul.model.agent.Registration;
import com.orbitz.consul.model.health.Service;
import com.orbitz.consul.model.health.ServiceHealth;
import com.orbitz.consul.option.ConsistencyMode;
import com.orbitz.consul.option.ImmutableQueryOptions;
import com.orbitz.consul.option.QueryOptions;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.START_TIME_KEY;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.ACL_TOKEN;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.CACHE_MAX_AGE;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.CACHE_MAX_STALE;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.CHECK_INTERVAL;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.CHECK_PASS_INTERVAL;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.DEFAULT_ACL_TOKEN;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.DEFAULT_CHECK_INTERVAL;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.DEFAULT_CHECK_PASS_INTERVAL;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.DEFAULT_POLLING_WAIT_SEC;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.DEFAULT_PORT;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.DEREGISTER_CRITICAL_SERVICE_AFTER_HOUR;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.INVALID_PORT;
import static cn.huolala.arch.hermes.discovery.DiscoveryConstants.POLLING_WAIT_SEC;
import static cn.huolala.arch.hermes.metadata.event.AppRunTimeEvent.MEMO_KEY;
import static cn.huolala.arch.hermes.metadata.event.AppRunTimeEvent.SUCCESS;
import static java.util.concurrent.TimeUnit.SECONDS;

/**
 * Consul Discovery
 */
public class ConsulDiscovery extends AbstractDiscovery<ConsulHealthListener, ServiceHealth> {
    private static final Logger logger = LoggerFactory.getLogger(ConsulDiscovery.class);

    private static final String TTL_NOTE = "TTL check passing by SOA framework";
    private static final String TTL_NOTE_FAIL = "TTL check failed by SOA framework";

    private final AtomicBoolean registrySuccessFlag = new AtomicBoolean(false);

    private String aclToken;
    private long checkPassInterval;
    private long checkInterval;

    private Consul consul;
    private ConsulTtlScheduler ttlScheduler;


    public ConsulDiscovery(URL discoveryUrl) {
        super(discoveryUrl, true);
    }

    @SuppressWarnings("UnstableApiUsage")
    @Override
    public void initialize() throws DiscoveryException {
        try {
            this.aclToken = url.getParameter(ACL_TOKEN, DEFAULT_ACL_TOKEN);
            this.checkPassInterval = url.getParameter(CHECK_PASS_INTERVAL, DEFAULT_CHECK_PASS_INTERVAL);
            this.checkInterval = url.getParameter(CHECK_INTERVAL, DEFAULT_CHECK_INTERVAL);

            int watchSeconds = getWatchSeconds();
            HostAndPort hostAndPort = HostAndPort.fromParts(url.getHost(),
                    INVALID_PORT != url.getPort() ? url.getPort() : DEFAULT_PORT);
            CacheConfig cacheConfig = CacheConfig.builder()
                    .withWatchDuration(Duration.ofSeconds(watchSeconds))
                    .build();

            this.consul = Consul.builder()
                    .withHostAndPort(hostAndPort)
                    .withAclToken(aclToken)
                    .withReadTimeoutMillis(SECONDS.toMillis(watchSeconds + 1))
                    .withClientConfiguration(new ClientConfig(cacheConfig))
                    .build();
            this.ttlScheduler = new ConsulTtlScheduler(this, checkInterval);
        } catch (Exception e) {
            logger.error("Consul discovery init failed," +
                    "host:[" + url.getHost() + "]," +
                    "port:[" + (INVALID_PORT != url.getPort() ? url.getPort() : DEFAULT_PORT) + "]," +
                    "token:[" + url.getParameter(ACL_TOKEN, DEFAULT_ACL_TOKEN) + "]", e);
            throw new DiscoveryException(e);
        }
    }

    @Override
    public void doRegister(ServiceInstance serviceInstance) throws DiscoveryException {
        try {
            Registration registration = buildRegistration(serviceInstance, true);

            ttlScheduler.add(serviceInstance, registration);
            consul.agentClient().register(registration);
            publishRegistrySuccessEvent(serviceInstance, AppRunTimeEvent.EventType.RegistrySubEventType.UP);
            logger.info("Consul register succeed, service instance:" + serviceInstance);
        } catch (Exception e) {
            publishRegistryFailEvent(serviceInstance, AppRunTimeEvent.EventType.RegistrySubEventType.UP, e.getMessage());
            logger.error("Consul register failed, service instance:" + serviceInstance);
            throw new DiscoveryException(e);
        }
    }

    @Override
    public void update(ServiceInstance serviceInstance) throws DiscoveryException {
        try {
            Registration registration = buildRegistration(serviceInstance, false);

            consul.agentClient().register(registration);
            ttlScheduler.add(serviceInstance, registration);
            logger.info("Consul update succeed, service instance:" + serviceInstance);
        } catch (Exception e) {
            logger.error("Consul update failed, service instance:" + serviceInstance);
            throw new DiscoveryException(e);
        }
    }

    @Override
    public void doUnregister(ServiceInstance serviceInstance) throws DiscoveryException {
        String id = buildId(serviceInstance);
        if (!isRegistered(id)) {
            logger.info(id + " 's registration information does not exist on consul, unregister operation will be cancel");
            return;
        }
        try {
            consul.agentClient().deregister(id);
            ttlScheduler.remove(id);
            publishRegistrySuccessEvent(serviceInstance, AppRunTimeEvent.EventType.RegistrySubEventType.DOWN);
            logger.info("Consul unregister succeed, service instance:" + serviceInstance);
        } catch (RuntimeException e) {
            publishRegistryFailEvent(serviceInstance, AppRunTimeEvent.EventType.RegistrySubEventType.DOWN, e.getMessage());
            logger.error("Consul unregister failed, service instance id:" + id);
            throw new DiscoveryException(e);
        }
    }

    private Registration buildRegistration(ServiceInstance serviceInstance, boolean firstCreate) {
        return ImmutableRegistration.builder()
                .name(serviceInstance.getServiceName())
                .id(buildId(serviceInstance))
                .address(serviceInstance.getHost())
                .port(serviceInstance.getPort())
                .check(buildCheck(serviceInstance))
                .tags(buildTags(serviceInstance, firstCreate))
                .meta(buildMetadata(serviceInstance))
                .build();
    }

    @Override
    public ServiceInstance doBuildServiceInstance(ServiceHealth serviceHealth) {
        Service service = serviceHealth.getService();

        DefaultServiceInstance instance = new DefaultServiceInstance(service.getService(), service.getAddress(), service.getPort());
        instance.setTags(tagList2Map(service.getTags()));
        instance.setMetadata(service.getMeta());
        return instance;
    }

    @Override
    public void addListener(String serviceName, List<String> filterTags, DiscoveryListener listener) throws DiscoveryException {
        try {
            listeners.computeIfAbsent(DiscoveryUtils.listenerKey(filterTags, serviceName), key ->
                    new ConsulHealthListener(this, serviceName, filterTags)).addListener(listener);
        } catch (Exception e) {
            logger.error("Consul add listener failed");
            throw new DiscoveryException(e);
        }
    }

    @Override
    public void removeListener(String serviceName, List<String> filterTags, DiscoveryListener listener) throws DiscoveryException {
        try {
            ConsulHealthListener consulHealthListener = listeners.get(DiscoveryUtils.listenerKey(filterTags, serviceName));
            if (consulHealthListener != null) {
                consulHealthListener.removeListener(listener);
                if (!consulHealthListener.hasInternalListener()) {
                    consulHealthListener.destroy();
                    listeners.remove(DiscoveryUtils.listenerKey(filterTags, serviceName));
                }
            }
        } catch (Exception e) {
            logger.error("Consul remove listener failed");
            throw new DiscoveryException(e);
        }
    }

    @Override
    public List<ServiceInstance> getInstances(String serviceName, List<String> filterTags) throws DiscoveryException {
        try {
            QueryOptions queryOptions = newDefaultQueryOptions(filterTags.toArray(new String[0]));
            return consul.healthClient().getHealthyServiceInstances(serviceName, queryOptions).getResponse()
                    .stream()
                    .map(this::buildServiceInstance)
                    .filter(ServiceInstance::isValid)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Consul get instances failed, service name:" + serviceName);
            throw new DiscoveryException(e);
        }
    }

    @Override
    public DiscoveryStrategy.Type type() {
        return DiscoveryStrategy.Type.NODE;
    }

    @Override
    protected void doDestroy() {
        listeners.forEach((s, listener) -> listener.destroy());
        listeners.clear();

        if (ttlScheduler != null) {
            ttlScheduler.shutdown();
        }

        if (consul != null) {
            consul.destroy();
        }
    }

    public void checkPass(ServiceInstance serviceInstance, Registration registration) {
        try {
            if (!isDestroyed()) {
                String id = registration.getId();
                if (logger.isDebugEnabled()) {
                    logger.debug("Sending consul TTL check passing for: " + id);
                }

                if (checkHealthIndicators()) {
                    consul.agentClient().pass(id, TTL_NOTE);
                } else {
                    if (!registrySuccessFlag.get()){
                        throw new NotRegisteredException("trigger re-register");
                    }else {
                        consul.agentClient().fail(id, TTL_NOTE_FAIL);
                    }
                }
            }
        } catch (NotRegisteredException e) {
            if (!isDestroyed()) {
                logger.error("Consul TTL check passing exception", e);
                try {
                    logger.info("Consul will try re-register");
                    consul.agentClient().register(registration);
                    publishRegistrySuccessEvent(serviceInstance, AppRunTimeEvent.EventType.RegistrySubEventType.RECONNECTION);
                    logger.info("Consul re-register succeed");
                } catch (Exception ex) {
                    publishRegistryFailEvent(serviceInstance, AppRunTimeEvent.EventType.RegistrySubEventType.RECONNECTION, ex.getMessage());
                    logger.warn("Consul re-register failed", ex);
                }
            }

        }
    }

    private boolean isRegistered(String serviceId) {
        boolean registered = true;
        try {
            registered = consul.agentClient().isRegistered(serviceId);
        } catch (Exception e) {
            logger.warn("Consul isRegistered failed service instance id:" + serviceId + "fail: " + e.getMessage());
        }
        return registered;
    }


    @Override
    public boolean isRegistered(ServiceInstance serviceInstance) {
        return isRegistered(buildId(serviceInstance));
    }

    /**
     * check all HealthIndicator is UP
     */
    private boolean checkHealthIndicators() {
        return ExtensionLoader.getExtensionLoader(HealthIndicator.class)
                .getSupportedExtensionInstances()
                .stream()
                .allMatch(hi -> HealthIndicator.Status.UP.equals(hi.health()));
    }

    public ServiceHealthCache newServiceHealthCache(String serviceName, List<String> tags) {
        int watchSeconds = getWatchSeconds();
        QueryOptions queryOptions = newDefaultQueryOptions(tags.toArray(new String[0]));
        return ServiceHealthCache.newCache(consul.healthClient(), serviceName, true, watchSeconds, queryOptions);
    }

    private QueryOptions newDefaultQueryOptions(String... tags) {
        ConsistencyMode consistencyMode = ConsistencyMode
                .createCachedConsistencyWithMaxAgeAndStale(Optional.of(CACHE_MAX_AGE), Optional.of(CACHE_MAX_STALE));
        return ImmutableQueryOptions.builder()
                .consistencyMode(consistencyMode).addTag(tags).build();
    }

    private String buildId(ServiceInstance serviceInstance) {
        return serviceInstance.getServiceName() + "-" + serviceInstance.getHost().replace(".", "-");
    }

    private List<String> buildTags(ServiceInstance serviceInstance, boolean firstCreate) {
        Map<String, String> instanceTags = serviceInstance.getTags();
        if (firstCreate) {
            // mock startTime
            instanceTags.put(START_TIME_KEY, String.valueOf(System.currentTimeMillis()));
        }
        return tagMap2List(instanceTags);
    }

    private Map<String, String> buildMetadata(ServiceInstance serviceInstance) {
        return serviceInstance.getMetadata();
    }

    private Registration.RegCheck buildCheck(ServiceInstance serviceInstance) {
        return ImmutableRegCheck
                .builder()
                .ttl(String.format("%ss", checkPassInterval))
                .deregisterCriticalServiceAfter(String.format("%sh", DEREGISTER_CRITICAL_SERVICE_AFTER_HOUR))
                .build();
    }

    private int getWatchSeconds() {
        return url.getParameter(POLLING_WAIT_SEC, DEFAULT_POLLING_WAIT_SEC);
    }

    public Consul getConsul() {
        return consul;
    }

    public void publishRegistrySuccessEvent(ServiceInstance instance, AppRunTimeEvent.EventType.RegistrySubEventType registryEventType) {
        AppRunTimeEvent<Map<String, Object>> registryEvent = AppRunTimeEvent.build(instance, registryEventType);
        registryEvent.getData().put(MEMO_KEY, SUCCESS);
        dispatcher.dispatch(registryEvent, false);
        updateRegistrySuccessFlag(registryEventType, true);
    }

    public void publishRegistryFailEvent(ServiceInstance instance, AppRunTimeEvent.EventType.RegistrySubEventType registryEventType, String exceptionMessage) {
        AppRunTimeEvent<Map<String, Object>> registryEvent = AppRunTimeEvent.build(instance, registryEventType);
        registryEvent.getData().put(MEMO_KEY, "false: " + exceptionMessage);
        dispatcher.dispatch(registryEvent, false);
        updateRegistrySuccessFlag(registryEventType, false);
    }

    /**
     * same as DiscoveryHealthIndicatorComponent listening event type
     */
    private void updateRegistrySuccessFlag(AppRunTimeEvent.EventType.RegistrySubEventType type, boolean success) {
        if (type.equals(AppRunTimeEvent.EventType.RegistrySubEventType.UP) || type.equals(AppRunTimeEvent.EventType.RegistrySubEventType.RECONNECTION)) {
            registrySuccessFlag.set(success);
        }
    }
}
