package cn.huolala.arch.hermes.discovery.consul;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.discovery.Discovery;
import cn.huolala.arch.hermes.discovery.core.AbstractDiscoveryFactory;

/**
 * Consul DiscoveryFactory
 *
 * @see ConsulDiscovery
 */
public class ConsulDiscoveryFactory extends AbstractDiscoveryFactory {
    @Override
    protected Discovery createDiscovery(URL discoveryUrl) {
        return new ConsulDiscovery(discoveryUrl);
    }
}
