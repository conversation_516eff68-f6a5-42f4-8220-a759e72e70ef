package cn.huolala.arch.hermes.discovery.consul;

import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.discovery.ServiceInstance;
import cn.huolala.arch.hermes.discovery.event.DiscoveryEvent;
import cn.huolala.arch.hermes.discovery.event.DiscoveryListener;
import com.orbitz.consul.cache.ConsulCache;
import com.orbitz.consul.cache.ServiceHealthCache;
import com.orbitz.consul.cache.ServiceHealthKey;
import com.orbitz.consul.model.health.ServiceHealth;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.stream.Collectors;

/**
 * Consul Listener
 *
 * @see ServiceHealthCache#addListener(ConsulCache.Listener)
 * @see ConsulCache.Listener
 */
public class ConsulHealthListener implements ConsulCache.Listener<ServiceHealthKey, ServiceHealth> {

    private static final Logger logger = LoggerFactory.getLogger(ConsulHealthListener.class);

    private final Set<DiscoveryListener> listeners = new CopyOnWriteArraySet<>();

    private final ConsulDiscovery discovery;

    private final String serviceName;

    private final ServiceHealthCache healthCache;

    private volatile DiscoveryEvent lastEvent;

    public ConsulHealthListener(ConsulDiscovery discovery, String serviceName, List<String> tags) {
        this.discovery = discovery;
        this.serviceName = serviceName;
        this.healthCache = discovery.newServiceHealthCache(serviceName, tags);
        this.healthCache.addListener(this);
        this.healthCache.start();
    }

    @Override
    public void notify(Map<ServiceHealthKey, ServiceHealth> newValues) {
        List<ServiceInstance> serviceInstances = newValues.values().stream()
                .map(discovery::buildServiceInstance)
                .filter(ServiceInstance::isValid)
                .collect(Collectors.toList());

        logger.info("Consul service health listener detected a service change event from agent for service [" + serviceName + "], size:[" + newValues.size() + "], ip "
                + serviceInstances.stream().map(v -> v.getHost() + ":" + v.getPort()).collect(Collectors.toList()));
        lastEvent = new DiscoveryEvent(serviceName, serviceInstances, discovery.type());
        listeners.forEach(listener -> listener.onEvent(lastEvent));
    }

    public void addListener(DiscoveryListener discoveryListener) {
        this.listeners.add(discoveryListener);

        // first times
        if (lastEvent != null) {
            discoveryListener.onEvent(lastEvent);
        }
    }

    public void removeListener(DiscoveryListener discoveryListener) {
        this.listeners.remove(discoveryListener);
    }

    public boolean hasInternalListener() {
        return listeners.size() > 0;
    }

    public void destroy() {
        if (healthCache != null) {
            healthCache.stop();
        }
    }
}
