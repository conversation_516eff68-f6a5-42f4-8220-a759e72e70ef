package cn.huolala.arch.hermes.discovery.consul;

import cn.huolala.arch.hermes.common.thread.NamedThreadFactory;
import cn.huolala.arch.hermes.discovery.ServiceInstance;
import com.orbitz.consul.AgentClient;
import com.orbitz.consul.model.agent.Registration;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * Consul Ttl Scheduler
 *
 * @see AgentClient#pass(String)
 */
public class ConsulTtlScheduler {
    private final Map<String, ScheduledFuture<?>> heartbeatFutures = new ConcurrentHashMap<>();

    private final ScheduledExecutorService scheduler = Executors
            .newSingleThreadScheduledExecutor(new NamedThreadFactory("Consul-TTL-check", true));

    private final ConsulDiscovery discovery;

    private final long checkInterval;

    public ConsulTtlScheduler(ConsulDiscovery discovery, long checkInterval) {
        this.discovery = discovery;
        this.checkInterval = checkInterval;
    }

    /**
     * add ttl task, the previous same ID task will be canceled
     */
    public void add(ServiceInstance serviceInstance, Registration registration) {
        ScheduledFuture<?> future = this.scheduler.scheduleAtFixedRate(() -> discovery.checkPass(serviceInstance, registration),
                checkInterval, checkInterval, TimeUnit.SECONDS);
        ScheduledFuture<?> prev = heartbeatFutures.put(registration.getId(), future);
        if (prev != null) {
            prev.cancel(true);
        }
    }

    public void remove(String id) {
        heartbeatFutures.computeIfPresent(id, (s, future) -> {
            future.cancel(true);
            return null;
        });
    }

    public void shutdown() {
        this.scheduler.shutdownNow();
    }
}
