<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.huolala.arch.hermes</groupId>
        <artifactId>hermes-distribution</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>hermes-bom</artifactId>
    <packaging>pom</packaging>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-spring-boot-autoconfigure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-spring-boot-actuator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-spring-boot-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-config-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-config-spring</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-configcenter-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-configcenter-apollo</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-discovery-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-discovery-consul</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-metadata-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-metrics-monitor</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-filter-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-filter-degrade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-filter-circuitbreaker</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-filter-ratelimit</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-filter-log</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-filter-validation</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-filter-timeout</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-filter-auth</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-cluster</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-protocol-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-protocol-grpc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-protocol-jsonrpc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-remoting-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-remoting-grpc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-remoting-http</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-serialization-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-serialization-jackson</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-all</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-api-grpc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-compatible-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-compatible</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-compatible-autoconfigure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-corruption</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-sink-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-router-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-router-az</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-router-gray</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-router-group</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-router-version</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-metrics-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.huolala.arch.hermes</groupId>
                <artifactId>hermes-spec-api</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
