<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.huolala.arch.hermes</groupId>
        <artifactId>hermes-parent</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>hermes-distribution</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>hermes-bom</module>
        <module>hermes-all</module>
        <module>hermes-api</module>
        <module>hermes-mini</module>
        <module>hermes-api-grpc</module>
        <module>hermes-api-grpc-parent</module>
    </modules>
</project>