package cn.huolala.arch.hermes.filter.auth;

import cn.huolala.arch.hermes.cluster.ClusterFilter;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.filter.auth.rule.AuthConsumerRule;
import cn.huolala.arch.hermes.filter.auth.rule.AuthProviderRule;
import cn.huolala.arch.hermes.filter.metadata.GovernanceMetadataSupplier;
import cn.huolala.arch.hermes.protocol.Filter;

import java.util.HashMap;
import java.util.Map;

import static cn.huolala.arch.hermes.common.constants.SpiConstants.FILTER_AUTH_CONSUMER;
import static cn.huolala.arch.hermes.common.constants.SpiConstants.FILTER_AUTH_PROVIDER;

public class AuthMetadataSupplier extends GovernanceMetadataSupplier {

    @Override
    public String getDesc() {
        return "服务治理-服务鉴权配置";
    }

    @Override
    public Map<String, Object> get() {

        Map<String, Object> metadataInfo = new HashMap<>();

        ClusterFilter consumerFilter = ExtensionLoader.getExtensionLoader(ClusterFilter.class).getExtension(FILTER_AUTH_CONSUMER);
        metadataInfo.putAll(getMetadata(AuthConsumerRule.class, consumerFilter));

        Filter providerFilter = ExtensionLoader.getExtensionLoader(Filter.class).getExtension(FILTER_AUTH_PROVIDER);
        metadataInfo.putAll(getMetadata(AuthProviderRule.class, providerFilter));

        return metadataInfo;
    }

}
