package cn.huolala.arch.hermes.filter.auth.filter;


import java.util.Optional;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigEvent;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigEventType;
import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.filter.auth.rule.AuthConsumerRule;
import cn.huolala.arch.hermes.filter.governance.AbstractGovernanceClusterFilter;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.FILTER_ORDER_AUTH;
import static cn.huolala.arch.hermes.common.constants.Constants.AUTH_TOKEN_ATTACHMENT;
import static cn.huolala.arch.hermes.common.constants.Constants.CONSUMER;

/**
 * The AuthConsumerFilter
 */
@Activate(group = CONSUMER, order = FILTER_ORDER_AUTH)
@ApiAudience.Private
public class AuthConsumerFilter extends AbstractGovernanceClusterFilter<AuthConsumerRule> {

    private static final String TOKEN_FIELD_KEY = "token";


    public AuthConsumerFilter() {
        super(false, true, tokenRuleGovernanceRule -> new AuthConsumerRule());
    }


    @Override
    protected Result doInvoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        String tokenConfigKey = String.format("%s@/%s/*", invocation.getRemoteApplication(), invocation.getServiceName());
        Optional.ofNullable(rule.getAppCommandRules().get(tokenConfigKey)).ifPresent(authConsumerRule -> invocation.getAttachments().put(AUTH_TOKEN_ATTACHMENT, authConsumerRule.getToken()));
        return invoker.invoke(invocation);
    }


    @Override
    protected boolean onCommandRuleChange(AuthConsumerRule commandRule, String commandKey, String fieldKey, ConfigEvent event) {
        if (TOKEN_FIELD_KEY.equals(fieldKey)) {
            if (event.getChangeType() == ConfigEventType.DELETED) {
                return true;
            }
            commandRule.setToken(event.getContent());
        }
        return false;
    }


    @Override
    protected String ruleKeyPrefix() {
        return "auth.consumer";
    }

}
