package cn.huolala.arch.hermes.filter.auth.filter;


import java.util.Objects;

import cn.huolala.arch.hermes.cluster.governance.GovernanceException;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigEvent;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigEventType;
import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.filter.auth.exception.RpcAuthenticationException;
import cn.huolala.arch.hermes.filter.auth.rule.AuthProviderRule;
import cn.huolala.arch.hermes.filter.governance.AbstractBlockedGovernanceFilter;
import cn.huolala.arch.hermes.common.event.MetricsEvent;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.InvokeMode;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.core.RpcResult;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.security.SignatureException;
import io.micrometer.core.instrument.Tags;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.FILTER_ORDER_AUTH;
import static cn.huolala.arch.hermes.common.constants.Constants.AUTH_TOKEN_ATTACHMENT;
import static cn.huolala.arch.hermes.common.constants.Constants.PROVIDER;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.LABEL_GRAY_VERSION;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.METRIC_PROVIDER_AUTH_CHECK_RESULT;
import static io.micrometer.core.instrument.Tags.of;

@Activate(group = PROVIDER, order = FILTER_ORDER_AUTH)
@ApiAudience.Private
public class AuthProviderFilter extends AbstractBlockedGovernanceFilter<AuthProviderRule> {

    private static final Logger logger = LoggerFactory.getLogger(AuthProviderFilter.class);

    private static final String SECRET_FIELD_KEY = "secretKey";


    public AuthProviderFilter() {
        super(false, true, rule -> new AuthProviderRule());
    }


    @Override
    public Result doInvoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        boolean permit = true;
        try {
            doGovern(invoker, invocation);
        } catch (GovernanceException governanceException) {
            permit = false;
            logger.info(governanceException.getMessage());
            if (invocation.getInvokeMode() == InvokeMode.FUTURE) {
                return RpcResult.newRpcResult(governanceException, invocation);
            } else {
                throw governanceException;
            }
        } finally {
            metricRecord(permit, String.format("/%s/*", invocation.getServiceName()), invocation.getCommandKey(), invocation.getRemoteApplication(), invocation.getGrayVersion());
        }
        return invoker.invoke(invocation);
    }


    /**
     * this result will be ignored, GovernanceException catch by method: doInvoke
     */
    @Override
    protected boolean doGovern(Invoker<?> invoker, Invocation invocation) throws RpcException, GovernanceException {
        if (!(rule.isGlobalEnable() && rule.isAppEnable() && StringUtils.isNotEmpty(invocation.getCommandKey()))) {
            return true;
        }
        String appId = invocation.getRemoteApplication();
        String blockMessagePrefix = "Blocked traffic by PROVIDER auth filter" + ", commandKey:[" + invocation.getCommandKey() + "], ";
        String commandRuleKey = String.format("/%s/*", invocation.getServiceName());
        AuthProviderRule authProviderRule = rule.getAppCommandRules().get(commandRuleKey);
        if (Objects.nonNull(authProviderRule) && authProviderRule.isEnabled()) {
            String token = invocation.getAttachmentString(AUTH_TOKEN_ATTACHMENT, StringUtils.EMPTY_STRING);
            if (StringUtils.isEmpty(token)) {
                throw new RpcAuthenticationException(blockMessagePrefix + "consumer: " + appId + " not authorized");
            } else {
                try {
                    Jws<Claims> claimsJws = Jwts.parserBuilder().setSigningKey(authProviderRule.getSecretKey()).build().parseClaimsJws(token);
                    if (!StringUtils.equals(claimsJws.getBody().getAudience(), appId)) {
                        throw new RpcAuthenticationException(blockMessagePrefix + "consumer: " + appId + "  not the token's owner");
                    }
                } catch (MalformedJwtException | SignatureException | ExpiredJwtException | UnsupportedJwtException | IllegalArgumentException e) {
                    throw new RpcAuthenticationException(blockMessagePrefix + "cause: " + e.getMessage());
                }
            }
        }
        return true;
    }


    @Override
    protected GovernanceException buildGovernanceException(Invoker<?> invoker, Invocation invocation) {
        return null;
    }


    @Override
    protected boolean onCommandRuleChange(AuthProviderRule commandRule, String commandKey, String fieldKey, ConfigEvent event) {
        if (SECRET_FIELD_KEY.equals(fieldKey)) {
            if (event.getChangeType() == ConfigEventType.DELETED) {
                return true;
            }
            commandRule.setSecretKey(event.getContent());
        }
        return false;
    }


    @Override
    protected String ruleKeyPrefix() {
        return "auth.provider";
    }


    private void metricRecord(boolean permit, String service, String command, String consumerAppId, String grayVersion) {
        eventDispatcher.dispatch(new MetricsEvent(meterRegistry -> {
            Tags tags = of("result", permit ? "1" : "0",
                    "service", service,
                    "command", command,
                    "auth_app_id", StringUtils.isNotBlank(consumerAppId) ? consumerAppId : "-",
                    LABEL_GRAY_VERSION, StringUtils.emptyIfNull(grayVersion));
            meterRegistry.counter(METRIC_PROVIDER_AUTH_CHECK_RESULT, tags).increment();
        }));
    }
}
