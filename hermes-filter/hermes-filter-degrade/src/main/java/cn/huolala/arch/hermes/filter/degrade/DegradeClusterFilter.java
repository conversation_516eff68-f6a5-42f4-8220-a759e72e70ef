package cn.huolala.arch.hermes.filter.degrade;

import cn.huolala.arch.hermes.cluster.governance.GovernanceException;
import cn.huolala.arch.hermes.common.event.MetricsEvent;
import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.CommandKeyUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.filter.governance.AbstractBlockedGovernanceClusterFilter;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.sink.common.util.SinkCollectionUtils;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import io.micrometer.core.instrument.Tags;

import java.util.ArrayList;
import java.util.List;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.FILTER_ORDER_DEGRADE;
import static cn.huolala.arch.hermes.common.constants.Constants.ALL;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.LABEL_GRAY_VERSION;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.METRIC_DEGRADER_BLOCKED_COUNT;
import static io.micrometer.core.instrument.Tags.of;

/**
 * Degrade ClusterFilter
 */
@Activate(order = FILTER_ORDER_DEGRADE)
@ApiAudience.Private
public class DegradeClusterFilter extends AbstractBlockedGovernanceClusterFilter<DegradeRule> {
    private static final Logger logger = LoggerFactory.getLogger(DegradeClusterFilter.class);

    public DegradeClusterFilter() {
        super(false, true, rule -> new DegradeRule());
    }

    @Override
    protected boolean doGovern(Invoker<?> invoker, Invocation invocation) throws GovernanceException {

        // 优先级：APP > APP CommandKey > Service CommandKey > Method CommandKey
        DegradeRule degradeRule = seekDegradeRule(invocation);

        boolean permit = degradeRule == null || !degradeRule.isEnabled();

        if (!permit) {
            String grayVersion = StringUtils.emptyIfNull(invocation.getGrayVersion());
            eventDispatcher.dispatch(new MetricsEvent(meterRegistry -> {
                Tags tags = of("command", invocation.getCommandKey(), LABEL_GRAY_VERSION, grayVersion);
                meterRegistry.counter(METRIC_DEGRADER_BLOCKED_COUNT, tags).increment();
            }));
        }

        return permit;
    }

    @Override
    protected GovernanceException buildGovernanceException(Invoker<?> invoker, Invocation invocation) {
        String commandKey = invocation.getCommandKey();

        String message = "Blocked invoke by consumer degrade filter, commandKey:[" + commandKey + "]";
        logger.warn(message);

        return new DegradeException(commandKey, message);
    }

    @Override
    protected String ruleKeyPrefix() {
        return "degrader";
    }


    private DegradeRule seekDegradeRule(Invocation invocation) {

        List<String> keys = new ArrayList<>();
        keys.add(CommandKeyUtils.buildInConsumerSide(ALL, ALL, ALL));
        keys.add(invocation.getAppCommandKey());
        keys.add(invocation.getServiceCommandKey());
        keys.add(invocation.getCommandKey());

        return SinkCollectionUtils.findFirst(rule.getAppCommandRules(), keys, commandRule -> commandRule.isEnabled());
    }

}
