package cn.huolala.arch.hermes.filter.degrade;

import cn.huolala.arch.hermes.cluster.ClusterFilter;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.filter.metadata.GovernanceMetadataSupplier;

import java.util.HashMap;
import java.util.Map;

import static cn.huolala.arch.hermes.common.constants.SpiConstants.FILTER_DEGRADE;

public class DegradeMetadataSupplier extends GovernanceMetadataSupplier {

    @Override
    public String getDesc() {
        return "服务治理-服务降级配置";
    }

    @Override
    public Map<String, Object> get() {
        Map<String, Object> metadataInfo = new HashMap<>();
        ClusterFilter clusterFilter = ExtensionLoader.getExtensionLoader(ClusterFilter.class).getExtension(FILTER_DEGRADE);
        metadataInfo.putAll(getMetadata("degradeRule", clusterFilter));
        return metadataInfo;
    }

}
