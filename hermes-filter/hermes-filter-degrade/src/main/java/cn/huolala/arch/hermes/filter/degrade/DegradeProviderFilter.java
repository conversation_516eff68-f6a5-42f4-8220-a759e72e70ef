package cn.huolala.arch.hermes.filter.degrade;

import cn.huolala.arch.hermes.cluster.governance.GovernanceException;
import cn.huolala.arch.hermes.common.event.MetricsEvent;
import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.filter.governance.AbstractBlockedGovernanceFilter;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.sink.common.util.SinkCollectionUtils;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;

import io.micrometer.core.instrument.Tags;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.FILTER_ORDER_DEGRADE;
import static cn.huolala.arch.hermes.common.constants.Constants.ALL;
import static cn.huolala.arch.hermes.common.constants.Constants.PROVIDER;
import static cn.huolala.arch.hermes.sink.common.util.SinkCommandKeyUtils.rebuildKeyInProviderSide;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.LABEL_GRAY_VERSION;
import static cn.huolala.arch.hermes.sink.common.constants.SinkMetricsConstants.METRIC_PROVIDER_DEGRADER_BLOCKED_COUNT;
import static io.micrometer.core.instrument.Tags.of;

@Activate(group = PROVIDER, order = FILTER_ORDER_DEGRADE)
@ApiAudience.Private
public class DegradeProviderFilter extends AbstractBlockedGovernanceFilter<DegradeRule> {

    private static final Logger logger = LoggerFactory.getLogger(DegradeProviderFilter.class);

    public DegradeProviderFilter() {
        super(false, true, rule -> new DegradeRule());
    }

    @Override
    protected boolean doGovern(Invoker<?> invoker, Invocation invocation) throws GovernanceException {

        DegradeRule degradeRule = seekDegradeRule(invocation);
        boolean permit = degradeRule == null || !degradeRule.isEnabled();
        if (!permit) {
            String grayVersion = StringUtils.emptyIfNull(invocation.getGrayVersion());
            String remoteAppId = Optional.ofNullable(invocation.getRemoteApplication()).orElse("-");
            eventDispatcher.dispatch(new MetricsEvent(meterRegistry -> {
                Tags tags = of("command", invocation.getCommandKey(),
                        "degrade_app_id", remoteAppId,
                        LABEL_GRAY_VERSION, grayVersion);
                meterRegistry.counter(METRIC_PROVIDER_DEGRADER_BLOCKED_COUNT, tags).increment();
            }));
        }

        return permit;
    }

    @Override
    protected GovernanceException buildGovernanceException(Invoker<?> invoker, Invocation invocation) {
        String commandKey = invocation.getCommandKey();

        String message = "Blocked invoke by provider degrade filter, commandKey:[" + commandKey + "]";
        logger.warn(message);

        return new DegradeException(commandKey, message);
    }

    @Override
    protected String ruleKeyPrefix() {
        return "degrader.provider";
    }

    /**
     * 优先级：
     * 1. APP CommandKey@*
     * 2. APP CommandKey@appId
     * 3. Service CommandKey@*
     * 4. Service CommandKey@appId
     * 5. Method CommandKey@*
     * 6. Method CommandKey@appId
     *
     * @param invocation
     * @return
     */
    private DegradeRule seekDegradeRule(Invocation invocation) {

        String appId = invocation.getRemoteApplication();

        List<String> keys = new ArrayList<>();
        keys.add(rebuildKeyInProviderSide(invocation.getAppCommandKey(), ALL));
        keys.add(rebuildKeyInProviderSide(invocation.getAppCommandKey(), appId));
        keys.add(rebuildKeyInProviderSide(invocation.getServiceCommandKey(), ALL));
        keys.add(rebuildKeyInProviderSide(invocation.getServiceCommandKey(), appId));
        keys.add(rebuildKeyInProviderSide(invocation.getCommandKey(), ALL));
        keys.add(rebuildKeyInProviderSide(invocation.getCommandKey(), appId));

        return SinkCollectionUtils.findFirst(rule.getAppCommandRules(), keys, commandRule -> commandRule.isEnabled());
    }

}
