package cn.huolala.arch.hermes.filter.ratelimit;

import cn.huolala.arch.hermes.cluster.governance.GovernanceException;
import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.common.util.CommandKeyUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.filter.ratelimit.support.AbstractRateLimitFilter;
import cn.huolala.arch.hermes.common.event.MetricsEvent;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.sink.common.util.SinkCollectionUtils;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;

import io.micrometer.core.instrument.Tags;

import java.util.ArrayList;
import java.util.List;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.FILTER_ORDER_RATE_LIMIT;
import static cn.huolala.arch.hermes.common.constants.Constants.ALL;
import static cn.huolala.arch.hermes.common.constants.Constants.CONSUMER;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.LABEL_GRAY_VERSION;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.METRIC_RATELIMIT_CHECK_RESULT;
import static io.micrometer.core.instrument.Tags.of;

/**
 * RateLimit Consumer Filter()
 */
@Activate(group = CONSUMER, order = FILTER_ORDER_RATE_LIMIT)
@ApiAudience.Private
public class RateLimitConsumerFilter extends AbstractRateLimitFilter {

    @Override
    protected boolean doGovern(Invoker<?> invoker, Invocation invocation) throws GovernanceException {

        boolean permit = true;

        // 优先级：APP > APP CommandKey > Service CommandKey > Method CommandKey
        List<RateLimitRule> rateLimitRules = seekRateLimitRule( invocation);

        for (RateLimitRule rateLimitRule : rateLimitRules) {
            permit = rateLimitRule.rateLimiter().tryAcquire();
            if (!permit) {
                break;
            }
        }

        boolean finalPermit = permit;
        String grayVersion = StringUtils.emptyIfNull(invocation.getGrayVersion());

        eventDispatcher.dispatch(new MetricsEvent(meterRegistry -> {
            Tags tags = of("command", invocation.getCommandKey(), "result", finalPermit ? "1" : "0",
                    LABEL_GRAY_VERSION, grayVersion);
            meterRegistry.counter(METRIC_RATELIMIT_CHECK_RESULT, tags).increment();
        }));

        return finalPermit;
    }

    @Override
    protected String ruleKeyPrefix() {
        return "ratelimit";
    }

    @Override
    protected String rateLimitMessage() {
        return "Blocked traffic by CONSUMER rate limit filter";
    }

    private List<RateLimitRule> seekRateLimitRule(Invocation invocation) {

        // 优先级：APP > APP CommandKey > Service CommandKey > Method CommandKey
        List<String> keys = new ArrayList<>();
        keys.add(CommandKeyUtils.buildInConsumerSide(ALL, ALL, ALL));
        keys.add(invocation.getAppCommandKey());
        keys.add(invocation.getServiceCommandKey());
        keys.add(invocation.getCommandKey());

        return SinkCollectionUtils.filterByKeyOrder(rule.getAppCommandRules(),keys);
    }
}
