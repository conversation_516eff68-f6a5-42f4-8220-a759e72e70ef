package cn.huolala.arch.hermes.filter.ratelimit;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.FILTER_ORDER_RATE_LIMIT;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import cn.huolala.arch.hermes.cluster.governance.GovernanceException;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.common.tool.Pair;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.filter.ratelimit.support.AbstractRateLimitFilter;
import cn.huolala.arch.hermes.common.event.MetricsEvent;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import io.micrometer.core.instrument.Tag;

import static cn.huolala.arch.hermes.common.constants.Constants.ALL;
import static cn.huolala.arch.hermes.common.constants.Constants.PROVIDER;
import static cn.huolala.arch.hermes.sink.common.util.SinkCommandKeyUtils.rebuildKeyInProviderSide;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.LABEL_GRAY_VERSION;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.METRIC_PROVIDER_RATELIMIT_CHECK_RESULT;
import static io.micrometer.core.instrument.Tag.of;

/**
 * RateLimit Provider Filter
 */
@Activate(group = PROVIDER, order = FILTER_ORDER_RATE_LIMIT)
@ApiAudience.Private
public class RateLimitProviderFilter extends AbstractRateLimitFilter {

    @Override
    @SuppressWarnings("unchecked")
    protected boolean doGovern(Invoker<?> invoker, Invocation invocation) throws GovernanceException {
        String appId = invocation.getRemoteApplication();
        return tryAcquire(invocation,
                Pair.of(invocation.getAppCommandKey(), ALL),
                Pair.of(invocation.getAppCommandKey(), appId),
                Pair.of(invocation.getServiceCommandKey(), ALL),
                Pair.of(invocation.getServiceCommandKey(), appId),
                Pair.of(invocation.getCommandKey(), ALL),
                Pair.of(invocation.getCommandKey(), appId));
    }
    
    /**
     * Acquire permit，Priority：Method > Service > App > Global
     */
    @SuppressWarnings("unchecked")
    private boolean tryAcquire(Invocation invocation, Pair<String, String>... pairs) {
        String appId = invocation.getRemoteApplication();
        boolean permit = true;
        Pair<String, String> matchedPair = null;
        for (Pair<String, String> pair : pairs) {
            matchedPair = pair;
            if (StringUtils.isAnyEmpty(appId, pair.getRight())) {
                continue;
            }
            permit = Optional.of(rebuildKeyInProviderSide(pair.getLeft(), pair.getRight()))
                    .map(key -> this.rule.getAppCommandRules().get(key))
                    .map(rateLimitRule -> rateLimitRule.rateLimiter())
                    .map(RateLimiter::tryAcquire)
                    .orElse(true);
            if (!permit) {
                break;
            }

        }

        if(null != matchedPair) {
            dispatchMetricEvent(appId, matchedPair.getLeft(), matchedPair.getRight(), permit, invocation.getGrayVersion());
        }

        return permit;
    }

    private void dispatchMetricEvent(String limitAppId, String commandKey, String type, boolean permit, String grayVersion) {
        eventDispatcher.dispatch(new MetricsEvent(meterRegistry -> {
            List<Tag> tags = Arrays.asList(
                    of("service_key", commandKey), 
                    of("result", permit ? "1" : "0"),
                    of("limit_type", ALL.equals(type) ? ALL : "appId"),
                    of("limit_app_id", StringUtils.isNotBlank(limitAppId) ? limitAppId : "-"),
                    of(LABEL_GRAY_VERSION, StringUtils.emptyIfNull(grayVersion))
            );
            meterRegistry.counter(METRIC_PROVIDER_RATELIMIT_CHECK_RESULT, tags).increment();
        }));
    }

    @Override
    protected String ruleKeyPrefix() {
        return "ratelimit.provider";
    }

    @Override
    protected String rateLimitMessage() {
        return "Blocked traffic by PROVIDER rate limit filter";
    }
}