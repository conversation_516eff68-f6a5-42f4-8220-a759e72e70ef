package cn.huolala.arch.hermes.filter.ratelimit;

import cn.huolala.arch.hermes.filter.governance.GovernanceCommandRule;

/**
 * RateLimit Rule
 */
public class RateLimitRule extends GovernanceCommandRule {
    public static final Byte ENABLE_STATUS = 1;

    /**
     * command key
     */
    private String name;

    /**
     * version
     */
    private String version;

    /**
     * 1:enable/0:disable
     */
    private Byte status = ENABLE_STATUS;

    /**
     * consumer/provider
     */
    private String limitSource;

    /**
     * LEAKY_BUCKET/TOKEN_BUCKET
     *
     * @see LimitType
     */
    private String limitType;

    private String limitRet;

    private Integer limitCount;

    private String limitMsg;

    private Integer tokenBucketCount;

    /**
     * RateLimiter
     */
    private transient volatile RateLimiter rateLimiter;

    /**
     * rebuildRateLimiter if null
     */
    public RateLimiter rateLimiter() {
        if (rateLimiter == null) {
            rebuildRateLimiter();
        }
        return rateLimiter;
    }

    /**
     * new or rebuild
     */
    public synchronized void rebuildRateLimiter() {
        RateLimiter old = this.rateLimiter;
        this.rateLimiter = RateLimiter.of(this);
        if (old != null) {
            old.destroy();
        }
    }

    @Override
    public void destroy() {
        super.destroy();

        if (rateLimiter != null) {
            rateLimiter.destroy();
        }
    }

    @Override
    public void copyOf(Object newRule) {
        super.copyOf(newRule);

        if (newRule instanceof RateLimitRule) {
            RateLimitRule rateLimitRule = (RateLimitRule) newRule;

            this.name = rateLimitRule.name;
            this.status = rateLimitRule.status;
            this.version = rateLimitRule.version;
            this.limitSource = rateLimitRule.limitSource;
            this.limitType = rateLimitRule.limitType;
            this.limitRet = rateLimitRule.limitRet;
            this.limitCount = rateLimitRule.limitCount;
            this.limitMsg = rateLimitRule.limitMsg;
            this.tokenBucketCount = rateLimitRule.tokenBucketCount;
        }
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getLimitSource() {
        return limitSource;
    }

    public void setLimitSource(String limitSource) {
        this.limitSource = limitSource;
    }

    public String getLimitType() {
        return limitType;
    }

    public void setLimitType(String limitType) {
        this.limitType = limitType;
    }

    public String getLimitRet() {
        return limitRet;
    }

    public void setLimitRet(String limitRet) {
        this.limitRet = limitRet;
    }

    public Integer getLimitCount() {
        return limitCount;
    }

    public void setLimitCount(Integer limitCount) {
        this.limitCount = limitCount;
    }

    public String getLimitMsg() {
        return limitMsg;
    }

    public void setLimitMsg(String limitMsg) {
        this.limitMsg = limitMsg;
    }

    public Integer getTokenBucketCount() {
        return tokenBucketCount;
    }

    public void setTokenBucketCount(Integer tokenBucketCount) {
        this.tokenBucketCount = tokenBucketCount;
    }

    /**
     * LimitType
     **/
    public enum LimitType {
        TOKEN_BUCKET, LEAKY_BUCKET
    }

}
