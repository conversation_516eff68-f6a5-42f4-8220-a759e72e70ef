package cn.huolala.arch.hermes.filter.ratelimit;

import cn.huolala.arch.hermes.filter.ratelimit.limiter.LeakyBucketRateLimiter;
import cn.huolala.arch.hermes.filter.ratelimit.limiter.TokenBucketRateLimiter;

import static cn.huolala.arch.hermes.filter.ratelimit.RateLimitRule.LimitType.TOKEN_BUCKET;

/**
 * RateLimiter
 */
public interface RateLimiter {
    /**
     * Acquires a permit from this {@link RateLimiter} if it can be acquired immediately
     */
    boolean tryAcquire();

    /**
     * destroy {@link RateLimiter}
     */
    default void destroy() {
    }

    static RateLimiter of(RateLimitRule rule) {
        try {
            if (TOKEN_BUCKET.name().equalsIgnoreCase(rule.getLimitType())) {
                return new TokenBucketRateLimiter(rule);
            }

            // default
            return new LeakyBucketRateLimiter(rule);
        } catch (Exception e) {
            throw new IllegalArgumentException(e);
        }
    }
}
