package cn.huolala.arch.hermes.filter.ratelimit;

import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.filter.metadata.GovernanceMetadataSupplier;
import cn.huolala.arch.hermes.protocol.Filter;

import java.util.HashMap;
import java.util.Map;

import static cn.huolala.arch.hermes.common.constants.SpiConstants.FILTER_RATELIMIT_CONSUMER;
import static cn.huolala.arch.hermes.common.constants.SpiConstants.FILTER_RATELIMIT_PROVIDER;

public class RatelimitMetadataSupplier extends GovernanceMetadataSupplier {

    @Override
    public String getDesc() {
        return "服务治理-服务限流配置";
    }

    @Override
    public Map<String, Object> get() {
        Map<String, Object> metadataInfo = new HashMap<>();

        Filter rateLimitConsumerFilter = ExtensionLoader.getExtensionLoader(Filter.class).getExtension(FILTER_RATELIMIT_CONSUMER);
        metadataInfo.putAll(getMetadata("rateLimitConsumerRule", rateLimitConsumerFilter));

        Filter rateLimitProviderFilter = ExtensionLoader.getExtensionLoader(Filter.class).getExtension(FILTER_RATELIMIT_PROVIDER);
        metadataInfo.putAll(getMetadata("rateLimitProviderRule", rateLimitProviderFilter));

        return metadataInfo;
    }

}
