package cn.huolala.arch.hermes.filter.ratelimit.limiter;

import cn.huolala.arch.hermes.filter.ratelimit.RateLimitRule;
import cn.huolala.arch.hermes.filter.ratelimit.support.AbstractRateLimiter;

/**
 * LeakyBucket RateLimiter
 */
public class LeakyBucketRateLimiter extends AbstractRateLimiter {
    /**
     * The wallet rate
     */
    private final double rate;

    /**
     * The bucket capacity
     */
    private final long burst;

    /**
     * The last refresh time
     */
    private long refreshTime;

    /**
     * water
     */
    private long water;

    public LeakyBucketRateLimiter(RateLimitRule rule) {
        super(rule);

        this.rate = Double.parseDouble(rule.getLimitRet());
        this.burst = rule.getLimitCount();

        this.water = 0;
        this.refreshTime = System.currentTimeMillis();
    }

    /**
     * refresh water and time
     */
    private void refresh() {
        long now = System.currentTimeMillis();

        double leakyWater = (now - refreshTime) * 1.0 * rate / 1000;
        water = (long) Math.max(0, water - leakyWater);

        refreshTime = now;
    }


    @Override
    protected boolean doTryAcquire() {
        refresh();
        return water++ < burst;
    }
}
