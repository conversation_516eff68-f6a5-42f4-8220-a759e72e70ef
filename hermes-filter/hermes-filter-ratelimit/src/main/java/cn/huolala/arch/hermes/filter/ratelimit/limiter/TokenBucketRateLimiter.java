package cn.huolala.arch.hermes.filter.ratelimit.limiter;

import cn.huolala.arch.hermes.filter.ratelimit.RateLimitRule;
import cn.huolala.arch.hermes.filter.ratelimit.support.AbstractRateLimiter;
import com.google.common.util.concurrent.RateLimiter;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * TokenBucket RateLimiter via guava {@link RateLimiter}
 */
@SuppressWarnings("UnstableApiUsage")
public class TokenBucketRateLimiter extends AbstractRateLimiter {
    private static final int WARMUP_MILLIS = 1000;

    private volatile RateLimiter rateLimiter;

    private volatile boolean warmup;
    private AtomicInteger warmupCount;
    private volatile long warmupToMillis;

    public TokenBucketRateLimiter(RateLimitRule rule) {
        super(rule);
    }

    @Override
    protected boolean doTryAcquire() {
        if (rateLimiter == null) {
            synchronized (this) {
                if (rateLimiter == null) {
                    rateLimiter = RateLimiter.create(rule.getTokenBucketCount());
                    warmup = true;
                    warmupCount = new AtomicInteger(rule.getTokenBucketCount());
                    warmupToMillis = System.currentTimeMillis() + WARMUP_MILLIS;
                }
            }
        }
        return warmupPeriod() || rateLimiter.tryAcquire();
    }

    private boolean warmupPeriod() {
        if (warmup) {
            warmup = System.currentTimeMillis() <= warmupToMillis
                    && warmupCount != null && warmupCount.decrementAndGet() > 0;
        }
        return warmup;
    }
}
