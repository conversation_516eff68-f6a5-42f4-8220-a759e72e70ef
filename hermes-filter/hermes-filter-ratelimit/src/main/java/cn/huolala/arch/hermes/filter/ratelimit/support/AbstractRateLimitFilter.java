package cn.huolala.arch.hermes.filter.ratelimit.support;

import cn.huolala.arch.hermes.cluster.governance.GovernanceException;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigEvent;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigEventType;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.JsonUtils;
import cn.huolala.arch.hermes.filter.governance.AbstractBlockedGovernanceFilter;
import cn.huolala.arch.hermes.filter.ratelimit.RateLimitException;
import cn.huolala.arch.hermes.filter.ratelimit.RateLimitRule;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;

/**
 * Abstract RateLimit Filter
 */
public abstract class AbstractRateLimitFilter extends AbstractBlockedGovernanceFilter<RateLimitRule> {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    public static final String RULE_FIELD_KEY = "rule";

    public AbstractRateLimitFilter() {
        super(false, true, rule -> new RateLimitRule());
    }

    @Override
    protected GovernanceException buildGovernanceException(Invoker<?> invoker, Invocation invocation) {
        String commandKey = invocation.getCommandKey();
        String message = rateLimitMessage() + ", commandKey:[" + commandKey + "]";
        logger.info(message);

        return new RateLimitException(commandKey, message);
    }

    @Override
    protected boolean onCommandRuleChange(RateLimitRule commandRule, String commandKey, String fieldKey, ConfigEvent event) {
        if (RULE_FIELD_KEY.equals(fieldKey)) {
            if (event.getChangeType() == ConfigEventType.DELETED) {
                return true;
            } else {
                try {
                    RateLimitRule newCommandRule = JsonUtils.parse(event.getContent(), RateLimitRule.class);
                    commandRule.copyOf(newCommandRule);
                    commandRule.rebuildRateLimiter();
                } catch (Exception e) {
                    logger.warn(getClass().getSimpleName() + "Rate limit config is not a valid JSON string", e);
                }
            }
        }

        return false;
    }

    protected abstract String rateLimitMessage();
}
