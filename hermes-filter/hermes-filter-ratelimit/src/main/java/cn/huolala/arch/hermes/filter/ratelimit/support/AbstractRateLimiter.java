package cn.huolala.arch.hermes.filter.ratelimit.support;

import cn.huolala.arch.hermes.filter.ratelimit.RateLimitRule;
import cn.huolala.arch.hermes.filter.ratelimit.RateLimiter;

import java.util.concurrent.atomic.AtomicBoolean;

import static cn.huolala.arch.hermes.filter.ratelimit.RateLimitRule.ENABLE_STATUS;

/**
 * Abstract RateLimiter
 */
public abstract class AbstractRateLimiter implements RateLimiter {
    protected final AtomicBoolean enable;

    protected final RateLimitRule rule;

    public AbstractRateLimiter(RateLimitRule rule) {
        this.rule = rule;

        this.enable = new AtomicBoolean(ENABLE_STATUS.equals(rule.getStatus()));
    }

    @Override
    public boolean tryAcquire() {
        return !enable.get() || doTryAcquire();
    }

    protected abstract boolean doTryAcquire();
}
