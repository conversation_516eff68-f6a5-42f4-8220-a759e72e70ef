package cn.huolala.arch.hermes.filter.ratelimit.limiter;

import cn.huolala.arch.hermes.filter.ratelimit.RateLimitRule;
import cn.huolala.arch.hermes.filter.ratelimit.support.AbstractRateLimiter;
import org.junit.jupiter.api.Test;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class TokenBucketRateLimiterTest {

    @Test
    void tryAcquire() throws InterruptedException {
        RateLimitRule rule = new RateLimitRule();
        rule.setEnabled(true);
        rule.setTokenBucketCount(200);
        TokenBucketRateLimiter limiter = new TokenBucketRateLimiter(rule);

        assertEquals(190, testTryAcquire(limiter, 190));
        int pass = testTryAcquire(limiter, 100);
        assertTrue(pass >= 5 && pass <= 15);

        for (int i = 0; i < 2; i++) {
            TimeUnit.MILLISECONDS.sleep(1000);
            pass = testTryAcquire(limiter, 500);
            assertTrue(pass >= 195 && pass <= 205);
        }
    }

    private int testTryAcquire(AbstractRateLimiter limiter, int count) {
        int pass = 0;
        for (int i = 1; i <= count; i++) {
            if (limiter.tryAcquire()) {
                pass++;
            }
        }
        return pass;
    }
}