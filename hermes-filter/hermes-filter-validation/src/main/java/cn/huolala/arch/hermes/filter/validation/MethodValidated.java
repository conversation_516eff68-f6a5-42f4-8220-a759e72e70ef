package cn.huolala.arch.hermes.filter.validation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Method grouping validation.
 * <p>
 * Scenario: this annotation can be used on interface's method when need to check against group before invoke the method
 * For example: <pre> @MethodValidated({Save.class, Update.class})
 * void relatedQuery(ValidationParameter parameter);
 * </pre>
 * It means both Save group and Update group are needed to check when method relatedQuery is invoked.
 * </p>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MethodValidated {
    Class<?>[] value() default {};
}
