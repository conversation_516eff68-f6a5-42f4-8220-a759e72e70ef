package cn.huolala.arch.hermes.filter.validation.filter;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.common.extension.Inject;
import cn.huolala.arch.hermes.common.util.ConfigUtils;
import cn.huolala.arch.hermes.filter.validation.Validation;
import cn.huolala.arch.hermes.filter.validation.Validator;
import cn.huolala.arch.hermes.filter.validation.support.AbstractValidation;
import cn.huolala.arch.hermes.filter.validation.support.jvalidation.JValidator;
import cn.huolala.arch.hermes.protocol.Filter;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.core.RpcResult;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

import javax.validation.ValidationException;

import static cn.huolala.arch.hermes.cluster.ClusterConstants.FILTER_ORDER_VALID;
import static cn.huolala.arch.hermes.common.constants.Constants.CONSUMER;
import static cn.huolala.arch.hermes.common.constants.Constants.PROVIDER;
import static cn.huolala.arch.hermes.common.constants.SpiConstants.VALIDATION_DEFAULT;

/**
 * ValidationFilter invoke the validation by finding the right {@link Validator} instance based on the
 * configured <b>validation</b> attribute value of invoker url before the actual method invocation.
 *
 * <pre>
 *     e.g. &lt;hermes:method name="save" validation="jvalidation" /&gt;
 *     In the above configuration a validation has been configured of type jvalidation. On invocation of method <b>save</b>
 *     hermes will invoke {@link JValidator}
 * </pre>
 *
 * To add a new type of validation
 * <pre>
 *     e.g. &lt;hermes:method name="save" validation="special" /&gt;
 *     where "special" is representing a validator for special character.
 * </pre>
 *
 * developer needs to do
 * <br/>
 * 1)Implement a SpecialValidation.java class (package name xxx.yyy.zzz) either by implementing {@link Validation} or extending {@link AbstractValidation} <br/>
 * 2)Implement a SpecialValidator.java class (package name xxx.yyy.zzz) <br/>
 * 3)Add an entry <b>special</b>=<b>xxx.yyy.zzz.SpecialValidation</b> under <b>META-INF folders cn.huolala.arch.hermes.hermes.filter.validation.Validation file</b>.
 *
 * @see Validation
 * @see Validator
 * @see Filter
 * @see AbstractValidation
 */

@Activate(group = {CONSUMER, PROVIDER}, value = VALIDATION_DEFAULT, order = FILTER_ORDER_VALID)
@ApiAudience.Private
public class ValidationFilter implements Filter {

    private Validation validation;

    /**
     * Sets the validation instance for ValidationFilter
     * @param validation Validation instance injected by hermes framework based on "validation" attribute value.
     */
    @Inject(optional = true)
    public void setValidation(Validation validation) {
        this.validation = validation;
    }

    /**
     * Perform the validation of before invoking the actual method based on <b>validation</b> attribute value.
     * @param invoker    service
     * @param invocation invocation.
     * @return Method invocation result
     * @throws RpcException Throws RpcException if  validation failed or any other runtime exception occurred.
     */
    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        if (validation != null && !invocation.getMethodName().startsWith("$")
                && ConfigUtils.isNotEmpty(invoker.getUrl().getMethodParameter(invocation.getMethodName(), VALIDATION_DEFAULT))) {
            try {
                Validator validator = validation.getValidator(invoker.getUrl());
                if (validator != null) {
                    validator.validate(invocation.getMethodName(), invocation.getParameterTypes(), invocation.getArguments());
                }
            } catch (RpcException e) {
                throw e;
            } catch (ValidationException e) {
                // only use exception's message to avoid potential serialization issue
                return RpcResult.newRpcResult(new ValidationException(e.getMessage()), invocation);
            } catch (Throwable t) {
                return RpcResult.newRpcResult(t, invocation);
            }
        }
        return invoker.invoke(invocation);
    }

}
