package cn.huolala.arch.hermes.filter.validation.support;


import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.filter.validation.Validation;
import cn.huolala.arch.hermes.filter.validation.Validator;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * AbstractValidation is abstract class for Validation interface. It helps low level Validation implementation classes
 * by performing common task e.g. key formation, storing instance of validation class to avoid creation of unnecessary
 * copy of validation instance and faster execution.
 *
 * @see Validation
 * @see Validator
 */
public abstract class AbstractValidation implements Validation {

    private final ConcurrentMap<String, Validator> validators = new ConcurrentHashMap<>();

    @Override
    public Validator getValidator(URL url) {
        String key = url.toFullString();
        Validator validator = validators.get(key);
        if (validator == null) {
            validators.put(key, createValidator(url));
            validator = validators.get(key);
        }
        return validator;
    }

    protected abstract Validator createValidator(URL url);

}
