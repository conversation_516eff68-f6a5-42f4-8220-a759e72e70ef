package cn.huolala.arch.hermes.filter.validation.support.jvalidation;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.filter.validation.support.AbstractValidation;
import cn.huolala.arch.hermes.filter.validation.Validator;

/**
 * Creates a new instance of {@link Validator} using input argument url.
 * @see AbstractValidation
 * @see Validator
 */
public class JValidation extends AbstractValidation {

    /**
     * Return new instance of {@link JValidator}
     * @param url Valid URL instance
     * @return Instance of JValidator
     */
    @Override
    protected Validator createValidator(URL url) {
        return new JValidator(url);
    }

}