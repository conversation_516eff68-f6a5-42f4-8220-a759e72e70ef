package cn.huolala.arch.hermes.protocol;

import cn.huolala.arch.hermes.protocol.exception.RpcException;

/**
 * Filter: Extension for intercepting the invocation<br/>
 * <pre>
 *                                           -> Filter -> Invoker
 *
 *  Proxy -> ClusterFilter -> ClusterInvoker -> Filter -> Invoker
 *
 *                                           -> Filter -> Invoker
 * </pre>
 * @see Filter
 */
public interface BaseFilter {
    /**
     * Make sure call invoker.invoke() in your implementation.
     */
    Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException;

    /** Filter Listener **/
    interface Listener {
        void onResult(Response result, Invoker<?> invoker, Invocation invocation);
        void onError(Throwable err, Invoker<?> invoker, Invocation invocation);
    }
}
