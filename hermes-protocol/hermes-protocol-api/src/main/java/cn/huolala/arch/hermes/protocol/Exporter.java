package cn.huolala.arch.hermes.protocol;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;

/**
 * Exporter
 *
 * @param <T> service interface type
 * @see Protocol#export(Invoker)
 */
@ApiAudience.Private
public interface Exporter<T> {
    /**
     * get invoker.
     * @return invoker
     */
    Invoker<T> getInvoker();

    /**
     * unExport<br/>
     * <code>
     * getInvoker().destroy();
     * </code>
     */
    void unExport();
}
