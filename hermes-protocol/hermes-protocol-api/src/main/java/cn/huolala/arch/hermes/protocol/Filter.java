package cn.huolala.arch.hermes.protocol;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.spec.classification.ApiStability;
import cn.huolala.arch.hermes.common.extension.SPI;

/**
 * Filter<br/>
 * Extension for intercepting the invocation for both service provider and consumer
 */
@SPI(internal = false)
@ApiAudience.Public
@ApiStability.Stable
public interface Filter extends BaseFilter {
}
