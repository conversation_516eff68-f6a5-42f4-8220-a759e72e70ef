package cn.huolala.arch.hermes.protocol;

import cn.huolala.arch.hermes.common.logger.Level;
import cn.huolala.arch.hermes.common.support.function.ThrowableSupplier;
import cn.huolala.arch.hermes.common.util.CollectionUtils;

import java.lang.reflect.Type;
import java.util.Map;
import java.util.stream.Stream;

/**
 * Invocation
 *
 * @see Invoker#invoke(Invocation)
 * @see cn.huolala.arch.hermes.protocol.core.RpcInvocation
 */
public interface Invocation {
    /**
     * get the invoker in current context.
     */
    Invoker<?> getInvoker();

    void setInvoker(Invoker<?> invoker);

    InvokeMode getInvokeMode();

    void setInvokeMode(InvokeMode invokeMode);

    String getProtocolServiceKey();

    /**
     * get remote application name(app id)
     */
    String getRemoteApplication();

    /**
     * set remote application name(app id)
     */
    void setRemoteApplication(String remoteApplication);

    /**
     * get service CommandKey
     */
    String getCommandKey();

    /**
     * get service command key
     */
    String getServiceCommandKey();

    /**
     * get app command key
     */
    String getAppCommandKey();

    /**
     * get the interface name.
     */
    String getServiceName();

    /**
     * get version<br/>
     * <ul>
     *     <li>provider: service version</li>
     *     <li>consumer: remote ref service version</li>
     * </ul>
     */
    String getVersion();

    /**
     * get gray version
     */
    String getGrayVersion();

    /**
     * get method name.
     */
    String getMethodName();

    /**
     * get parameter types.
     */
    Class<?>[] getParameterTypes();

    /**
     * get parameter types desc.
     */
    String getParameterTypesDesc();

    /**
     * get parameter's signature, string representation of parameter types.
     *
     * @return parameter's signature
     * @see #getParameterTypes()
     */
    default String[] getCompatibleParamSignatures() {
        return Stream.of(getParameterTypes())
                .map(Class::getName)
                .toArray(String[]::new);
    }

    /**
     * get arguments.
     */
    Object[] getArguments();

    Class<?> getReturnType();

    Type[] getReturnTypes();

    void setTimeout(Integer timeout);

    Integer getTimeout();

    void setConnectionTimeout(Integer connectionTimeout);

    Integer getConnectionTimeout();

    Map<Object, Object> getAttributes();

    Object put(Object key, Object value);

    Object putIfAbsent(Object key, Object value);

    Object get(Object key);

    Object get(String key, Object defaultValue);

    /**
     * value instanceof String
     **/
    default String getString(String key) {
        return this.getString(key, null);
    }

    /**
     * value instanceof String
     **/
    String getString(String key, String defaultValue);

    Map<String, Object> getAttachments();

    Map<String, Object> getRawAttachments();

    default void putAttachment(String key, Object value) {
        getAttachments().put(key, value);
    }

    default void putAttachments(Map<String, Object> attachments) {
        if (CollectionUtils.isEmptyMap(attachments)) {
            return;
        }

        attachments.forEach(this::putAttachment);
    }

    default void putAttachmentIfAbsent(String key, Object value) {
        getAttachments().putIfAbsent(key, value);
    }

    default void putAttachmentsIfAbsent(Map<String, Object> attachments) {
        if (CollectionUtils.isEmptyMap(attachments)) {
            return;
        }

        attachments.forEach(this::putAttachmentIfAbsent);
    }

    void putRawAttachment(String key, Object value);

    default void putRawAttachments(Map<String, Object> rawAttachments) {
        if (CollectionUtils.isEmptyMap(rawAttachments)) {
            return;
        }
        rawAttachments.forEach(this::putRawAttachment);
    }

    void putRawAttachmentIfAbsent(String key, Object value);

    default void putRawAttachmentsIfAbsent(Map<String, Object> rawAttachments) {
        if (CollectionUtils.isEmptyMap(rawAttachments)) {
            return;
        }
        rawAttachments.forEach(this::putRawAttachmentIfAbsent);
    }

    default Object getAttachment(String key) {
        return getAttachments().get(key);
    }

    default Object getAttachment(String key, Object defaultValue) {
        return getAttachments().getOrDefault(key, defaultValue);
    }

    /**
     * value instanceof String
     **/
    default String getAttachmentString(String key) {
        return this.getAttachmentString(key, null);
    }

    /**
     * value instanceof String
     **/
    String getAttachmentString(String key, String defaultValue);

    default Object getRawAttachment(String key) {
        return getRawAttachment(key, null);
    }

    Object getRawAttachment(String key, Object defaultValue);

    /**
     * value instanceof String
     **/
    default String getRawAttachmentString(String key) {
        return this.getRawAttachmentString(key, null);
    }

    /**
     * value instanceof String
     **/
    String getRawAttachmentString(String key, String defaultValue);

    void putDMCLog(String key, ThrowableSupplier<Object> value, Level level);

    void putDMCLog(String key, Object value);

    Map<String, Object> getBaseMDCLogs();

    Map<String, Object> getLevelMDCLogs();

    /**
     * is provider side.
     *
     * @return provider side.
     */
    boolean isProviderSide();

    /**
     * is consumer side.
     *
     * @return consumer side.
     */
    boolean isConsumerSide();

}
