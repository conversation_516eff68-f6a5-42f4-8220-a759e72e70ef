package cn.huolala.arch.hermes.protocol;

import cn.huolala.arch.hermes.common.Node;
import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

/**
 * Invoker
 * @param <T> service interface type
 * @see Protocol#refer(Class, URL)
 */
public interface Invoker<T> extends Node {

    /**
     * get service interface.
     *
     * @return service interface.
     */
    Class<T> getInterface();

    /**
     * invoke.
     *
     * @param invocation invocation
     * @return result
     * @throws RpcException Rpc Exception
     */
    Result invoke(Invocation invocation) throws RpcException;
}
