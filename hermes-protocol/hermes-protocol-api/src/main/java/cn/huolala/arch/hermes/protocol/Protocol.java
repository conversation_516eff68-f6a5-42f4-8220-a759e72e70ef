package cn.huolala.arch.hermes.protocol;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.extension.Adaptive;
import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

import java.util.Collections;
import java.util.List;

import static cn.huolala.arch.hermes.common.constants.SpiConstants.PROTOCOL_DEFAULT;

/**
 * RPC Protocol
 */
@SPI(PROTOCOL_DEFAULT)
public interface Protocol {
    /**
     * Get default port when user doesn't config the port.
     *
     * @return default port
     */
    int getDefaultPort();

    @Adaptive
    <T> Exporter<T> export(Invoker<T> invoker) throws RpcException;

    @Adaptive
    <T> Invoker<T> refer(Class<T> type, URL url) throws RpcException;

    /**
     * Get all servers serving this protocol
     */
    default List<ProtocolServer> getServers() {
        return Collections.emptyList();
    }

    /**
     * Destroy protocol: <br>
     * 1. Cancel all services this protocol exports and refers <br>
     * 2. Release all occupied resources, for example: connection, port, etc. <br>
     * 3. Protocol can continue to export and refer new service even after it's destroyed.
     */
    void destroy();
}
