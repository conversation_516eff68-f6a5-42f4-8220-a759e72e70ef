package cn.huolala.arch.hermes.protocol;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.remoting.Server;

/**
 * Distinct from {@link Server}, each protocol holds one or more ProtocolServers(the number usually decides by port numbers),
 * while each ProtocolServer holds zero or one {@link Server}.
 */
public interface ProtocolServer {

    default Server getRemotingServer() {
        return null;
    }

    default void setRemotingServers(Server server) {
    }

    String getAddress();

    void setAddress(String address);

    default URL getUrl() {
        return null;
    }

    default void reset(URL url) {
    }

    void close();
}
