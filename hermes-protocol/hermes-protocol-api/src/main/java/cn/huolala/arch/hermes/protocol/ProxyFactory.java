package cn.huolala.arch.hermes.protocol;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.extension.Adaptive;
import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.huolala.arch.hermes.protocol.proxy.javassist.JavassistProxyFactory;

import static cn.huolala.arch.hermes.common.constants.Constants.PROXY_KEY;

/**
 * ProxyFactory
 */
@SPI(JavassistProxyFactory.NAME)
@ApiAudience.Private
public interface ProxyFactory {
    /**
     * create proxy.
     */
    @Adaptive({PROXY_KEY})
    <T> T getProxy(Invoker<T> invoker) throws RpcException;

    /**
     * create proxy.
     */
    @Adaptive({PROXY_KEY})
    <T> T getProxy(Invoker<T> invoker, boolean generic) throws RpcException;

    /**
     * create invoker.
     */
    @Adaptive({PROXY_KEY})
    <T> Invoker<T> getInvoker(T proxy, Class<T> type, URL url) throws RpcException;

}
