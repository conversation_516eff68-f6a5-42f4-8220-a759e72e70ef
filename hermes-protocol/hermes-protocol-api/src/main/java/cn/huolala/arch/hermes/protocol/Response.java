package cn.huolala.arch.hermes.protocol;

import java.io.Serializable;
import java.util.Map;

/**
 * Invoker real {@link Response}
 * @see Result
 */
public interface Response extends Serializable {
    /**
     * Get invoke Invocation
     */
    Invocation getInvocation();

    /**
     * Get invoke result.
     *
     * @return result. if no result return null.
     */
    Object getValue();

    void setValue(Object value);

    /**
     * Get exception.
     *
     * @return exception. if no exception return null.
     */
    Throwable getException();

    void setException(Throwable t);

    /**
     * Has exception.
     *
     * @return has exception.
     */
    boolean hasException();

    /**
     * Recreate.
     * <p>
     * <code>
     * if (hasException()) {
     * throw getException();
     * } else {
     * return getValue();
     * }
     * </code>
     *
     * @return result.
     * @throws Throwable has exception throw it.
     */
    Object recreate() throws Throwable;

    /**
     * get attachments.
     *
     * @return attachments.
     */
    Map<String, Object> getAttachments();

    /**
     * Replace the existing attachments with the specified param.
     */
    void setAttachments(Map<String, Object> map);

    void putAttachment(String key, Object value);

    void putAttachmentIfAbsent(String key, Object value);

    /**
     * Put the specified map to existing attachments in this instance.
     */
    void putAllAttachments(Map<String, Object> map);

    /**
     * get attachment by key.
     */
    Object getAttachment(String key);

    /**
     * get attachment by key with default value
     */
    Object getAttachment(String key, Object defaultValue);

    /**
     * get string attachment by key.<br/>
     * attachment value instanceof String
     */
    String getAttachmentString(String key);

    /**
     * get string attachment by key with default value<br/>
     * attachment value instanceof String
     */
    String getAttachmentString(String key, String defaultValue);

}
