package cn.huolala.arch.hermes.protocol;

import cn.huolala.arch.hermes.common.constants.ConstantsMarker;

/**
 * Rpc Constants
 * @see cn.huolala.arch.hermes.common.constants.Constants
 */
public interface RpcConstants extends ConstantsMarker {
    String CONSUMER_MODEL = "consumerModel";
    String PROVIDER_MODEL = "providerModel";
    String METHOD_MODEL = "methodModel";

    String INTERFACE = "interface";
    String INTERFACES = "interfaces";

    String CONSUMER_METHOD_CONFIG = "consumerMethodConfig";

}