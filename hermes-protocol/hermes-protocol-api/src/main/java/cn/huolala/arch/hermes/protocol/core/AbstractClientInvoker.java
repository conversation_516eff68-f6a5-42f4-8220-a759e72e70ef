package cn.huolala.arch.hermes.protocol.core;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.remoting.Client;

import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Abstract Client Invoker
 *
 * @param <T> service interface type
 * @param <C> Client Type
 * @see Client
 */
public abstract class AbstractClientInvoker<T, C extends Client> extends AbstractInvoker<T> {
    private final ReentrantLock destroyLock = new ReentrantLock();
    protected final C client;

    public AbstractClientInvoker(Class<T> type, URL url, C client) {
        super(type, url);
        this.client = client;
    }

    public AbstractClientInvoker(Class<T> type, URL url, C client, String[] keys) {
        super(type, url, keys);
        this.client = client;
    }

    public AbstractClientInvoker(Class<T> type, URL url, C client, Map<String, Object> attachment) {
        super(type, url, attachment);
        this.client = client;
    }

    @Override
    public boolean isAvailable() {
        return super.isAvailable() && !client.isClosed();
    }

    @Override
    public boolean isDestroyed() {
        return super.isDestroyed() || client.isClosed();
    }

    @Override
    public void destroy() {
        if (!super.isDestroyed()) {
            destroyLock.lock();
            try {
                if (super.isDestroyed()) {
                    return;
                }
                super.destroy();
                client.close();
            } finally {
                destroyLock.unlock();
            }
        }
    }

    public C getClient() {
        return client;
    }
}
