package cn.huolala.arch.hermes.protocol.core;

import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.protocol.Exporter;
import cn.huolala.arch.hermes.protocol.Invoker;

/**
 * Abstract Exporter
 * @param <T> service interface type
 */
public abstract class AbstractExporter<T> implements Exporter<T> {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    protected final Invoker<T> invoker;

    private volatile boolean unExported = false;

    private Runnable unExportCallback;

    public AbstractExporter(Invoker<T> invoker) {
        if (invoker == null) {
            throw new IllegalStateException("service invoker == null");
        }
        if (invoker.getInterface() == null) {
            throw new IllegalStateException("service type == null");
        }
        if (invoker.getUrl() == null) {
            throw new IllegalStateException("service url == null");
        }
        this.invoker = invoker;
    }

    public AbstractExporter(Invoker<T> invoker, Runnable unExportCallback) {
        this(invoker);
        this.unExportCallback = unExportCallback;
    }

    @Override
    public Invoker<T> getInvoker() {
        return invoker;
    }

    @Override
    public void unExport() {
        if (unExported) {
            return;
        }
        unExported = true;
        getInvoker().destroy();

        if (unExportCallback != null) {
            unExportCallback.run();
        }
    }

    public AbstractExporter<T> setUnExportCallback(Runnable unExportCallback) {
        this.unExportCallback = unExportCallback;
        return this;
    }

    @Override
    public String toString() {
        return getInvoker().toString();
    }

}
