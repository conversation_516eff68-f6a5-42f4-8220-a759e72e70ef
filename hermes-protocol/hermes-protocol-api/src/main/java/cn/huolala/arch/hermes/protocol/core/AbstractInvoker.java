package cn.huolala.arch.hermes.protocol.core;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.ArrayUtils;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.InvokeMode;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.huolala.arch.hermes.protocol.support.RpcUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static cn.huolala.arch.hermes.common.constants.Constants.TIMEOUT_KEY;

/**
 * Abstract Invoker
 *
 * @param <T> service interface type
 */
public abstract class AbstractInvoker<T> implements Invoker<T> {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    private final Class<T> type;

    private final URL url;

    private final Map<String, Object> attachment;

    private volatile boolean available = true;

    private final AtomicBoolean destroyed = new AtomicBoolean(false);

    private Runnable destroyCallback;

    public AbstractInvoker(Class<T> type, URL url) {
        this(type, url, (Map<String, Object>) null);
    }

    public AbstractInvoker(Class<T> type, URL url, String[] keys) {
        this(type, url, convertAttachment(url, keys));
    }

    public AbstractInvoker(Class<T> type, URL url, Map<String, Object> attachment) {
        if (type == null) {
            throw new IllegalArgumentException("service type == null");
        }
        if (url == null) {
            throw new IllegalArgumentException("service url == null");
        }
        this.type = type;
        this.url = url;
        this.attachment = attachment == null ? null : Collections.unmodifiableMap(attachment);
    }

    private static Map<String, Object> convertAttachment(URL url, String[] keys) {
        if (ArrayUtils.isEmpty(keys)) {
            return null;
        }
        Map<String, Object> attachment = new HashMap<>();
        for (String key : keys) {
            String value = url.getParameter(key);
            if (value != null && value.length() > 0) {
                attachment.put(key, value);
            }
        }
        return attachment;
    }

    @Override
    public Class<T> getInterface() {
        return type;
    }

    @Override
    public URL getUrl() {
        return url;
    }

    @Override
    public boolean isAvailable() {
        return available;
    }

    protected void setAvailable(boolean available) {
        this.available = available;
    }

    @Override
    public void destroy() {
        if (!destroyed.compareAndSet(false, true)) {
            return;
        }
        setAvailable(false);

        if (destroyCallback != null) {
            destroyCallback.run();
        }
    }

    public boolean isDestroyed() {
        return destroyed.get();
    }

    public AbstractInvoker<T> setDestroyCallback(Runnable destroyCallback) {
        this.destroyCallback = destroyCallback;
        return this;
    }

    @Override
    public String toString() {
        return getInterface() + " -> " + (getUrl() == null ? "" : getUrl().toString());
    }

    @Override
    public Result invoke(Invocation invocation) throws RpcException {
        // if invoker is destroyed due to address refresh from discovery, let's allow the current invoke to proceed
        if (isDestroyed()) {
            logger.warn("Invoker for service " + this
                    + " on consumer is destroyed, this invoker should not be used any longer");
        }

        // prepare rpc invocation
        prepareInvocation(invocation);

        return doInvokeAndMonitor( invocation);
    }

    /**
     * 调用并被Monitor拦截记录（monitor拦截的方法，慎重修改）
     * @param invocation
     * @return
     */
    private Result doInvokeAndMonitor(Invocation invocation) {

        // do invoke rpc invocation and return async result
        Result result = doInvokeAndReturn(invocation);

        // wait rpc result if sync
        waitForResultIfSync(result, invocation);

        return result;
    }

    private void prepareInvocation(Invocation invocation) {
        invocation.setInvoker(this);

        addInvocationAttachments(invocation);

        invocation.setInvokeMode(RpcUtils.getInvokeMode(url, invocation));

    }

    private void addInvocationAttachments(Invocation invocation) {
        // invoker attachment
        invocation.putAttachmentsIfAbsent(attachment);
    }

    private Result doInvokeAndReturn(Invocation invocation) {
        Result result;
        try {
            result = doInvoke(invocation).handle((value, ex) -> {
                // handle exception
                if (ex != null) {
                    throw handleException(invocation, ex);
                } else {
                    return value;
                }
            });
        } catch (InvocationTargetException e) {
            Throwable te = e.getTargetException();
            if (te != null) {
                // if biz exception
                if (te instanceof RpcException) {
                    ((RpcException) te).setCode(RpcException.BIZ_EXCEPTION);
                }
                result = RpcResult.newRpcResult(te, invocation);
            } else {
                result = RpcResult.newRpcResult(e, invocation);
            }
        } catch (RpcException e) {
            // if biz exception
            if (e.isBiz()) {
                result = RpcResult.newRpcResult(e, invocation);
            } else {
                throw e;
            }
        } catch (Throwable e) {
            result = RpcResult.newRpcResult(e, invocation);
        }

        return result;
    }

    private void waitForResultIfSync(Result result, Invocation invocation) {
        if (InvokeMode.SYNC != invocation.getInvokeMode()) {
            return;
        }
        try {
            /*
             * NOTICE!
             * must call {@link java.util.concurrent.CompletableFuture#get(long, TimeUnit)} because
             * {@link java.util.concurrent.CompletableFuture#get()} was proved to have serious performance drop.
             */
            Object timeout = invocation.get(TIMEOUT_KEY);
            if (timeout instanceof Integer) {
                result.get((Integer) timeout, TimeUnit.MILLISECONDS);
            } else {
                result.get(Integer.MAX_VALUE, TimeUnit.MILLISECONDS);
            }
        } catch (Throwable e) {
            throw handleException(invocation, e);
        }
    }

    /**
     * handleException
     */
    protected RpcException handleException(Invocation invocation, Throwable e) {
        return RpcException.getRpcException(getUrl(), invocation, e);
    }

    /**
     * Specific implementation of the {@link #invoke(Invocation)} method
     */
    protected abstract Result doInvoke(Invocation invocation) throws Throwable;
}
