package cn.huolala.arch.hermes.protocol.core;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.tool.ConcurrentHashSet;
import cn.huolala.arch.hermes.protocol.Exporter;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Protocol;
import cn.huolala.arch.hermes.protocol.ProtocolServer;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.huolala.arch.hermes.protocol.support.ProtocolUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import static cn.huolala.arch.hermes.common.constants.Constants.BIND_PORT_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.GROUP_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.VERSION_KEY;

/**
 * Abstract Protocol
 */
public abstract class AbstractProtocol implements Protocol {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    protected final Map<String, Exporter<?>> exporterMap = new ConcurrentHashMap<>();

    /**
     * <host:port, ProtocolServer>
     **/
    protected final Map<String, ProtocolServer> serverMap = new ConcurrentHashMap<>();

    protected final Set<Invoker<?>> invokers = new ConcurrentHashSet<>();

    @Override
    public <T> Exporter<T> export(final Invoker<T> invoker) throws RpcException {
        final String url = serviceKey(invoker.getUrl());
        @SuppressWarnings("unchecked")
        Exporter<T> exporter = (Exporter<T>) exporterMap.get(url);
        // When modifying the configuration through override, you need to re-expose the newly modified service.
        if (exporter != null && Objects.equals(exporter.getInvoker().getUrl(), invoker.getUrl())) {
            return exporter;
        }

        exporter = doExport(invoker).setUnExportCallback(() -> exporterMap.remove(url));
        exporterMap.put(url, exporter);
        return exporter;
    }

    @Override
    public <T> Invoker<T> refer(Class<T> type, URL url) throws RpcException {
        AbstractInvoker<T> invoker = doRefer(type, url);
        invoker.setDestroyCallback(() -> invokers.remove(invoker));
        invokers.add(invoker);
        return invoker;
    }

    protected static String serviceKey(URL url) {
        int port = url.getParameter(BIND_PORT_KEY, url.getPort());
        return serviceKey(port, url.getPath(), url.getParameter(VERSION_KEY), url.getParameter(GROUP_KEY));
    }

    protected static String serviceKey(int port, String serviceName, String serviceVersion, String serviceGroup) {
        return ProtocolUtils.serviceKey(port, serviceName, serviceVersion, serviceGroup);
    }

    @Override
    public List<ProtocolServer> getServers() {
        return Collections.unmodifiableList(new ArrayList<>(serverMap.values()));
    }

    @Override
    public void destroy() {
        try {
            invokers.stream().filter(Objects::nonNull).forEach(invoker -> {
                if (logger.isInfoEnabled()) {
                    logger.info("Destroy reference: " + invoker.getUrl());
                }
                invoker.destroy();
            });
            exporterMap.values().stream().filter(Objects::nonNull).forEach(exporter -> {
                if (logger.isInfoEnabled()) {
                    logger.info("UnExport service: " + exporter.getInvoker().getUrl());
                }
                exporter.unExport();
            });

            invokers.clear();
            exporterMap.clear();
            serverMap.clear();
        } catch (Throwable t) {
            logger.warn(t.getMessage(), t);
        }
    }

    protected abstract <T> AbstractExporter<T> doExport(Invoker<T> invoker) throws RpcException;

    protected abstract <T> AbstractInvoker<T> doRefer(Class<T> type, URL url) throws RpcException;
}