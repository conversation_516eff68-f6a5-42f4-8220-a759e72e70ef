package cn.huolala.arch.hermes.protocol.core;

import cn.huolala.arch.hermes.common.extension.Inject;
import cn.huolala.arch.hermes.protocol.ProxyFactory;

/**
 * Abstract Proxy Protocol
 *
 * @see ProxyFactory
 */
public abstract class AbstractProxyProtocol extends AbstractProtocol {
    protected ProxyFactory proxyFactory;

    public ProxyFactory getProxyFactory() {
        return proxyFactory;
    }

    @Inject
    public void setProxyFactory(ProxyFactory proxyFactory) {
        this.proxyFactory = proxyFactory;
    }
}