package cn.huolala.arch.hermes.protocol.core;

import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

import static cn.huolala.arch.hermes.protocol.exception.RpcException.UNIMPLEMENTED;

/**
 * Only Refer AbstractProxyProtocol
 */
public abstract class AbstractReferOnlyProxyProtocol extends AbstractProxyProtocol {
    public static final int DEFAULT_PORT = 80;

    @Override
    public int getDefaultPort() {
        return DEFAULT_PORT;
    }

    @Override
    protected <T> AbstractExporter<T> doExport(Invoker<T> invoker) throws RpcException {
        throw new RpcException(UNIMPLEMENTED, "Protocol server Unimplemented");
    }
}
