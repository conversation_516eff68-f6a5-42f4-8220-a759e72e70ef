package cn.huolala.arch.hermes.protocol.core;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.protocol.ProtocolServer;
import cn.huolala.arch.hermes.remoting.Server;

/**
 * Proxy ProtocolServer
 */
public class ProxyProtocolServer implements ProtocolServer {
    private Server server;
    private String address;

    public ProxyProtocolServer(Server server) {
        this.server = server;
    }

    @Override
    public Server getRemotingServer() {
        return server;
    }

    @Override
    public String getAddress() {
        return StringUtils.isNotEmpty(address) ? address : server.getUrl().getAddress();
    }

    @Override
    public void setAddress(String address) {
        this.address = address;
    }

    @Override
    public URL getUrl() {
        return server.getUrl();
    }

    @Override
    public void close() {
        server.close();
    }
}