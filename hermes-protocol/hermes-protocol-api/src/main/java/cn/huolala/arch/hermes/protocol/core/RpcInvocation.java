package cn.huolala.arch.hermes.protocol.core;

import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.context.ServiceRepository;
import cn.huolala.arch.hermes.common.context.model.MethodModel;
import cn.huolala.arch.hermes.common.context.model.ServiceModel;
import cn.huolala.arch.hermes.common.logger.Level;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.support.function.ThrowableSupplier;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.InvokeMode;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.RpcConstants;
import cn.huolala.arch.hermes.protocol.support.RpcUtils;

import java.io.Serializable;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.huolala.arch.hermes.common.constants.Constants.DYNAMIC_LOG_BASE_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.DYNAMIC_LOG_LEVEL_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.GRAY_VERSION_KEY;

/**
 * RPC Invocation.
 *
 * @serial Don't change the class name and properties.
 */
public class RpcInvocation implements Invocation, Serializable {
    private static final long serialVersionUID = 4161654653495433919L;

    private String protocolServiceKey;
    private String remoteApplication;
    private String commandKey;
    private String serviceCommandKey;
    private String appCommandKey;
    private String serviceName;
    private String version;
    private String methodName;

    private transient Class<?>[] parameterTypes;
    private Object[] arguments;

    private String parameterTypesDesc;
    private String[] compatibleParamSignatures;
    private transient Class<?> returnType;
    private transient Type[] returnTypes;

    private transient Invoker<?> invoker;
    private transient InvokeMode invokeMode;
    private Integer timeout;
    private Integer connectionTimeout;

    /**
     * Only used on the caller side, will not appear on the wire.
     */
    private final transient Map<Object, Object> attributes;

    public RpcInvocation(String protocolServiceKey, String serviceName, String version,
                         String methodName, Class<?>[] parameterTypes, Object[] arguments,
                         Map<Object, Object> attributes) {
        this.protocolServiceKey = protocolServiceKey;
        this.serviceName = serviceName;
        this.version = version;
        this.methodName = methodName;
        this.parameterTypes = parameterTypes == null ? new Class<?>[0] : parameterTypes;
        this.arguments = arguments == null ? new Object[0] : arguments;

        this.attributes = attributes == null ? new HashMap<>() : attributes;

        initParameterDesc();
    }

    private void initParameterDesc() {
        ServiceRepository repository = ApplicationContext.getServiceRepository();
        if (StringUtils.isNotEmpty(serviceName)) {
            ServiceModel serviceModel = repository.lookupService(serviceName);
            if (serviceModel != null) {
                MethodModel methodModel = serviceModel.getMethod(methodName, parameterTypes);
                if (methodModel != null) {
                    this.parameterTypesDesc = methodModel.getParamDesc();
                    this.compatibleParamSignatures = methodModel.getCompatibleParamSignatures();
                    this.returnTypes = methodModel.getReturnTypes();
                    this.returnType = methodModel.getReturnClass();
                }
            }
        }

        if (parameterTypesDesc == null) {
            this.parameterTypesDesc = ReflectUtils.getDesc(this.getParameterTypes());
            this.compatibleParamSignatures = Stream.of(this.parameterTypes).map(Class::getName).toArray(String[]::new);
            this.returnTypes = RpcUtils.getReturnTypes(this);
            this.returnType = RpcUtils.getReturnType(this);
        }
    }

    @Override
    public Invoker<?> getInvoker() {
        return invoker;
    }

    @Override
    public void setInvoker(Invoker<?> invoker) {
        this.invoker = invoker;
    }

    @Override
    public InvokeMode getInvokeMode() {
        return invokeMode;
    }

    @Override
    public void setInvokeMode(InvokeMode invokeMode) {
        this.invokeMode = invokeMode;
    }

    @Override
    public String getProtocolServiceKey() {
        return protocolServiceKey;
    }

    public void setProtocolServiceKey(String protocolServiceKey) {
        this.protocolServiceKey = protocolServiceKey;
    }

    @Override
    public String getRemoteApplication() {
        return remoteApplication;
    }

    @Override
    public void setRemoteApplication(String remoteApplication) {
        this.remoteApplication = remoteApplication;
    }

    @Override
    public String getCommandKey() {
        return commandKey;
    }

    public void setCommandKey(String commandKey) {
        this.commandKey = commandKey;
    }

    @Override
    public String getServiceCommandKey() {
        return serviceCommandKey;
    }

    public void setServiceCommandKey(String serviceCommandKey) {
        this.serviceCommandKey = serviceCommandKey;
    }

    @Override
    public String getAppCommandKey() {
        return appCommandKey;
    }

    public void setAppCommandKey(String appCommandKey) {
        this.appCommandKey = appCommandKey;
    }

    @Override
    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    @Override
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Override
    public String getGrayVersion() {
        return getRawAttachmentString(GRAY_VERSION_KEY);
    }

    @Override
    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    @Override
    public Class<?>[] getParameterTypes() {
        return parameterTypes;
    }

    public void setParameterTypes(Class<?>[] parameterTypes) {
        this.parameterTypes = parameterTypes == null ? new Class<?>[0] : parameterTypes;
    }

    @Override
    public String getParameterTypesDesc() {
        return parameterTypesDesc;
    }

    @Override
    public String[] getCompatibleParamSignatures() {
        return compatibleParamSignatures;
    }

    @Override
    public Object[] getArguments() {
        return arguments;
    }

    public void setArguments(Object[] arguments) {
        this.arguments = arguments == null ? new Object[0] : arguments;
    }

    @Override
    public Class<?> getReturnType() {
        return returnType;
    }

    public void setReturnType(Class<?> returnType) {
        this.returnType = returnType;
    }

    @Override
    public Type[] getReturnTypes() {
        return returnTypes;
    }

    public void setReturnTypes(Type[] returnTypes) {
        this.returnTypes = returnTypes;
    }

    @Override
    public Map<Object, Object> getAttributes() {
        return attributes;
    }

    @Override
    public Object put(Object key, Object value) {
        return attributes.put(key, value);
    }

    @Override
    public Object putIfAbsent(Object key, Object value) {
        return attributes.putIfAbsent(key, value);
    }

    @Override
    public Object get(Object key) {
        return attributes.get(key);
    }

    @Override
    public Object get(String key, Object defaultValue) {
        return attributes.getOrDefault(key, defaultValue);
    }

    @Override
    public String getString(String key, String defaultValue) {
        return Optional.ofNullable(attributes.get(key))
                .filter(v -> v instanceof String)
                .map(Object::toString)
                .orElse(defaultValue);
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public void setConnectionTimeout(Integer connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }

    public Integer getConnectionTimeout() {
        return connectionTimeout;
    }

    @Override
    public ConcurrentMap<String, Object> getAttachments() {
        if (isProviderSide()) {
            return RpcContext.getServerAttachment().getAttachments();
        }
        return RpcContext.getClientAttachment().getAttachments();
    }

    @Override
    public String getAttachmentString(String key, String defaultValue) {
        return Optional.ofNullable(getAttachments())
                .map(a -> a.get(key))
                .filter(v -> v instanceof String)
                .map(Object::toString)
                .orElse(defaultValue);
    }

    @Override
    public Map<String, Object> getRawAttachments() {

        ConcurrentMap<String, Object> rawAttachments = new ConcurrentHashMap<>();

        if (isProviderSide()) {
            rawAttachments = RpcContext.getServiceContext().getAttachments();
        } else {
            rawAttachments = Stream.of(RpcContext.getServiceContext().getAttachments(), RpcContext.getClientAttachment().getRawAttachments())
                    .flatMap(map -> map.entrySet().stream())
                    .collect(Collectors.toConcurrentMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (v1, v2) -> v2));
        }
        return Collections.unmodifiableMap(rawAttachments);
    }

    private Object getRawAttachmentObject(String key, Object defaultValue) {

        if (isProviderSide()) {
            return RpcContext.getServiceContext().getAttachments().getOrDefault(key, defaultValue);
        }

        // Consumer side
        Object value = RpcContext.getClientAttachment().getRawAttachments().get(key);
        if (Objects.isNull(value)) {
            value = RpcContext.getServiceContext().getAttachments().getOrDefault(key, defaultValue);
        }
        return value;
    }

    @Override
    public void putRawAttachment(String key, Object value) {
        if (isConsumerSide()) {
            getClientRawAttachments().put(key, value);
        }
    }

    @Override
    public void putRawAttachmentIfAbsent(String key, Object value) {
        if (isConsumerSide()) {
            getClientRawAttachments().putIfAbsent(key, value);
        }
    }

    @Override
    public Object getRawAttachment(String key, Object defaultValue) {
        return getRawAttachmentObject(key, defaultValue);
    }

    @Override
    public String getRawAttachmentString(String key, String defaultValue) {
        return Optional.ofNullable(getRawAttachment(key))
                .filter(v -> v instanceof String)
                .map(Object::toString)
                .orElse(defaultValue);
    }

    @Override
    public void putDMCLog(String key, ThrowableSupplier<Object> value, Level level) {
        Logger logger = LoggerFactory.getLogger(this.commandKey);

        Object originalValue;
        if (level.ordinal() >= logger.getLevel().ordinal() && Objects.nonNull(originalValue = value.getIgnoreThrowable())) {
            attributeLevelMDCLogs().put(key, originalValue);
        }
    }

    @Override
    public void putDMCLog(String key, Object value) {
        if (Objects.nonNull(value)) {
            attributeBaseMDCLogs().put(key, value);
        }
    }

    @Override
    public Map<String, Object> getBaseMDCLogs() {
        return attributeBaseMDCLogs();
    }

    @Override
    public Map<String, Object> getLevelMDCLogs() {
        return attributeLevelMDCLogs();
    }

    @Override
    public boolean isProviderSide() {
        return getAttributes().containsKey(RpcConstants.PROVIDER_MODEL);
    }

    @Override
    public boolean isConsumerSide() {
        return getAttributes().containsKey(RpcConstants.CONSUMER_MODEL);
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> attributeBaseMDCLogs() {
        return (Map<String, Object>) getAttributes().computeIfAbsent(DYNAMIC_LOG_BASE_KEY, (Function<Object, Map<String, Object>>) o -> new LinkedHashMap<>());
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> attributeLevelMDCLogs() {
        return (Map<String, Object>) getAttributes().computeIfAbsent(DYNAMIC_LOG_LEVEL_KEY, (Function<Object, Map<String, Object>>) o -> new LinkedHashMap<>());
    }

    private ConcurrentMap<String, Object> getClientRawAttachments() {
        return RpcContext.getClientAttachment().getRawAttachments();
    }

    @Override
    public String toString() {
        return "RpcInvocation [methodName=" + methodName + ", parameterTypes="
                + Arrays.toString(parameterTypes) + ", arguments=" + Arrays.toString(arguments)
                + ", attachments=" + getAttachments() + ", rawAttachments=" + getRawAttachments() + "]";
    }

    /**
     * RpcInvocation Builder
     */
    public static final class Builder {
        private String protocolServiceKey;
        private String serviceName;
        private String version;
        private String methodName;
        private transient Class<?>[] parameterTypes;
        private Object[] arguments;

        private transient Map<Object, Object> attributes;

        private Builder() {
        }

        public static Builder newBuilder() {
            return new Builder();
        }

        public Builder protocolServiceKey(String protocolServiceKey) {
            this.protocolServiceKey = protocolServiceKey;
            return this;
        }

        public Builder serviceName(String serviceName) {
            this.serviceName = serviceName;
            return this;
        }

        public Builder method(Method method) {
            this.methodName = method.getName();
            this.parameterTypes = method.getParameterTypes();
            return this;
        }

        public Builder methodName(String methodName) {
            this.methodName = methodName;
            return this;
        }

        public Builder version(String version) {
            this.version = version;
            return this;
        }

        public Builder parameterTypes(Class<?>[] parameterTypes) {
            this.parameterTypes = parameterTypes;
            return this;
        }

        public Builder arguments(Object[] arguments) {
            this.arguments = arguments;
            return this;
        }

        public Builder attributes(Map<Object, Object> attributes) {
            this.attributes = attributes;
            return this;
        }

        public RpcInvocation build() {
            return new RpcInvocation(protocolServiceKey, serviceName, version, methodName, parameterTypes, arguments, attributes);
        }
    }

}