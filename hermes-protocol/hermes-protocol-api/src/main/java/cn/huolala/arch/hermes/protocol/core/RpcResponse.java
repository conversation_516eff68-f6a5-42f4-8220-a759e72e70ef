package cn.huolala.arch.hermes.protocol.core;

import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Response;
import cn.huolala.arch.hermes.protocol.proxy.InvokerInvocationHandler;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Rpc invoker real {@link Response}
 *
 * @see Invoker#invoke(Invocation)
 * @see RpcResult
 */
public class RpcResponse implements Response {
    private static final long serialVersionUID = -1456585988623370225L;

    private final Invocation invocation;

    private Object value;

    private Throwable exception;

    private Map<String, Object> attachments = new HashMap<>();

    public RpcResponse(Invocation invocation) {
        this.invocation = invocation;
    }

    public RpcResponse(Invocation invocation, Object value) {
        this.invocation = invocation;
        this.value = value;
    }

    public RpcResponse(Invocation invocation, Throwable exception) {
        this.invocation = invocation;
        this.exception = exception;
    }

    public void clear() {
        this.value = null;
        this.exception = null;
        this.attachments.clear();
    }

    @Override
    public Object recreate() throws Throwable {
        if (exception != null) {
            try {
                Object stackTrace = InvokerInvocationHandler.stackTraceField.get(exception);
                if (stackTrace == null) {
                    exception.setStackTrace(new StackTraceElement[0]);
                }
            } catch (Exception ignored) {
            }
            throw exception;
        }
        return value;
    }

    @Override
    public Invocation getInvocation() {
        return invocation;
    }

    @Override
    public Object getValue() {
        return value;
    }

    @Override
    public void setValue(Object value) {
        this.value = value;
    }

    @Override
    public Throwable getException() {
        return exception;
    }

    @Override
    public void setException(Throwable exception) {
        this.exception = exception;
    }

    @Override
    public boolean hasException() {
        return exception != null;
    }

    @Override
    public Map<String, Object> getAttachments() {
        return attachments;
    }

    @Override
    public void setAttachments(Map<String, Object> map) {
        this.attachments = map == null ? new HashMap<>() : map;
    }

    @Override
    public void putAttachment(String key, Object value) {
        if (attachments == null) {
            attachments = new HashMap<>();
        }
        attachments.put(key, value);
    }

    @Override
    public void putAttachmentIfAbsent(String key, Object value) {
        if (attachments == null) {
            attachments = new HashMap<>();
        }
        attachments.putIfAbsent(key, value);
    }

    @Override
    public void putAllAttachments(Map<String, Object> map) {
        if (map == null) {
            return;
        }
        if (attachments == null) {
            attachments = new HashMap<>();
        }
        this.attachments.putAll(map);
    }

    @Override
    public Object getAttachment(String key) {
        if (attachments == null) {
            return null;
        }
        return attachments.get(key);
    }

    @Override
    public Object getAttachment(String key, Object defaultValue) {
        if (attachments == null) {
            return defaultValue;
        }
        return attachments.getOrDefault(key, defaultValue);
    }

    @Override
    public String getAttachmentString(String key) {
        return getAttachmentString(key, null);
    }

    @Override
    public String getAttachmentString(String key, String defaultValue) {
        return Optional.ofNullable(attachments)
                .map(a -> a.get(key))
                .filter(v -> v instanceof String)
                .map(Object::toString)
                .orElse(defaultValue);
    }

    @Override
    public String toString() {
        return "RpcResponse [value=" + value + ", exception=" + exception + "]";
    }
}
