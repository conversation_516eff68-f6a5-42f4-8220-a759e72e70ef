package cn.huolala.arch.hermes.protocol.core;

import cn.huolala.arch.hermes.common.Node;
import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.InvokeMode;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Response;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.huolala.arch.hermes.protocol.support.FutureAdapter;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;

import static cn.huolala.arch.hermes.common.constants.Constants.TIMEOUT_KEY;

/**
 * This class represents an unfinished RPC call, it will hold some context information for this call, for example Rpc<PERSON>ontext and Invocation,
 * so that when the call finishes and the result returns, it can guarantee all the contexts being recovered as the same as
 * when the call was made before any callback is invoked.
 *
 * @see Invoker#invoke(Invocation)
 * @see RpcResponse
 */
public class RpcResult implements Result {
    private static final long serialVersionUID = 2176597186261537572L;

    private final Invocation invocation;

    private CompletableFuture<Response> responseFuture;

    public RpcResult(CompletableFuture<Response> future, Invocation invocation) {
        this.responseFuture = future;
        this.invocation = invocation;
    }

    // static newRpcResult

    public static RpcResult newRpcResult(Response response, Invocation invocation) {
        return new RpcResult(CompletableFuture.completedFuture(response), invocation);
    }

    public static RpcResult newRpcResult(Invocation invocation) {
        return newRpcResult(null, null, invocation);
    }

    public static RpcResult newRpcResult(Object value, Invocation invocation) {
        return newRpcResult(value, null, invocation);
    }

    public static RpcResult newRpcResult(Throwable t, Invocation invocation) {
        return newRpcResult(null, t, invocation);
    }

    public static RpcResult newRpcResult(Object value, Throwable t, Invocation invocation) {
        RpcResponse result = new RpcResponse(invocation);
        if (t != null) {
            result.setException(t);
        } else {
            result.setValue(value);
        }
        return newRpcResult(result, invocation);
    }

    @Override
    public boolean isDone() {
        return responseFuture.isDone();
    }

    @Override
    public Response get() throws RpcException {
        try {
            /*
             * NOTICE!
             * must call {@link java.util.concurrent.CompletableFuture#get(long, TimeUnit)} because
             * {@link java.util.concurrent.CompletableFuture#get()} was proved to have serious performance drop.
             */
            Object timeout = invocation.get(TIMEOUT_KEY);
            if (timeout instanceof Integer) {
                return responseFuture.get((Integer) timeout, TimeUnit.MILLISECONDS);
            } else {
                return responseFuture.get(Integer.MAX_VALUE, TimeUnit.MILLISECONDS);
            }
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            throw getRpcException(e);
        }
    }

    @Override
    public Response get(long timeout, TimeUnit unit) throws RpcException {
        try {
            return responseFuture.get(timeout, unit);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            throw getRpcException(e);
        }
    }

    @Override
    public void setValue(Object value) {
        try {
            if (responseFuture.isDone()) {
                responseFuture.get().setValue(value);
            } else {
                responseFuture.complete(new RpcResponse(invocation, value));
            }
        } catch (InterruptedException | ExecutionException e) {
            throw getRpcException(e);
        }
    }

    @Override
    public void setException(Throwable t) {
        try {
            if (responseFuture.isDone()) {
                responseFuture.get().setException(t);
            } else {
                responseFuture.complete(new RpcResponse(invocation, t));
            }
        } catch (InterruptedException | ExecutionException e) {
            throw getRpcException(e);
        }
    }

    @Override
    public Object recreate() throws Throwable {
        if (invocation.getInvokeMode() == InvokeMode.FUTURE) {
            return FutureAdapter.toFuture(this.responseFuture);
        }
        return get().recreate();
    }

    @Override
    public RpcResult whenComplete(BiConsumer<Response, Throwable> fn) {
        this.responseFuture = this.responseFuture.whenComplete(fn);
        return this;
    }

    @Override
    public Result handle(BiFunction<Response, Throwable, ? extends Response> fn) {
        this.responseFuture = this.responseFuture.handle(fn);
        return this;
    }

    @Override
    public CompletableFuture<Response> getFuture() {
        return responseFuture;
    }

    @Override
    public void setFuture(CompletableFuture<Response> future) {
        this.responseFuture = future;
    }

    private RpcException getRpcException(Throwable e) {
        URL url = Optional.ofNullable(invocation.getInvoker())
                .map(Node::getUrl)
                .orElse(null);
        return RpcException.getRpcException(url, invocation, e);
    }
}
