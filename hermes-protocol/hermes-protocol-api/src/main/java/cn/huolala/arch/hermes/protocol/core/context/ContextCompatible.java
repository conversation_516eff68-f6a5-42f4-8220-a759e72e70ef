package cn.huolala.arch.hermes.protocol.core.context;

import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.extension.SPI;

import java.util.Map;

@SPI(Constants.JSONRPC_PROTOCOL)
public interface ContextCompatible {

    Map<String, String> attachments();


    static ContextCompatible getDefaultExtension() {
        return ExtensionLoader.getExtensionLoader(ContextCompatible.class).getDefaultExtension();
    }

}
