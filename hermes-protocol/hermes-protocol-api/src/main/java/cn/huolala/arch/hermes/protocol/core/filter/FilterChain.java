package cn.huolala.arch.hermes.protocol.core.filter;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.protocol.BaseFilter;
import cn.huolala.arch.hermes.protocol.Filter;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

import java.util.Optional;

import static cn.huolala.arch.hermes.protocol.support.RpcUtils.getResultException;

/**
 * FilterChain
 *
 * @param <T> invoker type
 * @param <INVOKER>> Invoker
 * @param <FILTER>> BaseFilter type
 * @see Filter
 * @see ProtocolFilterWrapper
 */
public class FilterChain<T, INVOKER extends Invoker<T>, FILTER extends BaseFilter> implements Invoker<T> {
    protected final INVOKER invoker;
    protected final Invoker<T> next;
    protected final FILTER filter;

    public FilterChain(final INVOKER invoker, final Invoker<T> next, final FILTER filter) {
        this.invoker = invoker;
        this.next = next;
        this.filter = filter;
    }

    @Override
    public Class<T> getInterface() {
        return invoker.getInterface();
    }

    @Override
    public URL getUrl() {
        return invoker.getUrl();
    }

    @Override
    public boolean isAvailable() {
        return invoker.isAvailable();
    }

    @Override
    public Result invoke(Invocation invocation) throws RpcException {
        Result result;
        try {
            result = filter.invoke(next, invocation);
        } catch (Exception e) {
            if (filter instanceof FILTER.Listener) {
                FILTER.Listener listener = (FILTER.Listener) filter;
                listener.onError(e, invoker, invocation);
            }
            throw e;
        }

        return result.whenComplete((r, t) -> {
            if (filter instanceof FILTER.Listener) {
                FILTER.Listener listener = (FILTER.Listener) filter;

                Optional<Throwable> throwable = getResultException(r, t);
                if (throwable.isPresent()) {
                    listener.onError(throwable.get(), invoker, invocation);
                } else {
                    listener.onResult(r, invoker, invocation);
                }
            }
        });
    }

    @Override
    public void destroy() {
        invoker.destroy();
    }

    @Override
    public String toString() {
        return invoker.toString();
    }

    protected INVOKER getOriginalInvoker() {
        return invoker;
    }
}
