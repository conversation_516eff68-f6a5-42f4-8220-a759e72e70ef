package cn.huolala.arch.hermes.protocol.core.filter;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.extension.Wrapper;
import cn.huolala.arch.hermes.protocol.Exporter;
import cn.huolala.arch.hermes.protocol.Filter;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Protocol;
import cn.huolala.arch.hermes.protocol.ProtocolServer;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

import java.util.List;

import static cn.huolala.arch.hermes.common.constants.Constants.CONSUMER;
import static cn.huolala.arch.hermes.common.constants.Constants.PROVIDER;
import static cn.huolala.arch.hermes.common.constants.Constants.REFERENCE_FILTER_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.SERVICE_FILTER_KEY;

/**
 * ProtocolFilterWrapper
 *
 * @see Protocol
 * @see Filter
 * @see FilterChain
 */
@Wrapper
@Activate(order = 100)
public class ProtocolFilterWrapper implements Protocol {
    private final Protocol protocol;

    public ProtocolFilterWrapper(Protocol protocol) {
        if (protocol == null) {
            throw new IllegalArgumentException("protocol == null");
        }
        this.protocol = protocol;
    }

    @Override
    public int getDefaultPort() {
        return protocol.getDefaultPort();
    }

    @Override
    public <T> Exporter<T> export(Invoker<T> invoker) throws RpcException {
        return protocol.export(buildInvokerFilterChain(invoker, SERVICE_FILTER_KEY, PROVIDER));
    }

    @Override
    public <T> Invoker<T> refer(Class<T> type, URL url) throws RpcException {
        return buildInvokerFilterChain(protocol.refer(type, url), REFERENCE_FILTER_KEY, CONSUMER);
    }

    @Override
    public List<ProtocolServer> getServers() {
        return protocol.getServers();
    }

    @Override
    public void destroy() {
        protocol.destroy();
    }

    private static <T> Invoker<T> buildInvokerFilterChain(final Invoker<T> invoker, String key, String group) {
        Invoker<T> last = invoker;
        List<Filter> filters = ExtensionLoader.getExtensionLoader(Filter.class)
                .getActivateExtension(invoker.getUrl(), key, group);

        if (!filters.isEmpty()) {
            for (int i = filters.size() - 1; i >= 0; i--) {
                final Filter filter = filters.get(i);
                last = new FilterChain<>(invoker, last, filter);
            }
        }

        return last;
    }
}
