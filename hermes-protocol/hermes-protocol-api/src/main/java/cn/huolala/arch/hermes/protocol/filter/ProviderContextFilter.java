package cn.huolala.arch.hermes.protocol.filter;

import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.protocol.Filter;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Response;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.core.RpcContext;
import cn.huolala.arch.hermes.protocol.core.context.ServiceContext;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static cn.huolala.arch.hermes.common.constants.Constants.GROUP_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.INTERFACE_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.PATH_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.PROVIDER;
import static cn.huolala.arch.hermes.common.constants.Constants.TAGS_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.TIMEOUT_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.UP_STREAM_APP_ID;
import static cn.huolala.arch.hermes.common.constants.Constants.VERSION_KEY;
import static cn.huolala.arch.hermes.common.constants.SpiConstants.ORDER_STEP;
import static cn.huolala.arch.hermes.common.extension.Activate.MAX_ORDER;

/**
 * Set the provider RpcContext
 *
 * @see RpcContext
 */
@Activate(group = PROVIDER, order = MAX_ORDER + ORDER_STEP)
public class ProviderContextFilter implements Filter, Filter.Listener {

    private static final Set<String> UNLOADING_KEYS;

    static {
        UNLOADING_KEYS = new HashSet<>(16);
        UNLOADING_KEYS.add(PATH_KEY);
        UNLOADING_KEYS.add(INTERFACE_KEY);
        UNLOADING_KEYS.add(GROUP_KEY);
        UNLOADING_KEYS.add(VERSION_KEY);
        UNLOADING_KEYS.add(TIMEOUT_KEY);
        UNLOADING_KEYS.add(TAGS_KEY);
    }

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        // ServiceContext
        initServiceContext(invoker, invocation);

        invocation.setInvoker(invoker);

        // server context attachment
        filterUnloadingKeys(invocation.getAttachments());

        return invoker.invoke(invocation);
    }

    private void initServiceContext(Invoker<?> invoker, Invocation invocation) {
        ServiceContext context = RpcContext.getServiceContext();

        Optional.ofNullable(context.getAttachmentString(UP_STREAM_APP_ID)).ifPresent(invocation::setRemoteApplication);

        context.setInvoker(invoker);
        context.setInvocation(invocation);
    }

    @Override
    public void onResult(Response result, Invoker<?> invoker, Invocation invocation) {
    }

    @Override
    public void onError(Throwable err, Invoker<?> invoker, Invocation invocation) {
    }

    private Map<String, Object> filterUnloadingKeys(Map<String, Object> attachments) {
        if (CollectionUtils.isEmptyMap(attachments)) {
            return attachments;
        }

        attachments.entrySet().removeIf(entry -> UNLOADING_KEYS.contains(entry.getKey()));
        return attachments;
    }

}
