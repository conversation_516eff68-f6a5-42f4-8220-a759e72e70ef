package cn.huolala.arch.hermes.protocol.proxy;

import cn.huolala.arch.hermes.common.service.Destroyable;
import cn.huolala.arch.hermes.common.util.ClassUtils;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.ProxyFactory;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

import java.util.Arrays;
import java.util.LinkedHashSet;

import static cn.huolala.arch.hermes.common.constants.Constants.COMMA_SPLIT_PATTERN;
import static cn.huolala.arch.hermes.protocol.RpcConstants.INTERFACE;
import static cn.huolala.arch.hermes.protocol.RpcConstants.INTERFACES;

/**
 * Abstract ProxyFactory
 */
public abstract class AbstractProxyFactory implements ProxyFactory {
    private static final Class<?>[] INTERNAL_INTERFACES = new Class<?>[]{
        Destroyable.class
    };

    @Override
    public <T> T getProxy(Invoker<T> invoker) throws RpcException {
        return getProxy(invoker, false);
    }

    @Override
    public <T> T getProxy(Invoker<T> invoker, boolean generic) throws RpcException {
        // when compiling with native image, ensure that the order of the interfaces remains unchanged
        LinkedHashSet<Class<?>> interfaces = new LinkedHashSet<>();

        String config = invoker.getUrl().getParameter(INTERFACES);
        if (config != null && config.length() > 0) {
            String[] types = COMMA_SPLIT_PATTERN.split(config);
            for (String type : types) {
                ClassLoader classLoader = getClassLoader(invoker);
                interfaces.add(ReflectUtils.forName(classLoader, type));
            }
        }

        if (generic) {
            try {
                // find the real interface from url
                String realInterface = invoker.getUrl().getParameter(INTERFACE);
                ClassLoader classLoader = getClassLoader(invoker);
                interfaces.add(ReflectUtils.forName(classLoader, realInterface));
            } catch (Throwable ignored) {
            }
        }

        interfaces.add(invoker.getInterface());
        interfaces.addAll(Arrays.asList(INTERNAL_INTERFACES));

        return getProxy(invoker, interfaces.toArray(new Class<?>[0]));
    }

    private <T> ClassLoader getClassLoader(Invoker<T> invoker) {
        return ClassUtils.getClassLoader();
    }

    public abstract <T> T getProxy(Invoker<T> invoker, Class<?>[] types);

}
