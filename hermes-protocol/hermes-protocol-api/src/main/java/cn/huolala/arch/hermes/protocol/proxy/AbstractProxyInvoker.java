package cn.huolala.arch.hermes.protocol.proxy;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Response;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.core.RpcResult;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.huolala.arch.hermes.protocol.support.FutureAdapter;

import java.lang.reflect.InvocationTargetException;
import java.util.concurrent.CompletableFuture;

/**
 * This Invoker works on provider side, delegates RPC to interface implementation.
 * @param <T> proxy type
 */
public abstract class AbstractProxyInvoker<T> implements Invoker<T> {
    private final T proxy;

    private final Class<T> type;

    private final URL url;

    public AbstractProxyInvoker(T proxy, Class<T> type, URL url) {
        if (proxy == null) {
            throw new IllegalArgumentException("proxy == null");
        }
        if (type == null) {
            throw new IllegalArgumentException("interface == null");
        }
        if (!type.isInstance(proxy)) {
            throw new IllegalArgumentException(proxy.getClass().getName() + " not implement interface " + type);
        }
        this.proxy = proxy;
        this.type = type;
        this.url = url;
    }

    @Override
    public Class<T> getInterface() {
        return type;
    }

    @Override
    public URL getUrl() {
        return url;
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public void destroy() {
    }

    @Override
    public Result invoke(Invocation invocation) throws RpcException {
        try {
            Object value = doInvoke(proxy, invocation.getMethodName(), invocation.getParameterTypes(), invocation.getArguments());
            CompletableFuture<Object> future = wrapWithFuture(value);
            CompletableFuture<Response> responseFuture = FutureAdapter.toResponseFuture(future, invocation);
            return new RpcResult(responseFuture, invocation);
        } catch (InvocationTargetException e) {
            return RpcResult.newRpcResult(e.getTargetException(), invocation);
        } catch (Throwable e) {
            throw new RpcException(RpcException.UNKNOWN_EXCEPTION, "Failed to invoke remote proxy method "
                    + invocation.getMethodName() + " to " + getUrl() + ", cause: " + e.getMessage(), e);
        }
    }

    @SuppressWarnings("unchecked")
    private CompletableFuture<Object> wrapWithFuture(Object value) {
        if (value instanceof CompletableFuture) {
            return (CompletableFuture<Object>) value;
        }

        return CompletableFuture.completedFuture(value);
    }

    protected abstract Object doInvoke(T proxy, String methodName, Class<?>[] parameterTypes, Object[] arguments) throws Throwable;

    @Override
    public String toString() {
        return getInterface() + " -> " + (getUrl() == null ? " " : getUrl().toString());
    }

}
