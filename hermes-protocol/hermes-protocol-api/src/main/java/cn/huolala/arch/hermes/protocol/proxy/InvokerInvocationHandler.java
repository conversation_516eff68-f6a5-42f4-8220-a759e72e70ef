package cn.huolala.arch.hermes.protocol.proxy;

import cn.huolala.arch.hermes.api.config.MethodConfig;
import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.context.model.ConsumerModel;
import cn.huolala.arch.hermes.common.context.model.ProviderModel;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.RpcConstants;
import cn.huolala.arch.hermes.protocol.core.RpcContext;
import cn.huolala.arch.hermes.protocol.core.RpcInvocation;
import cn.huolala.arch.hermes.protocol.core.context.ContextCompatible;
import cn.huolala.arch.hermes.protocol.core.context.ServiceContext;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.huolala.arch.hermes.protocol.support.RpcUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

import static cn.huolala.arch.hermes.common.constants.Constants.CONNECTION_TIMEOUT_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.DEFAULT_CONNECTION_TIMEOUT;
import static cn.huolala.arch.hermes.common.constants.Constants.DEFAULT_TIMEOUT;
import static cn.huolala.arch.hermes.common.constants.Constants.GRAY_VERSION_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.HYPHEN_CLIENT_GROUP;
import static cn.huolala.arch.hermes.common.constants.Constants.HYPHEN_CLIENT_RELEASE_VERSION;
import static cn.huolala.arch.hermes.common.constants.Constants.HYPHEN_CLIENT_ZONE;
import static cn.huolala.arch.hermes.common.constants.Constants.LOADBALANCE_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.RPC_CONTEXT_BASE_PREFIX;
import static cn.huolala.arch.hermes.common.constants.Constants.TIMEOUT_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.UP_STREAM_APP_ID;
import static cn.huolala.arch.hermes.common.constants.Constants.VERSION_KEY;
import static cn.huolala.arch.hermes.protocol.exception.RpcException.getExceptionCode;
import static java.util.Optional.ofNullable;

/**
 * InvokerHandler
 */
public class InvokerInvocationHandler implements InvocationHandler {
    private final Invoker<?> invoker;
    private ConsumerModel consumerModel;
    private ProviderModel providerModel;
    private URL url;
    private String protocolServiceKey;

    public static Field stackTraceField;

    static {
        try {
            stackTraceField = Throwable.class.getDeclaredField("stackTrace");
            stackTraceField.setAccessible(true);
        } catch (NoSuchFieldException ignored) {
        }
    }

    public InvokerInvocationHandler(Invoker<?> handler) {
        this.invoker = handler;
        this.url = invoker.getUrl();
        this.protocolServiceKey = this.url.getProtocolServiceKey();

        String serviceKey = this.url.getServiceKey();
        if (serviceKey != null) {
            // one of them is `null`
            this.consumerModel = ApplicationContext.getConsumerModel(serviceKey);
            this.providerModel = ApplicationContext.getProviderModel(serviceKey);
        }
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        if (method.getDeclaringClass() == Object.class) {
            return method.invoke(invoker, args);
        }
        String methodName = method.getName();
        Class<?>[] parameterTypes = method.getParameterTypes();
        if (parameterTypes.length == 0) {
            if ("toString".equals(methodName)) {
                return invoker.toString();
            } else if ("$destroy".equals(methodName)) {
                invoker.destroy();
                return null;
            } else if ("hashCode".equals(methodName)) {
                return invoker.hashCode();
            }
        } else if (parameterTypes.length == 1 && "equals".equals(methodName)) {
            return invoker.equals(args[0]);
        }

        // invocation
        RpcInvocation rpcInvocation = RpcInvocation.Builder.newBuilder()
                .protocolServiceKey(this.protocolServiceKey)
                .serviceName(invoker.getInterface().getName())
                .version(url.getParameter(VERSION_KEY))
                .method(method)
                .arguments(args)
                .build();

        // consumer side
        if (consumerModel != null) {
            return doInvokeConsumer(rpcInvocation, method);
        }

        // provider side
        return doInvokeProvider(rpcInvocation, method);
    }

    private String getLoadBalance(RpcInvocation invocation) {
        Optional<MethodConfig> mc = consumerModel.getReferenceConfig()
                .getMethodConfigOptional(invocation.getMethodName(), invocation.getParameterTypesDesc());
        URL url = invoker.getUrl();
        return mc.map(MethodConfig::getLoadbalance).filter(StringUtils::isNotBlank)
                .orElse(url.getParameter(LOADBALANCE_KEY));
    }

    private Object doInvokeProvider(RpcInvocation rpcInvocation, Method method) throws Throwable {

        if (providerModel != null) {
            // provider side
            rpcInvocation.setCommandKey(providerModel.getCommandKey(method));
            rpcInvocation.setServiceCommandKey(providerModel.getServiceCommandKey());
            rpcInvocation.setAppCommandKey(providerModel.getAppCommandKey());
            rpcInvocation.put(RpcConstants.PROVIDER_MODEL, providerModel);
            // METHOD_MODEL usually not used
        }

        try {
            return invoker.invoke(rpcInvocation).recreate();
        } finally {
            RpcContext.removeContext();
        }

    }

    private Object doInvokeConsumer(RpcInvocation rpcInvocation, Method method) throws Throwable {

        // consumer side
        rpcInvocation.setInvokeMode(RpcUtils.getInvokeMode(url, rpcInvocation));
        rpcInvocation.setRemoteApplication(consumerModel.getRemoteApplication());
        rpcInvocation.setCommandKey(consumerModel.getCommandKey(method));
        rpcInvocation.setServiceCommandKey(consumerModel.getServiceCommandKey());
        rpcInvocation.setAppCommandKey(consumerModel.getAppCommandKey());
        rpcInvocation.put(RpcConstants.CONSUMER_MODEL, consumerModel);
        rpcInvocation.put(RpcConstants.METHOD_MODEL, consumerModel.getMethodModel(method));
        rpcInvocation.put(LOADBALANCE_KEY, getLoadBalance(rpcInvocation));

        Integer timeout = url.getParameter(TIMEOUT_KEY, DEFAULT_TIMEOUT);
        Integer connectionTimeout = url.getParameter(CONNECTION_TIMEOUT_KEY, DEFAULT_CONNECTION_TIMEOUT);
        Optional<MethodConfig> methodConfigOptional = consumerModel.getReferenceConfig().getMethodConfigOptional(rpcInvocation.getMethodName(), rpcInvocation.getParameterTypesDesc());
        if (methodConfigOptional.isPresent()) {
            MethodConfig methodConfig = methodConfigOptional.get();
            timeout = ofNullable(methodConfig.getTimeout()).orElse(timeout);
            connectionTimeout = ofNullable(methodConfig.getConnectionTimeout()).orElse(connectionTimeout);
        }
        rpcInvocation.setTimeout(timeout);
        rpcInvocation.setConnectionTimeout(connectionTimeout);

        RpcContext.RestoreServiceContext providerServiceContext = RpcContext.storeServiceContext();
        try {
            // ServiceContext
            initServiceContext(invoker, rpcInvocation, providerServiceContext);

            // 兼容1.X版本的attachments，且1.X版本的优先级较高
            rpcInvocation.putRawAttachments(new HashMap<>(ContextCompatible.getDefaultExtension().attachments()));
            rpcInvocation.putRawAttachment(UP_STREAM_APP_ID, ApplicationContext.getName());

            Optional.ofNullable(ApplicationContext.getApplicationConfig().getReleaseVersion())
                    .ifPresent(version -> rpcInvocation.putAttachment(HYPHEN_CLIENT_RELEASE_VERSION, version));
            Optional.ofNullable(ApplicationContext.getApplicationConfig().getZone())
                    .ifPresent(zone -> rpcInvocation.putAttachment(HYPHEN_CLIENT_ZONE, zone));
            Optional.ofNullable(ApplicationContext.getApplicationConfig().getGroup())
                    .ifPresent(group -> rpcInvocation.putAttachment(HYPHEN_CLIENT_GROUP, group));

            return invoker.invoke(rpcInvocation).recreate();
        } catch (RuntimeException re) {
            throw re;
        } catch (Exception e) {
            if (Arrays.asList(method.getExceptionTypes()).contains(e.getClass())) {
                throw e;
            }
            throw new RpcException(getExceptionCode(e), e);
        } finally {
            RpcContext.removeClientAttachment();
            RpcContext.restoreServiceContext(providerServiceContext);
        }

    }

    private void initServiceContext(Invoker<?> invoker, Invocation invocation,
                                    RpcContext.RestoreServiceContext providerServiceContext) {
        ServiceContext context = RpcContext.getServiceContext();
        context.setInvoker(invoker);
        context.setInvocation(invocation);

        // transparent headers
        ofNullable(providerServiceContext.getServiceContext())
                .map(ServiceContext::getAttachments)
                .ifPresent(attachments -> attachments.forEach((k, v) -> {
                    if (GRAY_VERSION_KEY.equalsIgnoreCase(k) || k.toLowerCase().startsWith(RPC_CONTEXT_BASE_PREFIX)) {
                        context.putAttachment(k, v);
                    }
                }));

        // inner raw attachments
        context.putAttachment(UP_STREAM_APP_ID, ApplicationContext.getName());

    }

    private Integer timeoutDetermine(URL url, List<String> keys, Supplier<Integer> other) {
         return keys.stream().map(key-> url.getParameter(key)).filter(StringUtils::isNotBlank).map(value->Integer.parseInt(value)).filter(value->value>0).findFirst().orElseGet(other);
    }

}
