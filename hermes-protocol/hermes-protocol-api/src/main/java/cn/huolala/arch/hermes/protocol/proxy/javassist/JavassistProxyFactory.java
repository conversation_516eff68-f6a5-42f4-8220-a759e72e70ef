package cn.huolala.arch.hermes.protocol.proxy.javassist;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.bytecode.Proxy;
import cn.huolala.arch.hermes.common.bytecode.Wrapper;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.proxy.AbstractProxyFactory;
import cn.huolala.arch.hermes.protocol.proxy.AbstractProxyInvoker;
import cn.huolala.arch.hermes.protocol.proxy.InvokerInvocationHandler;

/**
 * Javassist ProxyFactory
 */
public class JavassistProxyFactory extends AbstractProxyFactory {
    public static final String NAME = "javassist";

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getProxy(Invoker<T> invoker, Class<?>[] interfaces) {
        return (T) Proxy.getProxy(interfaces).newInstance(new InvokerInvocationHandler(invoker));
    }

    @Override
    public <T> Invoker<T> getInvoker(T proxy, Class<T> type, URL url) {
        // FIXME Wrapper cannot handle this scenario correctly: the classname contains '$'
        final Wrapper wrapper = Wrapper.getWrapper(proxy.getClass().getName().indexOf('$') < 0 ? proxy.getClass() : type);
        return new AbstractProxyInvoker<T>(proxy, type, url) {
            @Override
            protected Object doInvoke(T proxy, String methodName,
                                      Class<?>[] parameterTypes, Object[] arguments) throws Throwable {
                return wrapper.invokeMethod(proxy, methodName, parameterTypes, arguments);
            }
        };
    }
}
