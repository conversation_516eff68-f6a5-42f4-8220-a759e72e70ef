package cn.huolala.arch.hermes.protocol.support;

import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Response;
import cn.huolala.arch.hermes.protocol.core.RpcResponse;

import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

/**
 * CompletableFuture Adapter: CompletableFuture<Response>, CompletableFuture<Object>
 */
public class FutureAdapter extends CompletableFuture<Object> {
    private final CompletableFuture<Response> responseFuture;

    public FutureAdapter(CompletableFuture<Response> responseFuture) {
        this.responseFuture = responseFuture;
        this.responseFuture.whenComplete((response, t) -> {
            if (t != null) {
                if (t instanceof CompletionException) {
                    t = t.getCause();
                }
                this.completeExceptionally(t);
            } else {
                if (response.hasException()) {
                    this.completeExceptionally(response.getException());
                } else {
                    this.complete(response.getValue());
                }
            }
        });
    }

    /**
     * biz CompletableFuture<Object> to Rpc CompletableFuture<Response>
     */
    public static CompletableFuture<Response> toResponseFuture(CompletableFuture<Object> future, Invocation invocation) {
        return future.handle((obj, t) -> {
            RpcResponse response = new RpcResponse(invocation);
            if (t != null) {
                if (t instanceof CompletionException) {
                    response.setException(t.getCause());
                } else {
                    response.setException(t);
                }
            } else {
                response.setValue(obj);
            }
            return response;
        });
    }

    /**
     * Rpc CompletableFuture<Response> Adapter to biz CompletableFuture<Object>
     */
    public static CompletableFuture<Object> toFuture(CompletableFuture<Response> future) {
        return new FutureAdapter(future);
    }

    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return super.cancel(mayInterruptIfRunning) && responseFuture.cancel(mayInterruptIfRunning);
    }

    // https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/CompletableFuture.html
    // CompletableFuture.cancel(bool) is the same as CompleteExceptionally(new CancellationException())
    @Override
    public boolean completeExceptionally(Throwable ex) {
        if (ex instanceof CancellationException) {
            responseFuture.cancel(true);
        }
        return super.completeExceptionally(ex);
    }

}
