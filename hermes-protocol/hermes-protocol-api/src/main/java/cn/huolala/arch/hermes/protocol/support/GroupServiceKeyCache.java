package cn.huolala.arch.hermes.protocol.support;

import cn.huolala.arch.hermes.common.util.StringUtils;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static cn.huolala.arch.hermes.common.util.StringUtils.emptyIfNull;

/**
 * Group ServiceKeyCache
 */
public class GroupServiceKeyCache {
    private final String serviceGroup;

    // ConcurrentMap<serviceName, ConcurrentMap<serviceVersion, ConcurrentMap<port, ServiceKey>>>
    private final ConcurrentMap<String, ConcurrentMap<String, ConcurrentMap<Integer, String>>> serviceKeyMap;

    public GroupServiceKeyCache(String serviceGroup) {
        this.serviceGroup = serviceGroup;
        this.serviceKeyMap = new ConcurrentHashMap<>(512);
    }

    public String getServiceKey(String serviceName, String serviceVersion, int port) {

        ConcurrentMap<String, ConcurrentMap<Integer, String>> versionMap = serviceKeyMap.computeIfAbsent(serviceName, key -> new ConcurrentHashMap<>());

        serviceVersion = emptyIfNull(serviceVersion);
        ConcurrentMap<Integer, String> portMap = versionMap.computeIfAbsent(serviceVersion, key -> new ConcurrentHashMap<>());

        String finalServiceVersion = serviceVersion;
        return portMap.computeIfAbsent(port, key -> createServiceKey(serviceName, finalServiceVersion, key));
    }

    private String createServiceKey(String serviceName, String serviceVersion, int port) {
        StringBuilder buf = new StringBuilder();
        if (StringUtils.isNotEmpty(serviceGroup)) {
            buf.append(serviceGroup).append('/');
        }

        buf.append(serviceName);
        if (StringUtils.isNotEmpty(serviceVersion) && !"0.0.0".equals(serviceVersion) && !"*".equals(serviceVersion)) {
            buf.append(':').append(serviceVersion);
        }
        buf.append(':').append(port);
        return buf.toString();
    }
}
