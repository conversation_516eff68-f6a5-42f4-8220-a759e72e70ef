package cn.huolala.arch.hermes.protocol.support;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.Result;
import cn.huolala.arch.hermes.protocol.exception.RpcException;

/**
 * Invoker Adapter
 *
 * @param <T> service interface type
 */
public class InvokerAdapter<T> implements Invoker<T> {

    protected final Invoker<T> invoker;

    protected final URL url;

    public InvokerAdapter(Invoker<T> invoker, URL url) {
        this.invoker = invoker;
        this.url = url;
    }

    @Override
    public Class<T> getInterface() {
        return invoker.getInterface();
    }

    @Override
    public URL getUrl() {
        return url;
    }

    @Override
    public boolean isAvailable() {
        return invoker.isAvailable();
    }

    @Override
    public Result invoke(Invocation invocation) throws RpcException {
        return invoker.invoke(invocation);
    }

    @Override
    public void destroy() {
        invoker.destroy();
    }

}