package cn.huolala.arch.hermes.protocol.support;

import cn.huolala.arch.hermes.common.URL;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static cn.huolala.arch.hermes.common.constants.Constants.GROUP_KEY;
import static cn.huolala.arch.hermes.common.constants.Constants.VERSION_KEY;
import static cn.huolala.arch.hermes.common.util.StringUtils.emptyIfNull;

/**
 * Protocol Utils
 */
public final class ProtocolUtils {
    private static final ConcurrentMap<String, GroupServiceKeyCache> groupServiceKeyCacheMap = new ConcurrentHashMap<>();

    private ProtocolUtils() {
    }

    public static String serviceKey(URL url) {
        return serviceKey(url.getPort(), url.getPath(), url.getParameter(VERSION_KEY), url.getParameter(GROUP_KEY));
    }

    public static String serviceKey(int port, String serviceName, String serviceVersion, String serviceGroup) {
        GroupServiceKeyCache groupServiceKeyCache = groupServiceKeyCacheMap.computeIfAbsent(emptyIfNull(serviceGroup), key -> new GroupServiceKeyCache(key));
        return groupServiceKeyCache.getServiceKey(serviceName, serviceVersion, port);
    }
}