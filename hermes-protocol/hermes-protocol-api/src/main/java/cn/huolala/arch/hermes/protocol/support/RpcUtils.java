package cn.huolala.arch.hermes.protocol.support;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.ReflectUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.InvokeMode;
import cn.huolala.arch.hermes.protocol.Response;

import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.RejectedExecutionException;

/**
 * Rpc Utils
 */
public final class RpcUtils {
    private static final Logger logger = LoggerFactory.getLogger(RpcUtils.class);

    private RpcUtils() {
    }


    public static Class<?> getReturnType(Invocation invocation) {
        try {
            if (invocation != null && invocation.getInvoker() != null
                    && invocation.getInvoker().getUrl() != null
                    && !invocation.getMethodName().startsWith("$")) {
                String service = invocation.getInvoker().getUrl().getServiceInterface();
                if (StringUtils.isNotEmpty(service)) {
                    Method method = getMethodByService(invocation, service);
                    return method.getReturnType();
                }
            }
        } catch (Throwable t) {
            logger.warn(t.getMessage(), t);
        }
        return null;
    }

    public static Type[] getReturnTypes(Invocation invocation) {
        try {
            if (invocation != null && invocation.getInvoker() != null
                    && invocation.getInvoker().getUrl() != null
                    && !invocation.getMethodName().startsWith("$")) {
                String service = invocation.getInvoker().getUrl().getServiceInterface();
                if (StringUtils.isNotEmpty(service)) {
                    Method method = getMethodByService(invocation, service);
                    return ReflectUtils.getReturnTypes(method);
                }
            }
        } catch (Throwable t) {
            logger.warn(t.getMessage(), t);
        }
        return null;
    }

    private static Method getMethodByService(Invocation invocation, String service) throws NoSuchMethodException {
        Class<?> invokerInterface = invocation.getInvoker().getInterface();
        Class<?> cls = invokerInterface != null ? ReflectUtils.forName(invokerInterface.getClassLoader(), service)
                : ReflectUtils.forName(service);
        return cls.getMethod(invocation.getMethodName(), invocation.getParameterTypes());
    }

    public static boolean isReturnTypeFuture(Invocation inv) {
        Class<?> clazz = inv.getReturnType();
        if (clazz == null) {
            clazz = getReturnType(inv);
        }
        return clazz != null && CompletableFuture.class.isAssignableFrom(clazz);
    }

    public static InvokeMode getInvokeMode(URL url, Invocation inv) {
        if (inv.getInvokeMode() != null) {
            return inv.getInvokeMode();
        } else if (isReturnTypeFuture(inv)) {
            return InvokeMode.FUTURE;
        } else {
            return InvokeMode.SYNC;
        }
    }

    public static String getMethodName(Invocation invocation) {
        return invocation.getMethodName();
    }

    public static Optional<Throwable> getResultException(Response response, Throwable throwable) {
        Throwable t = throwable != null ? throwable
                : (response != null ? response.getException() : null);

        if ((t instanceof ExecutionException
                || t instanceof CompletionException
                || t instanceof RejectedExecutionException)
                && t.getCause() != null) {
            t = t.getCause();
        }

        return Optional.ofNullable(t);
    }
}
