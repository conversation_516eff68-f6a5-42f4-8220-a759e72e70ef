package cn.huolala.arch.hermes.protocol.core;

import cn.huolala.arch.hermes.protocol.core.context.ServiceContext;
import com.alibaba.ttl.TtlRunnable;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

/**
 * RpcContext Test
 */
public class RpcContextTest {

    @Test
    void storeServiceContext() {
        RpcContext.RestoreServiceContext restoreServiceContext;
        ServiceContext serviceContext = RpcContext.getServiceContext();
        serviceContext.putAttachment("k1", "v1");
        assertEquals("v1", serviceContext.getAttachmentString("k1"));

        restoreServiceContext = RpcContext.storeServiceContext();
        assertNotEquals(serviceContext, RpcContext.getServiceContext());
        assertEquals("v1", serviceContext.getAttachmentString("k1"));
        assertNotEquals("v1", RpcContext.getServiceContext().getAttachmentString("k1"));

        RpcContext.restoreServiceContext(restoreServiceContext);
        assertEquals(serviceContext, RpcContext.getServiceContext());
        assertEquals("v1", RpcContext.getServiceContext().getAttachmentString("k1"));

        restoreServiceContext = RpcContext.storeServiceContext(false);
        assertEquals(serviceContext, RpcContext.getServiceContext());
        assertEquals("v1", RpcContext.getServiceContext().getAttachmentString("k1"));

        RpcContext.restoreServiceContext(restoreServiceContext);
        assertEquals(serviceContext, RpcContext.getServiceContext());
        assertEquals("v1", RpcContext.getServiceContext().getAttachmentString("k1"));
    }

    @Test
    void asyncStoreServiceContext() throws InterruptedException {
        CountDownLatch countDownLatch = new CountDownLatch(1);
        AtomicBoolean asyncFailed = new AtomicBoolean(false);

        ServiceContext serviceContext = RpcContext.getServiceContext();
        serviceContext.putAttachment("k1", "v1");
        assertEquals("v1", serviceContext.getAttachmentString("k1"));

        new Thread(TtlRunnable.get(() -> {
            try {
                RpcContext.RestoreServiceContext restoreServiceContext;

                restoreServiceContext = RpcContext.storeServiceContext();
                assertNotEquals(serviceContext, RpcContext.getServiceContext());
                assertEquals("v1", serviceContext.getAttachmentString("k1"));
                assertNotEquals("v1", RpcContext.getServiceContext().getAttachmentString("k1"));

                RpcContext.getServiceContext().putAttachment("k2", "v2");
                assertEquals("v2", RpcContext.getServiceContext().getAttachmentString("k2"));

                // wait for parent thread remove
                TimeUnit.MILLISECONDS.sleep(200);
                assertNotEquals(serviceContext, RpcContext.getServiceContext());
                assertEquals("v2", RpcContext.getServiceContext().getAttachmentString("k2"));

                RpcContext.restoreServiceContext(restoreServiceContext);
                assertNotEquals(serviceContext, RpcContext.getServiceContext());
                assertEquals("v1", RpcContext.getServiceContext().getAttachmentString("k1"));
                assertNotEquals("v2", RpcContext.getServiceContext().getAttachmentString("k2"));
            } catch (Exception | AssertionError e) {
                asyncFailed.set(true);
                throw new RuntimeException(e);
            } finally {
                countDownLatch.countDown();
            }
        })).start();

        // wait for child thread k2 -> v2
        TimeUnit.MILLISECONDS.sleep(100);
        assertEquals(serviceContext, RpcContext.getServiceContext());
        assertEquals("v1", serviceContext.getAttachmentString("k1"));
        assertNotEquals("v2", serviceContext.getAttachmentString("k2"));

        RpcContext.removeServiceContext();
        assertNotEquals("v1", RpcContext.getServiceContext().getAttachmentString("k1"));
        assertNotEquals("v2", serviceContext.getAttachmentString("k2"));

        // wait for child thread restore
        TimeUnit.MILLISECONDS.sleep(500);
        assertNotEquals(serviceContext, RpcContext.getServiceContext());
        assertNotEquals("v1", RpcContext.getServiceContext().getAttachmentString("k1"));
        assertNotEquals("v2", serviceContext.getAttachmentString("k2"));

        countDownLatch.await();
        assertFalse(asyncFailed.get());
    }
}