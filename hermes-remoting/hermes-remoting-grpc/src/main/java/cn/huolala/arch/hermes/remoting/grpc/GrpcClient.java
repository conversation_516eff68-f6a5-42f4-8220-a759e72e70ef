package cn.huolala.arch.hermes.remoting.grpc;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.remoting.support.ClientAdapter;
import io.grpc.ManagedChannel;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Grpc Client
 * @see ManagedChannel
 */
public class GrpcClient extends ClientAdapter {
    private final ManagedChannel channel;
    private final AtomicBoolean closed = new AtomicBoolean(false);

    public GrpcClient(URL url, ManagedChannel channel) {
        super(url);
        this.channel = channel;
    }

    @Override
    public void close() {
        if (closed.compareAndSet(false, true)) {
            if (!channel.isShutdown()) {
                channel.shutdown();
            }
        }
    }

    @Override
    public boolean isClosed() {
        return closed.get() || channel.isShutdown() || channel.isTerminated();
    }
}
