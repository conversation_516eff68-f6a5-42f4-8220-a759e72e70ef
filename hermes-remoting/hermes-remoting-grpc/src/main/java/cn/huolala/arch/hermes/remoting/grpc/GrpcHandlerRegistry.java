package cn.huolala.arch.hermes.remoting.grpc;

import io.grpc.BindableService;
import io.grpc.HandlerRegistry;
import io.grpc.ServerMethodDefinition;
import io.grpc.ServerServiceDefinition;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Grpc HandlerRegistry<br/>
 * see io.grpc.internal.InternalHandlerRegistry
 */
public class GrpcHandlerRegistry extends HandlerRegistry {

    private final Map<String, ServerServiceDefinition> services = new ConcurrentHashMap<>();
    private final Map<String, ServerMethodDefinition<?, ?>> methods = new ConcurrentHashMap<>();

    public GrpcHandlerRegistry() {
    }

    /**
     * Returns the service definitions in this registry.
     */
    @Override
    public List<ServerServiceDefinition> getServices() {
        return Collections.unmodifiableList(new ArrayList<>(services.values()));
    }

    @Nullable
    @Override
    public ServerMethodDefinition<?, ?> lookupMethod(String methodName, @Nullable String authority) {
        return methods.get(methodName);
    }

    public void addService(BindableService bindableService, String key) {
        ServerServiceDefinition service = bindableService.bindService();
        services.put(key, service);
        for (ServerMethodDefinition<?, ?> method : service.getMethods()) {
            methods.put(method.getMethodDescriptor().getFullMethodName(), method);
        }
    }

    public void removeService(String serviceKey) {
        ServerServiceDefinition service = services.remove(serviceKey);
        if (null != service) {
            for (ServerMethodDefinition<?, ?> method : service.getMethods()) {
                methods.remove(method.getMethodDescriptor().getFullMethodName(), method);
            }
        }
    }
}
