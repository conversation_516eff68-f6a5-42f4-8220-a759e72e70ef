package cn.huolala.arch.hermes.remoting.grpc;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.remoting.exception.RemotingException;
import cn.huolala.arch.hermes.remoting.support.ServerAdapter;
import io.grpc.Server;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Grpc Server
 *
 * @see Server
 * @see GrpcHandlerRegistry
 */
public class GrpcServer extends ServerAdapter {
    private final Server originalServer;
    private final GrpcHandlerRegistry handlerRegistry;

    private final AtomicBoolean started = new AtomicBoolean(false);
    private final AtomicBoolean closed = new AtomicBoolean(false);

    public GrpcServer(URL url, Server server, GrpcHandlerRegistry handlerRegistry) {
        super(url);
        this.originalServer = server;
        this.handlerRegistry = handlerRegistry;
    }

    @Override
    public void start() throws RemotingException {
        if (started.compareAndSet(false, true)) {
            try {
                originalServer.start();
            } catch (IOException e) {
                throw new RemotingException("Starting gRPC server failed. ", e);
            }
        }
    }

    @Override
    public Object getDelegateServer() {
        return originalServer;
    }

    @Override
    public boolean isStarted() {
        return started.get();
    }

    @Override
    public void close() {
        if (closed.compareAndSet(false, true)) {
            originalServer.shutdown();
        }
    }

    @Override
    public boolean isClosed() {
        return closed.get();
    }

    public GrpcHandlerRegistry getRegistry() {
        return handlerRegistry;
    }
}