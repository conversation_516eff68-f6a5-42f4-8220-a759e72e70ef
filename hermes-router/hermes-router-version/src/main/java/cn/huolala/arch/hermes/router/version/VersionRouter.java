package cn.huolala.arch.hermes.router.version;

import cn.huolala.arch.hermes.cluster.router.RouteException;
import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.protocol.exception.RpcException;
import cn.huolala.arch.hermes.router.api.AbstractRouter;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.huolala.arch.hermes.common.constants.Constants.ALL;
import static cn.huolala.arch.hermes.common.constants.Constants.VERSION_TAG_KEY;

/**
 * Version Router
 */
public class VersionRouter extends AbstractRouter {
    private static final Logger logger = LoggerFactory.getLogger(VersionRouter.class);

    public VersionRouter(URL url) {
        super(url);
    }

    @Override
    public <T> List<Invoker<T>> route(List<Invoker<T>> invokers, URL url, Invocation invocation) throws RpcException {
        String version = invocation.getVersion();
        List<Invoker<T>> filteredInvokers = invokers.stream().filter(invoker -> {
            URL invokerUrl = invoker.getUrl();
            if (StringUtils.equals(version, ALL)
                    || StringUtils.equals(version, invokerUrl.getParameter(VERSION_TAG_KEY))) {
                return true;
            }
            if (logger.isDebugEnabled()) {
                logger.debug(String.format("Invoker for service: [%s], Address: [%s]. Filtered by version: [%s]", 
                        invokerUrl.getServiceKey(), invokerUrl.getAddress(), version));
            }
            return false;
        }).collect(Collectors.toList());

        return Optional.ofNullable(filteredInvokers).filter(CollectionUtils::isNotEmpty)
                .orElseThrow(() -> new RouteException(RpcException.NO_INVOKER_AVAILABLE, "No available invoker for version: " + version + ", remoteAppId:" + invocation.getRemoteApplication()));
    }
}
