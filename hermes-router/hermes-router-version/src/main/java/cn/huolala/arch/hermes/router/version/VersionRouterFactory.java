package cn.huolala.arch.hermes.router.version;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.extension.Activate;
import cn.huolala.arch.hermes.router.api.AbstractRouterFactory;
import cn.huolala.arch.hermes.router.api.Router;

import static cn.huolala.arch.hermes.common.rule.RouterConstants.ROUTER_ORDER_VERSION;


/**
 * Version RouterFactory
 */
@Activate(order = ROUTER_ORDER_VERSION)
public class VersionRouterFactory extends AbstractRouterFactory {
    public static final String NAME = "version";

    protected Router createRouter(URL url) {
        return new VersionRouter(url);
    }
}
