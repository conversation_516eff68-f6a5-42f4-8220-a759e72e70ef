package cn.huolala.arch.hermes.router.version;

import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.discovery.tag.TagLoader;

import java.util.Map;
import java.util.Optional;

import static cn.huolala.arch.hermes.common.constants.Constants.VERSION_TAG_KEY;

public class VersionTagLoader implements TagLoader {

    @Override
    public Map<String, String> get() {
        Map<String, String> tags = CollectionUtils.emptyHashMap();
        Optional.ofNullable(ApplicationContext.getApplicationConfig().getVersion())
                .ifPresent(version -> tags.put(VERSION_TAG_KEY, version));
        return tags;
    }

}
