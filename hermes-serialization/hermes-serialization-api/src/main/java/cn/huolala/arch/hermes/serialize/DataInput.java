package cn.huolala.arch.hermes.serialize;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;

import java.io.IOException;

/**
 * Basic data type input interface.
 */
@ApiAudience.Private
public interface DataInput {

    /**
     * Read boolean.
     */
    boolean readBool() throws IOException;

    /**
     * Read byte.
     */
    byte readByte() throws IOException;

    /**
     * Read short integer.
     */
    short readShort() throws IOException;

    /**
     * Read integer.
     */
    int readInt() throws IOException;

    /**
     * Read long.
     */
    long readLong() throws IOException;

    /**
     * Read float.
     */
    float readFloat() throws IOException;

    /**
     * Read double.
     */
    double readDouble() throws IOException;

    /**
     * Read UTF-8 string.
     */
    String readUTF() throws IOException;

    /**
     * Read byte array.
     */
    byte[] readBytes() throws IOException;
}
