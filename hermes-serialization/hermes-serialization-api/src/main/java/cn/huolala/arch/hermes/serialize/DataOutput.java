package cn.huolala.arch.hermes.serialize;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;

import java.io.IOException;

/**
 * Basic data type output interface.
 */
@ApiAudience.Private
public interface DataOutput {

    /**
     * Write boolean.
     */
    void writeBool(boolean v) throws IOException;

    /**
     * Write byte.
     */
    void writeByte(byte v) throws IOException;

    /**
     * Write short.
     */
    void writeShort(short v) throws IOException;

    /**
     * Write integer.
     */
    void writeInt(int v) throws IOException;

    /**
     * Write long.
     */
    void writeLong(long v) throws IOException;

    /**
     * Write float.
     */
    void writeFloat(float v) throws IOException;

    /**
     * Write double.
     */
    void writeDouble(double v) throws IOException;

    /**
     * Write string.
     */
    void writeUTF(String v) throws IOException;

    /**
     * Write byte array.
     */
    void writeBytes(byte[] v) throws IOException;

    /**
     * Write byte array.
     *
     * @param v value.
     * @param off the start offset in the data.
     * @param len the number of bytes that are written.
     */
    void writeBytes(byte[] v, int off, int len) throws IOException;

    /**
     * Flush buffer.
     */
    void flushBuffer() throws IOException;
}
