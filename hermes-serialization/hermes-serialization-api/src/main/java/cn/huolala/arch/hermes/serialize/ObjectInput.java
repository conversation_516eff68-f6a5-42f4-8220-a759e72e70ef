package cn.huolala.arch.hermes.serialize;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.Map;

/**
 * Object input interface.
 */
@ApiAudience.Private
public interface ObjectInput extends DataInput {
    /**
     * read object
     *
     * @param cls object class
     * @return object
     * @throws IOException if an I/O error occurs
     */
    <T> T readObject(Class<T> cls) throws IOException;

    /**
     * read object
     *
     * @param cls object class
     * @param type object type
     * @return object
     * @throws IOException if an I/O error occurs
     */
    <T> T readObject(Class<T> cls, Type type) throws IOException;

    default Throwable readThrowable() throws IOException {
        Object obj = readObject(Object.class);
        if (!(obj instanceof Throwable)) {
            throw new IOException("Response data error, expect Throwable, but get " + obj);
        }
        return (Throwable) obj;
    }

    default Map<String, Object> readAttachments() throws IOException {
        //noinspection unchecked
        return readObject(Map.class);
    }
}
