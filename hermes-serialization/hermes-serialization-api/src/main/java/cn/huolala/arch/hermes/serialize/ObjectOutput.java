package cn.huolala.arch.hermes.serialize;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;

import java.io.IOException;
import java.util.Map;

/**
 * Object output interface.
 */
@ApiAudience.Private
public interface ObjectOutput extends DataOutput {

    /**
     * write object.
     */
    void writeObject(Object obj) throws IOException;

    default void writeThrowable(Object obj) throws IOException {
        writeObject(obj);
    }

    default void writeAttachments(Map<String, Object> attachments) throws IOException {
        writeObject(attachments);
    }
}
