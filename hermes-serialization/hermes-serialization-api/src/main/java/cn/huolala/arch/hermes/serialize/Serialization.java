package cn.huolala.arch.hermes.serialize;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.extension.Adaptive;
import cn.huolala.arch.hermes.common.extension.SPI;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Type;

/**
 * Serialization strategy interface that specifies a serializer
 */
@SPI
@ApiAudience.Private
public interface Serialization {
    byte SERIALIZATION_ID_JACKSON = 1;

    /**
     * Get content type unique id
     */
    byte getContentTypeId();

    /**
     * Get content type
     */
    String getContentType();

    /**
     * Get a serialization implementation instance
     */
    @Adaptive
    ObjectOutput serialize(URL url, OutputStream output) throws IOException;

    default byte[] serialize(URL url, Object obj) throws IOException {
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            ObjectOutput output = serialize(url, bos);
            output.writeObject(obj);
            output.flushBuffer();

            if (output instanceof Cleanable) {
                ((Cleanable) output).cleanup();
            }

            return bos.toByteArray();
        }
    }

    /**
     * Serialize object to string
     */
    default String serialize2String(URL url, Object obj) throws IOException {
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            ObjectOutput output = serialize(url, bos);
            output.writeObject(obj);
            output.flushBuffer();

            if (output instanceof Cleanable) {
                ((Cleanable) output).cleanup();
            }

            return bos.toString();
        }
    }


    /**
     * Get a deserialization implementation instance
     */
    @Adaptive
    ObjectInput deserialize(URL url, InputStream input) throws IOException;

    default <T> T deserializeString(URL url, String in, Class<T> cls) throws IOException {
        return deserializeString(url, in, cls, null);
    }


    default <T> T deserializeString(URL url, String in, Type type) throws IOException {
        return deserializeString(url, in, null, type);
    }

    /**
     * Deserialize string to object
     *
     * @param type Nullable
     */
    default <T> T deserializeString(URL url, String in, Class<T> cls, Type type) throws IOException {
        try (ByteArrayInputStream bis = new ByteArrayInputStream(in.getBytes())) {
            ObjectInput input = deserialize(url, bis);
            T obj = type == null ? input.readObject(cls) : input.readObject(cls, type);

            if (input instanceof Cleanable) {
                ((Cleanable) input).cleanup();
            }

            return obj;
        }
    }


    /**
     * Deserialize string to object
     *
     * @param type Nullable
     */
    default <T> T deserializeBytes(URL url, byte[] in, Class<T> cls, Type type) throws IOException {
        try (ByteArrayInputStream bis = new ByteArrayInputStream(in)) {
            ObjectInput input = deserialize(url, bis);
            T obj = type == null ? input.readObject(cls) : input.readObject(cls, type);

            if (input instanceof Cleanable) {
                ((Cleanable) input).cleanup();
            }

            return obj;
        }
    }
}
