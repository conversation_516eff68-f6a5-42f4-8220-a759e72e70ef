package cn.huolala.arch.hermes.serialize.jackson;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.serialize.ObjectInput;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.BufferedReader;
import java.io.EOFException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.lang.reflect.Type;

/**
 * Jackson ObjectInput implementation
 * @see ObjectMapper
 * @see BufferedReader
 */
@ApiAudience.Private
public class JacksonObjectInput implements ObjectInput {

    private final ObjectMapper objectMapper;
    private final BufferedReader reader;

    public JacksonObjectInput(ObjectMapper objectMapper, InputStream in) {
        this(objectMapper, new InputStreamReader(in));
    }

    public JacksonObjectInput(ObjectMapper objectMapper, Reader reader) {
        this.objectMapper = objectMapper;
        this.reader = new BufferedReader(reader);
    }

    @Override
    public boolean readBool() throws IOException {
        return readLine(boolean.class);
    }

    @Override
    public byte readByte() throws IOException {
        return readLine(byte.class);
    }

    @Override
    public short readShort() throws IOException {
        return readLine(short.class);
    }

    @Override
    public int readInt() throws IOException {
        return readLine(int.class);
    }

    @Override
    public long readLong() throws IOException {
        return readLine(long.class);
    }

    @Override
    public float readFloat() throws IOException {
        return readLine(float.class);
    }

    @Override
    public double readDouble() throws IOException {
        return readLine(double.class);
    }

    @Override
    public String readUTF() throws IOException {
        return readLine(String.class);
    }

    @Override
    public byte[] readBytes() throws IOException {
        return readLine().getBytes();
    }

    @Override
    public <T> T readObject(Class<T> cls) throws IOException {
        return objectMapper.readValue(reader, cls);
    }

    @Override
    public <T> T readObject(Class<T> cls, Type type) throws IOException {
        // more than readLine
        return objectMapper.readValue(reader, new TypeReference<T>() {
            @Override
            public Type getType() {
                return type;
            }
        });
    }

    private <T> T readLine(Class<T> cls) throws IOException {
        String json = readLine();
        return objectMapper.readValue(json, cls);
    }

    private String readLine() throws IOException {
        String line = reader.readLine();
        if (line == null || line.trim().length() == 0) {
            throw new EOFException();
        }
        return line;
    }
}
