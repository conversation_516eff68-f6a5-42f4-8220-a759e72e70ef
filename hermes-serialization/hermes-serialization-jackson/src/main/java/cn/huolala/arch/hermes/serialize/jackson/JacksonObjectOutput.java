package cn.huolala.arch.hermes.serialize.jackson;

import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.serialize.ObjectOutput;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.Writer;

/**
 * Jackson ObjectOutput implementation
 * @see ObjectMapper
 */
@ApiAudience.Private
public class JacksonObjectOutput implements ObjectOutput {

    private final ObjectMapper objectMapper;
    private final PrintWriter writer;

    public JacksonObjectOutput(ObjectMapper objectMapper, OutputStream out) {
        this(objectMapper, new OutputStreamWriter(out));
    }

    public JacksonObjectOutput(ObjectMapper objectMapper, Writer writer) {
        this.objectMapper = objectMapper;
        this.writer = new PrintWriter(writer);
    }

    @Override
    public void writeBool(boolean v) throws IOException {
        writeObject(v);
    }

    @Override
    public void writeByte(byte v) throws IOException {
        writeObject(v);
    }

    @Override
    public void writeShort(short v) throws IOException {
        writeObject(v);
    }

    @Override
    public void writeInt(int v) throws IOException {
        writeObject(v);
    }

    @Override
    public void writeLong(long v) throws IOException {
        writeObject(v);
    }

    @Override
    public void writeFloat(float v) throws IOException {
        writeObject(v);
    }

    @Override
    public void writeDouble(double v) throws IOException {
        writeObject(v);
    }

    @Override
    public void writeUTF(String v) throws IOException {
        writeObject(v);
    }

    @Override
    public void writeBytes(byte[] b) throws IOException {
        writer.println(new String(b));
    }

    @Override
    public void writeBytes(byte[] b, int off, int len) throws IOException {
        writer.println(new String(b, off, len));
    }

    @Override
    public void writeObject(Object obj) throws IOException {
        objectMapper.writeValue(writer, obj);
        writer.println();
        flushBuffer();
    }

    @Override
    public void flushBuffer() throws IOException {
        writer.flush();
    }
}
