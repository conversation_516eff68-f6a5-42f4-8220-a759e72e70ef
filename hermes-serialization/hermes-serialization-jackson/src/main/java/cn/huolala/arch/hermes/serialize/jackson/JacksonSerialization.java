package cn.huolala.arch.hermes.serialize.jackson;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.sink.common.constants.SinkConstants;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.serialize.ObjectInput;
import cn.huolala.arch.hermes.serialize.ObjectOutput;
import cn.huolala.arch.hermes.serialize.Serialization;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Map;
import java.util.Optional;

/**
 * Jackson Serialization
 */
@ApiAudience.Private
public class JacksonSerialization implements Serialization {
    @Override
    public byte getContentTypeId() {
        return SERIALIZATION_ID_JACKSON;
    }

    @Override
    public String getContentType() {
        return "text/json";
    }

    @Override
    public ObjectOutput serialize(URL url, OutputStream output) throws IOException {
        return new JacksonObjectOutput(getObjectMapper(url), output);
    }

    @Override
    public ObjectInput deserialize(URL url, InputStream input) throws IOException {
        return new JacksonObjectInput(getObjectMapper(url), input);
    }

    private ObjectMapper getObjectMapper(URL url) {
        return getContextObjectMapper(url).orElseGet(this::genObjectMapper);
    }

    /**
     * get ObjectMapper from global context
     */
    private Optional<ObjectMapper> getContextObjectMapper(URL url) {
        Map<String, Object> attributes = ApplicationContext.getAttributes();
        Object mapper = attributes.get(ObjectMapper.class.getName());

        if(url != null) {
            String appId = url.getParameter(Constants.REMOTE_APPLICATION_KEY);
            if(StringUtils.isNotEmpty(appId)) {
                Object customMapper = getCustomizeObjectMapper(appId, attributes);
                if(customMapper instanceof ObjectMapper) {
                    mapper = customMapper;
                }
            }
        }

        return Optional.ofNullable(mapper)
                .filter(obj -> obj instanceof ObjectMapper)
                .map(obj -> (ObjectMapper) obj);
    }


    /**
     * getCustomizeObjectMapper
     * @param appId
     * @return
     */
    private Object getCustomizeObjectMapper(String appId, Map<String, Object> attributes) {
        if(StringUtils.isEmpty(appId)) {
            return null;
        }

        return attributes.get(String.format(SinkConstants.CONSUMER_APP_SERIALIZATION_KEY, appId));
    }

    private ObjectMapper genObjectMapper() {
        return ObjectMapperHolder.objectMapper;
    }

    /**
     * ObjectMapper Holder
     **/
    private static class ObjectMapperHolder {
        private static final ObjectMapper objectMapper;

        static {
            ObjectMapper mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

            mapper.configure(SerializationFeature.WRAP_ROOT_VALUE, false);
            mapper.configure(SerializationFeature.INDENT_OUTPUT, true);
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

            objectMapper = mapper;
        }
    }
}
