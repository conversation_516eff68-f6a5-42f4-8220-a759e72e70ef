package cn.huolala.arch.hermes.serialize.jackson;

import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.metadata.MetadataSupplier;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

public class JacksonSerializationMetadataSupplier implements MetadataSupplier {
    @Override
    public String getDesc() {
        return "序列化ObjectMapper属性";
    }

    @Override
    public Map<String, Object> get() {

        Object mapper = ApplicationContext.getAttributes().get(ObjectMapper.class.getName());

        ObjectMapper objectMapper = Optional.ofNullable(mapper).filter(obj -> obj instanceof ObjectMapper).map(obj -> (ObjectMapper) obj).get();

        Set<SerializationFeature> serializationFeatures = Arrays.stream(SerializationFeature.values()).filter(serializationFeature -> objectMapper.getSerializationConfig().hasSerializationFeatures(serializationFeature.getMask())).collect(Collectors.toSet());
        Set<MapperFeature> serializationMapperFeatures = Arrays.stream(MapperFeature.values()).filter(mapperFeature -> objectMapper.getSerializationConfig().hasMapperFeatures(mapperFeature.getMask())).collect(Collectors.toSet());

        Set<DeserializationFeature> deserializationFeatures = Arrays.stream(DeserializationFeature.values()).filter(deserializationFeature -> objectMapper.getDeserializationConfig().hasDeserializationFeatures(deserializationFeature.getMask())).collect(Collectors.toSet());
        Set<MapperFeature> deserializationMapperFeatures = Arrays.stream(MapperFeature.values()).filter(mapperFeature -> objectMapper.getDeserializationConfig().hasMapperFeatures(mapperFeature.getMask())).collect(Collectors.toSet());

        Map<String, Object> metadataInfo = new HashMap<>();
        metadataInfo.put("serializationFeatures", serializationFeatures);
        metadataInfo.put("serializationMapperFeatures", serializationMapperFeatures);
        metadataInfo.put("deserializationFeatures", deserializationFeatures);
        metadataInfo.put("deserializationMapperFeatures", deserializationMapperFeatures);
        metadataInfo.put("registeredModuleIds", objectMapper.getRegisteredModuleIds());

        return metadataInfo;
    }

}
