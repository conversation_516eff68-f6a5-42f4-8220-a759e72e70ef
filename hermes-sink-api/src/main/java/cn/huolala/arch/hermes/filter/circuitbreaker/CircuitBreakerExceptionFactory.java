package cn.huolala.arch.hermes.filter.circuitbreaker;

import cn.huolala.arch.hermes.common.extension.SPI;

import java.util.function.Predicate;

@SPI
public interface CircuitBreakerExceptionFactory {

    Predicate<Throwable> DEFAULT_RECORD_EXCEPTION_PREDICATE = throwable -> false;

    default Predicate<Throwable> recordExceptionPredicate() {
        return DEFAULT_RECORD_EXCEPTION_PREDICATE;
    }

    default Class<? extends Throwable>[] recordExceptions() {
        return new Class[0];
    }

}
