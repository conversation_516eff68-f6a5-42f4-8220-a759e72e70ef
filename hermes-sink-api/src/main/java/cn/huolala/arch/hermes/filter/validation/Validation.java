package cn.huolala.arch.hermes.filter.validation;

import cn.huolala.arch.hermes.common.URL;
import cn.huolala.arch.hermes.common.extension.Adaptive;
import cn.huolala.arch.hermes.common.extension.SPI;

import static cn.huolala.arch.hermes.common.constants.SpiConstants.VALIDATION_DEFAULT;

/**
 * Instance of Validation interface provide instance of {@link Validator} based on the value of <b>validation</b> attribute.
 */
@SPI("jvalidation")
public interface Validation {

    /**
     * Return the instance of {@link Validator} for a given url.
     * @param url Invocation url
     * @return Instance of {@link Validator}
     */
    @Adaptive(VALIDATION_DEFAULT)
    Validator getValidator(URL url);

}
