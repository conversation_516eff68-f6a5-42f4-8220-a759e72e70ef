package cn.huolala.arch.hermes.router.group;

import cn.huolala.arch.hermes.common.extension.SPI;
import cn.huolala.arch.hermes.protocol.Invocation;
import cn.huolala.arch.hermes.protocol.Invoker;
import cn.huolala.arch.hermes.spec.classification.ApiAudience;
import cn.huolala.arch.hermes.spec.classification.ApiStability;

import java.util.List;
import java.util.Optional;

/**
 * GroupRouter Ext SPI
 *
 */
@SPI(internal = false)
@ApiAudience.Public
@ApiStability.Evolving
public interface GroupRouterExt {
    /**
     * Gets the pinned group from current invokers and context
     *
     * @param invokers unmodifiable invokers
     * @param invocation unmodifiable invocation
     * @return Pinned Group
     */
    <T> Optional<PinnedGroup> getGroup(List<Invoker<T>> invokers, Invocation invocation);

    /**
     * Pinned Group
     */
    class PinnedGroup {
        private String group;

        /**
         * Whether to allow cross group invoke when there is no matching group
         * <br/>
         * default false
         */
        private boolean cross;

        public PinnedGroup() {
        }

        public PinnedGroup(String group, boolean cross) {
            this.group = group;
            this.cross = cross;
        }

        public String getGroup() {
            return group;
        }

        public void setGroup(String group) {
            this.group = group;
        }

        public boolean isCross() {
            return cross;
        }

        public void setCross(boolean cross) {
            this.cross = cross;
        }
    }
}
