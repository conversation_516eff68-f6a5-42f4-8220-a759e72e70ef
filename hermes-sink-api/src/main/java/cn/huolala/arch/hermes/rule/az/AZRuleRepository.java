package cn.huolala.arch.hermes.rule.az;


import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.extension.SPI;

/**
 * az路由规则仓库
 */
@SPI(Constants.DEFAULT_KEY)
public interface AZRuleRepository {

    /**
     * 客户端是否开启zone路由开关
     *
     * @return zone路由开关
     */
    boolean isAZRouteEnable();

    /**
     * appId是否在当前请求泳道部署
     *
     * @return 当前appId是否在当前请求泳道部署
     */
    boolean appIdInZone(String appId, String zone);

    /**
     * 是否需要补全泳道标识信息
     * @return
     */
    boolean isFixheader();

    static AZRuleRepository getDefaultExtension() {
        return ExtensionLoader.getExtensionLoader(AZRuleRepository.class).getDefaultExtension();
    }

}
