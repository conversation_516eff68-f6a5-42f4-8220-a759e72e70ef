package cn.huolala.arch.hermes.rule.gray;

import cn.huolala.arch.hermes.common.constants.Constants;
import cn.huolala.arch.hermes.common.event.EventDispatcher;
import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.extension.SPI;

import java.util.Map;
import java.util.Set;

@SPI(Constants.DEFAULT_KEY)
public interface GrayRuleRepository {

    Map<String, Set<String>> grayVersion();

    boolean isGrayRoute(String grayVersion);

    boolean inGrayRoute(String grayVersion, String application);

    /**
     * The default extension of {@link EventDispatcher}
     */
    static GrayRuleRepository getDefaultExtension() {
        return ExtensionLoader.getExtensionLoader(GrayRuleRepository.class).getDefaultExtension();
    }

}
