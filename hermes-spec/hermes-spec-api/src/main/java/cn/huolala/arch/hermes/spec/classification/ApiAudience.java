package cn.huolala.arch.hermes.spec.classification;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@ApiAudience.Public
@ApiStability.Stable
public final class ApiAudience {

    /**
     * Intended for use by any project or application.
     */
    @Documented
    @Retention(RetentionPolicy.RUNTIME)
    public @interface Public {
    }

    /**
     * Intended for use only within the project(s) specified in the annotation.
     * For example, "Monitor", "cache" , "orm" etc other JAF components
     */
    @Documented
    @Retention(RetentionPolicy.RUNTIME)
    public @interface LimitedPrivate {
        String[] value() default {};
    }

    /**
     * Intended for use only within the project itself.
     */
    @Documented
    @Retention(RetentionPolicy.RUNTIME)
    public @interface Private {
    }

    /**
     * Intended for use only in the test code.
     */
    @Documented
    @Retention(RetentionPolicy.CLASS)
    @Target({ElementType.TYPE, ElementType.METHOD, ElementType.FIELD, ElementType.CONSTRUCTOR})
    public @interface ForTest {
    }

    // Audience can't exist on its own
    private ApiAudience() {
    }

}