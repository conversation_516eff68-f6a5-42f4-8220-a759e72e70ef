package cn.huolala.arch.hermes.spec.classification;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@ApiAudience.Public
@ApiStability.Stable
public final class ApiStability {

    // Given a version number MAJOR.MINOR.PATCH

    /**
     * Can evolve while retaining compatibility for minor release boundaries.;
     * can break compatibility only at major release (ie. at m.0).
     */
    @Documented
    @Retention(RetentionPolicy.RUNTIME)
    public @interface Stable {
    }

    /**
     * Evolving, but can break compatibility at minor release (i.e. m.x)
     */
    @Documented
    @Retention(RetentionPolicy.RUNTIME)
    public @interface Evolving {
    }

    /**
     * No guarantee is provided as to reliability or stability across any
     * level of release granularity.
     */
    @Documented
    @Retention(RetentionPolicy.RUNTIME)
    public @interface Unstable {
    }


    /**
     * A program element annotated &#64;Deprecated is one that programmers
     * are discouraged from using, typically because it is dangerous,
     * or because a better alternative exists.
     */
    @Documented
    @Retention(RetentionPolicy.RUNTIME)
    public @interface Deprecated {
    }

    private ApiStability() {
    }

}
