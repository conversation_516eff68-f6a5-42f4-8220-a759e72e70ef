package cn.huolala.arch.hermes.spring.boot.actuate;

import cn.huolala.arch.hermes.common.context.support.HealthIndicator;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.boot.actuate.health.HealthComponent;
import org.springframework.boot.actuate.health.HealthEndpoint;
import org.springframework.context.ApplicationContext;

import static cn.huolala.arch.hermes.common.context.ApplicationContext.getAttributes;
import static org.springframework.boot.actuate.health.Status.UP;

/**
 * HealthIndicator SpringBoot impl
 *
 * @see cn.huolala.arch.hermes.common.context.support.HealthIndicator
 */
public class SpringBootHealthIndicator implements HealthIndicator {
    private static final Logger logger = LoggerFactory.getLogger(SpringBootHealthIndicator.class);

    @Override
    public Status health() {
        try {
            ApplicationContext applicationContext = (ApplicationContext) getAttributes().get(ApplicationContext.class.getName());
            HealthComponent health = applicationContext.getBean(HealthEndpoint.class).health();
            return UP.equals(health.getStatus()) ? Status.UP : Status.DOWN;
        } catch (NullPointerException | ClassCastException | BeansException e) {
            logger.warn("spring boot HealthIndicator check exception", e);
            return Status.UP;
        }
    }
}
