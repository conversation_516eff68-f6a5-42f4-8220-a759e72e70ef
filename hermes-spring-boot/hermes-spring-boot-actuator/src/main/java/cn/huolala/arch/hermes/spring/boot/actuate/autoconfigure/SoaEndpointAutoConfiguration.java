package cn.huolala.arch.hermes.spring.boot.actuate.autoconfigure;


import cn.huolala.arch.hermes.spring.boot.actuate.endpoint.meta.MetaDataEndPoint;
import cn.huolala.arch.hermes.spring.boot.actuate.filter.EndpointSecurityFilter;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

import static cn.huolala.arch.hermes.spring.boot.actuate.filter.EndpointSecurityFilter.URL_PATTERNS_ACTUATOR;

@Configuration(proxyBeanMethods = false)
public class SoaEndpointAutoConfiguration {

    @Bean
    public MetaDataEndPoint metaDataEndPoint() {
        return new MetaDataEndPoint();
    }

    @Bean
    public FilterRegistrationBean<EndpointSecurityFilter> endpointSecurityFilter() {
        FilterRegistrationBean<EndpointSecurityFilter> filterBean = new FilterRegistrationBean<>();
        filterBean.setFilter(new EndpointSecurityFilter());
        filterBean.addUrlPatterns(URL_PATTERNS_ACTUATOR);
        filterBean.setEnabled(true);
        filterBean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return filterBean;
    }

}
