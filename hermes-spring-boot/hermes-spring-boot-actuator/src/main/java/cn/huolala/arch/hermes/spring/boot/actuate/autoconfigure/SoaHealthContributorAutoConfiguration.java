package cn.huolala.arch.hermes.spring.boot.actuate.autoconfigure;

import cn.huolala.arch.hermes.spring.boot.actuate.endpoint.health.SoaHealthIndicator;

import org.springframework.boot.actuate.autoconfigure.endpoint.condition.ConditionalOnAvailableEndpoint;
import org.springframework.boot.actuate.autoconfigure.health.ConditionalOnEnabledHealthIndicator;
import org.springframework.boot.actuate.autoconfigure.health.HealthEndpointAutoConfiguration;
import org.springframework.boot.actuate.health.HealthEndpoint;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@ConditionalOnAvailableEndpoint(endpoint = HealthEndpoint.class)
@ConditionalOnEnabledHealthIndicator("soa")
@AutoConfigureAfter(HealthEndpointAutoConfiguration.class)
public class SoaHealthContributorAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean(name = "soaHealthIndicator")
    public SoaHealthIndicator soaHealthIndicator() {
        return new SoaHealthIndicator();
    }
}