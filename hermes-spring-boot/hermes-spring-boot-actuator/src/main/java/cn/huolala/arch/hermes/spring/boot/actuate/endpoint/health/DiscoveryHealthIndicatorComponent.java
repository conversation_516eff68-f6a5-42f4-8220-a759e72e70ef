package cn.huolala.arch.hermes.spring.boot.actuate.endpoint.health;

import cn.huolala.arch.hermes.api.config.DiscoveryConfig;
import cn.huolala.arch.hermes.api.config.ProviderConfig;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.event.EventDispatcher;
import cn.huolala.arch.hermes.common.event.EventListener;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.metadata.event.AppRunTimeEvent;
import cn.huolala.arch.hermes.metadata.event.AppRunTimeEvent.EventType;

import org.springframework.boot.actuate.health.Status;
import org.springframework.util.ObjectUtils;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static cn.huolala.arch.hermes.metadata.event.AppRunTimeEvent.EVENT_TYPE_KEY;
import static cn.huolala.arch.hermes.metadata.event.AppRunTimeEvent.EventType.REGISTRY;
import static cn.huolala.arch.hermes.metadata.event.AppRunTimeEvent.EventType.RegistrySubEventType.RECONNECTION;
import static cn.huolala.arch.hermes.metadata.event.AppRunTimeEvent.EventType.RegistrySubEventType.UP;
import static cn.huolala.arch.hermes.metadata.event.AppRunTimeEvent.MEMO_KEY;
import static cn.huolala.arch.hermes.metadata.event.AppRunTimeEvent.SUCCESS;

@SuppressWarnings("rawtypes")
public class DiscoveryHealthIndicatorComponent
        implements EventListener<AppRunTimeEvent>, HealthIndicatorComponent {
    private static final Logger logger = LoggerFactory.getLogger(SoaHealthIndicator.class);

    private static final Set<EventType> EVENT_TYPES = new HashSet<>();

    private static final Set<String> SUB_EVENT_TYPES = new HashSet<>();

    public static final String COMPONENT_NAME = "discovery";

    private final boolean register;

    private Status status;

    static {
        EVENT_TYPES.add(REGISTRY);

        SUB_EVENT_TYPES.add(RECONNECTION.toString());
        SUB_EVENT_TYPES.add(UP.toString());
    }

    public DiscoveryHealthIndicatorComponent() {
        EventDispatcher.getDefaultExtension().addEventListener(this);

        this.register = ApplicationContext.getConfigManager().getProvider()
                .map(ProviderConfig::getDiscoveries)
                .map(discoveries -> discoveries.iterator().next())
                .map(DiscoveryConfig::getRegister)
                .orElse(true);

        this.status = this.register ? Status.OUT_OF_SERVICE : Status.UP;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void onEvent(AppRunTimeEvent event) {
        if (!register || !EVENT_TYPES.contains(event.getType())) {
            return;
        }
        Map<String, Object> data = (Map<String, Object>) event.getData();
        Object subType = data.get(EVENT_TYPE_KEY);
        Object result = data.get(MEMO_KEY);
        if (SUB_EVENT_TYPES.contains(subType)) {
            if (ObjectUtils.nullSafeEquals(result, SUCCESS)) {
                status = Status.UP;
            } else {
                status = Status.OUT_OF_SERVICE;
            }
            if (logger.isWarnEnabled()) {
                logger.warn("Recd eventType:" + subType + ", Current status:" + status);
            }
        }
    }

    @Override
    public String name() {
        return COMPONENT_NAME;
    }

    @Override
    public Status status() {
        return status;
    }
}
