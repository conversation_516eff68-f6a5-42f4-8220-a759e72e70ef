package cn.huolala.arch.hermes.spring.boot.actuate.endpoint.health;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.AbstractHealthIndicator;
import org.springframework.boot.actuate.health.Health.Builder;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.actuate.health.Status;
import org.springframework.boot.actuate.health.StatusAggregator;

/**
 *  {@link HealthIndicator} for a SOA
 */
public class SoaHealthIndicator extends AbstractHealthIndicator {
    private final Set<HealthIndicatorComponent> components = new HashSet<>();
    
    @Autowired
    private StatusAggregator aggregator;
    
    @PostConstruct
    public void init() {
        // Add health indicator component
        components.add(new DiscoveryHealthIndicatorComponent());
    }

    @Override
    protected void doHealthCheck(Builder builder) throws Exception {
        Map<String, Status> statuses = components.stream()
                .collect(Collectors.toMap(HealthIndicatorComponent::name, HealthIndicatorComponent::status));
        builder.status(aggregator.getAggregateStatus(new HashSet<>(statuses.values()))).withDetails(statuses);
    }
}
