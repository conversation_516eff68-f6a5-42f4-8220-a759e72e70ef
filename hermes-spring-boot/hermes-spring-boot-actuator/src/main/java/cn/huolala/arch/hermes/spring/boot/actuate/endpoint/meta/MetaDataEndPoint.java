package cn.huolala.arch.hermes.spring.boot.actuate.endpoint.meta;

import cn.huolala.arch.hermes.common.extension.ExtensionLoader;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.metadata.MetadataSupplier;


import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.boot.actuate.endpoint.annotation.Selector;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Endpoint(id = "hermes-metadata")
public class MetaDataEndPoint {

    private static final Logger logger = LoggerFactory.getLogger(MetaDataEndPoint.class);

    private static final String HELP_KEY = "help";
    private static boolean initialized;

    public static Map<String, Object> configCode = new HashMap<>();

    @ReadOperation
    public Map<String, Object> getMetadataInfo(@Selector String path) {

        if (!initialized) {
            initHelp();
            initialized = true;
        }

        if (HELP_KEY.equalsIgnoreCase(path) || !configCode.containsKey(path)) {
            return configCode;
        }

        try {
            MetadataSupplier metadataSupplier = ExtensionLoader.getExtensionLoader(MetadataSupplier.class).getExtension(path);
            if (null != metadataSupplier) {
                return metadataSupplier.get();
            }
        } catch (Throwable e) {
            logger.error("MetaDataEndPoint exception", e);
        }

        return configCode;

    }

    private void initHelp() {
        Set<MetadataSupplier> metadataSuppliers = ExtensionLoader.getExtensionLoader(MetadataSupplier.class).getSupportedExtensionInstances();
        configCode = metadataSuppliers.stream().collect(Collectors.toMap(MetadataSupplier::getCode, MetadataSupplier::getDesc, (k1, k2) -> k1));
    }

}
