package cn.huolala.arch.hermes.spring.boot.actuate.filter;

import cn.huolala.arch.hermes.common.config.AbstractGovernanceConfiguration;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigEvent;
import cn.huolala.arch.hermes.common.config.dynamic.event.ConfigEventType;
import cn.huolala.arch.hermes.common.context.ApplicationContext;
import cn.huolala.arch.hermes.common.event.EventDispatcher;
import cn.huolala.arch.hermes.common.event.MetricsEvent;
import cn.huolala.arch.hermes.common.logger.Logger;
import cn.huolala.arch.hermes.common.logger.LoggerFactory;
import cn.huolala.arch.hermes.common.util.CollectionUtils;
import cn.huolala.arch.hermes.common.util.StringUtils;
import cn.huolala.arch.hermes.spring.boot.actuate.util.DigestUtils;
import cn.huolala.arch.hermes.spring.boot.actuate.util.IpUtils;
import io.micrometer.core.instrument.Tag;
import org.springframework.http.HttpHeaders;
import org.springframework.web.client.HttpClientErrorException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.huolala.arch.hermes.common.constants.Constants.COMMA_SEPARATOR;
import static cn.huolala.arch.hermes.metrics.MetricsConstants.METRIC_ENDPOINT_AUTH_COUNT;
import static cn.huolala.arch.hermes.spring.boot.actuate.filter.EndpointSecurityFilter.EndpointAuthMetricEnum.EMPTY_PARAMETER;
import static cn.huolala.arch.hermes.spring.boot.actuate.filter.EndpointSecurityFilter.EndpointAuthMetricEnum.FAIL;
import static cn.huolala.arch.hermes.spring.boot.actuate.filter.EndpointSecurityFilter.EndpointAuthMetricEnum.ILLEGAL_NONCE;
import static cn.huolala.arch.hermes.spring.boot.actuate.filter.EndpointSecurityFilter.EndpointAuthMetricEnum.ILLEGAL_PARAMETER;
import static cn.huolala.arch.hermes.spring.boot.actuate.filter.EndpointSecurityFilter.EndpointAuthMetricEnum.ILLEGAL_PATH;
import static cn.huolala.arch.hermes.spring.boot.actuate.filter.EndpointSecurityFilter.EndpointAuthMetricEnum.ILLEGAL_SIGN;
import static cn.huolala.arch.hermes.spring.boot.actuate.filter.EndpointSecurityFilter.EndpointAuthMetricEnum.ILLEGAL_TIMESTAMP;
import static cn.huolala.arch.hermes.spring.boot.actuate.filter.EndpointSecurityFilter.EndpointAuthMetricEnum.SUCCESS;
import static org.springframework.http.HttpStatus.BAD_REQUEST;

public class EndpointSecurityFilter extends AbstractGovernanceConfiguration implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(EndpointSecurityFilter.class);

    private static final List<String> FORBIDDEN_SEMICOLON = Collections.unmodifiableList(Arrays.asList(";", "%3b", "%3B"));
    private static final List<String> FORBIDDEN_ENCODED_PERIOD = Collections
            .unmodifiableList(Arrays.asList("..", "%2e", "%2E"));

    private static final List<String> FORBIDDEN_SPECIAL_CHARACTERS = Collections
            .unmodifiableList(Arrays.asList("./", "//"));

    private Set<String> encodedUrlBlocklist = new HashSet<>();

    public static final String URL_PATTERNS_ACTUATOR = "/actuator/*";
    public static final String PATH_SEPARATOR = "/";
    private static final long DEFAULT_AUTH_TIME_TOLERANCE_IN_MILLIS = 60 * 1000L;//60s

    public static final String NONCE_KEY = "nonce";
    public static final String TIMESTAMP_KEY = "timestamp";

    private static final String AUTH_ID_KEY = "id";
    private static final String AUTH_SECRET_KEY = "secret";
    private static final String AUTH_TIME_TOLERANCE_KEY = "timeTolerance";

    private boolean initialized;

    private Set<String> globalAuthEndpoint = Collections.emptySet();
    private String globalAuthSecret = StringUtils.EMPTY_STRING;

    private Set<String> appAuthEndpoint = Collections.emptySet();
    private String appAuthSecret = StringUtils.EMPTY_STRING;

    private long globalAuthTimeToleranceInMillis = 0L;
    private long appAuthTimeToleranceInMillis = 0L;

    // caches
    private final ConcurrentHashMap<String, Long> cachedNonces = new ConcurrentHashMap<>();

    private final EventDispatcher eventDispatcher = EventDispatcher.getDefaultExtension();

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        init();

        HttpServletRequest httpServletRequest = (HttpServletRequest) request;

        rejectedBlacklistedUrls(httpServletRequest);

        checkEndpointAuth(httpServletRequest);

        endpointAuthMetric(httpServletRequest, SUCCESS.code, SUCCESS);
        chain.doFilter(request, response);
    }

    private void init() {
        if (!initialized) {
            synchronized (EndpointSecurityFilter.class) {
                if (!initialized) {
                    initEndpoint();
                    startListen();
                    initialized = true;
                }
            }
        }
    }

    private void initEndpoint() {

        this.encodedUrlBlocklist.addAll(FORBIDDEN_SEMICOLON);
        this.encodedUrlBlocklist.addAll(FORBIDDEN_ENCODED_PERIOD);
        this.encodedUrlBlocklist.addAll(FORBIDDEN_SPECIAL_CHARACTERS);

        // 定时任务：每30S清理一次过期的 nonce
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1, r -> {
            Thread thread = new Thread(r);
            thread.setName("SOA-cleanExpiredNoncesThread");
            thread.setDaemon(true);
            thread.setPriority(Thread.NORM_PRIORITY);
            return thread;
        });
        scheduler.scheduleAtFixedRate(() -> cleanExpiredNonces(), 5, 30, TimeUnit.SECONDS);
    }

    // 清理过期的 nonce
    public void cleanExpiredNonces() {
        long currentTime = System.currentTimeMillis();
        long finalAuthTimeToleranceInMillis = getAuthTimeTolerance();
        cachedNonces.entrySet().removeIf(entry -> entry.getValue() + finalAuthTimeToleranceInMillis < currentTime);
    }

    @Override
    protected void onGlobalConfigChange(ConfigEvent event) {

        Optional<String> optionalKey = realGlobalConfigKey(event.getKey());

        if (!optionalKey.isPresent()) {
            logger.warn(getClass().getSimpleName() + " Unknown endpoint global rule key: " + event.getKey());
            return;
        }

        String key = optionalKey.get();
        if (AUTH_ID_KEY.equalsIgnoreCase(key)) {
            globalAuthEndpoint = processAuthId(event);
        } else if (AUTH_SECRET_KEY.equalsIgnoreCase(key)) {
            globalAuthSecret = processAuthSecret(event);
        } else if (AUTH_TIME_TOLERANCE_KEY.equalsIgnoreCase(key)) {
            globalAuthTimeToleranceInMillis = processAuthTimeTolerance(event);
        }

    }

    @Override
    protected void onAppConfigChange(ConfigEvent event) {
        Optional<String> optionalKey = realAppConfigKey(event.getKey());

        if (!optionalKey.isPresent()) {
            logger.warn(getClass().getSimpleName() + " Unknown endpoint app rule key: " + event.getKey());
            return;
        }

        String key = optionalKey.get();
        if (AUTH_ID_KEY.equalsIgnoreCase(key)) {
            appAuthEndpoint = processAuthId(event);
        } else if (AUTH_SECRET_KEY.equalsIgnoreCase(key)) {
            appAuthSecret = processAuthSecret(event);
        } else if (AUTH_TIME_TOLERANCE_KEY.equalsIgnoreCase(key)) {
            appAuthTimeToleranceInMillis = processAuthTimeTolerance(event);
        }

    }

    @Override
    protected void onRuleChange(ConfigEvent event) {
    }

    @Override
    protected String ruleKeyPrefix() {
        return "endpoint.auth";
    }

    private void rejectedBlacklistedUrls(HttpServletRequest request) {
        String path = request.getRequestURI();
        for (String forbidden : this.encodedUrlBlocklist) {
            if (valueContains(path, forbidden)) {
                endpointAuthMetric(request, FAIL.code, ILLEGAL_PATH);
                throw new HttpClientErrorException(BAD_REQUEST, "The request[" + path + "] was rejected because the URL contained a potentially malicious String \"" + forbidden + "\"");
            }
        }
    }

    private static boolean valueContains(String value, String contains) {
        return value != null && value.contains(contains);
    }

    private void checkEndpointAuth(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String[] paths = requestURI.split(PATH_SEPARATOR);

        if (paths.length <= 2) {
            return;
        }

        String endpointId = paths[2];

        Set<String> finalAuthCode = Collections.emptySet();
        if (CollectionUtils.isNotEmpty(appAuthEndpoint)) {
            finalAuthCode = appAuthEndpoint;
        } else if (CollectionUtils.isNotEmpty(globalAuthEndpoint)) {
            finalAuthCode = globalAuthEndpoint;
        }
        if (!finalAuthCode.contains(endpointId)) {
            return;
        }

        String timestamp = request.getHeader(TIMESTAMP_KEY);
        String nonce = request.getHeader(NONCE_KEY);
        String sign = request.getHeader(HttpHeaders.AUTHORIZATION);
        if (StringUtils.isAnyEmpty(timestamp, nonce, sign)) {
            endpointAuthMetric(request, FAIL.code, EMPTY_PARAMETER);
            // 抛出异常
            String errorMsg = "Endpoint [" + endpointId + "] Unauthorized,Authentication parameters missing,requestURI=" + requestURI;
            throw new HttpClientErrorException(BAD_REQUEST, errorMsg);
        }

        long requestTimeMillis;
        try {
            requestTimeMillis = Long.parseLong(timestamp);
        } catch (NumberFormatException e) {
            endpointAuthMetric(request, FAIL.code, ILLEGAL_PARAMETER);
            // 抛出异常
            String errorMsg = "Endpoint [" + endpointId + "] Unauthorized,Authentication parameters invalid,requestURI=" + requestURI;
            throw new HttpClientErrorException(BAD_REQUEST, errorMsg);
        }

        if (!checkTimestamp(requestTimeMillis)) {
            endpointAuthMetric(request, FAIL.code, ILLEGAL_TIMESTAMP);
            // 抛出异常
            String errorMsg = "Endpoint [" + endpointId + "] Unauthorized,Authentication Timestamp parameters invalid,requestURI=" + requestURI;
            throw new HttpClientErrorException(BAD_REQUEST, errorMsg);
        } else if (!checkNonce(requestTimeMillis, nonce)) {
            endpointAuthMetric(request, FAIL.code, ILLEGAL_NONCE);
            // 抛出异常
            String errorMsg = "Endpoint [" + endpointId + "] Unauthorized,Authentication Nonce parameters invalid,requestURI=" + requestURI;
            throw new HttpClientErrorException(BAD_REQUEST, errorMsg);
        } else if (!checkSign(requestTimeMillis, nonce, sign)) {
            endpointAuthMetric(request, FAIL.code, ILLEGAL_SIGN);
            // 抛出异常
            String errorMsg = "Endpoint [" + endpointId + "] Unauthorized,Authentication Sign parameters invalid,requestURI=" + requestURI;
            throw new HttpClientErrorException(BAD_REQUEST, errorMsg);
        }

    }

    private boolean checkTimestamp(long timestamp) {
        if (0 == timestamp) {
            return false;
        }
        return timestamp + getAuthTimeTolerance() > System.currentTimeMillis();
    }

    private boolean checkNonce(long timestamp, String nonce) {
        Long existedTimestamp = cachedNonces.putIfAbsent(nonce, timestamp);
        if (null != existedTimestamp && 0 != existedTimestamp && checkTimestamp(existedTimestamp)) {
            return false;
        }
        return true;
    }

    private boolean checkSign(long timestamp, String nonce, String sign) {
        String appId = ApplicationContext.getName();
        String secret = getAuthSecret();
        if (StringUtils.isBlank(secret)) {
            secret = DigestUtils.sha256Hex(StringUtils.reverse(appId));
        }
        String checkedSign = DigestUtils.sha256Hex(appId + secret + timestamp + nonce);

        return StringUtils.equals(sign, checkedSign);
    }

    private Set<String> processAuthId(ConfigEvent event) {

        if (event.getChangeType() == ConfigEventType.DELETED || StringUtils.isBlank(event.getContent())) {
            return Collections.emptySet();
        }

        return Arrays.stream(event.getContent().split(COMMA_SEPARATOR))
                .map(String::trim)
                .collect(Collectors.toSet());
    }

    private String processAuthSecret(ConfigEvent event) {

        if (event.getChangeType() == ConfigEventType.DELETED || StringUtils.isBlank(event.getContent())) {
            return StringUtils.EMPTY_STRING;
        }
        return event.getContent().trim();
    }

    private long processAuthTimeTolerance(ConfigEvent event) {

        if (event.getChangeType() == ConfigEventType.DELETED || StringUtils.isBlank(event.getContent())) {
            return 0L;
        }

        try {
            int authTimeToleranceSeconds = Integer.parseInt(event.getContent());
            return authTimeToleranceSeconds * 1000L;
        } catch (NumberFormatException e) {
            return 0L;
        }
    }

    private long getAuthTimeTolerance() {
        long finalAuthTimeToleranceInMillis = DEFAULT_AUTH_TIME_TOLERANCE_IN_MILLIS;
        if (appAuthTimeToleranceInMillis > 0) {
            finalAuthTimeToleranceInMillis = appAuthTimeToleranceInMillis;
        } else if (globalAuthTimeToleranceInMillis > 0) {
            finalAuthTimeToleranceInMillis = globalAuthTimeToleranceInMillis;
        }
        return finalAuthTimeToleranceInMillis;
    }

    private String getAuthSecret() {
        if (StringUtils.isNotBlank(appAuthSecret)) {
            return appAuthSecret;
        } else if (StringUtils.isNotBlank(globalAuthSecret)) {
            return globalAuthSecret;
        }
        return StringUtils.EMPTY_STRING;
    }

    private void endpointAuthMetric(HttpServletRequest request, String result, EndpointAuthMetricEnum endpointAuthMetricEnum) {

        String clientIp = IpUtils.getIpAddress(request);
        String requestURI = request.getRequestURI();

        eventDispatcher.dispatch(new MetricsEvent(meterRegistry -> {
            List<Tag> tags = Arrays.asList(
                    Tag.of("result", result),
                    Tag.of("uri", requestURI),
                    Tag.of("client_ip", clientIp),
                    Tag.of("error", endpointAuthMetricEnum.getCode())
            );
            meterRegistry.counter(METRIC_ENDPOINT_AUTH_COUNT, tags).increment();
        }));

    }

    enum EndpointAuthMetricEnum {

        FAIL("0"),
        SUCCESS("1"),
        ILLEGAL_PATH("path"),
        EMPTY_PARAMETER("empty"),
        ILLEGAL_PARAMETER("parameter"),
        ILLEGAL_TIMESTAMP("timestamp"),
        ILLEGAL_NONCE("nonce"),
        ILLEGAL_SIGN("sign");

        private final String code;

        EndpointAuthMetricEnum(String code) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }

    }

}
