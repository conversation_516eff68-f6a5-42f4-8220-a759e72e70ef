package cn.huolala.arch.hermes.spring.boot.actuate.util;


import cn.huolala.arch.hermes.common.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

public class IpUtils {

    public static final String UNKNOWN = "unknown";

    /**
     * 获取Ip地址
     *
     * @param request
     * @return
     */
    public static String getIpAddress(HttpServletRequest request) {

        String ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress == null || StringUtils.isBlank(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("X-Real-IP");
        }

        if (ipAddress == null || StringUtils.isBlank(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }


        if (ipAddress == null || StringUtils.isBlank(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }


        if (ipAddress == null || StringUtils.isBlank(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("HTTP_CLIENT_IP");
        }

        if (ipAddress == null || StringUtils.isBlank(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("HTTP_X_FORWARDED_FOR");
        }

        if (ipAddress == null || StringUtils.isBlank(ipAddress) || UNKNOWN.equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }

        if (StringUtils.isNotEmpty(ipAddress) && !UNKNOWN.equalsIgnoreCase(ipAddress)) {
            //注意：多次代理会出现多个ip值，第一个ip是真实的
            int index = ipAddress.indexOf(",");
            if (index != -1) {
                String ip = ipAddress.substring(0, index);
                return ip;
            } else {
                return ipAddress;
            }
        }

        return ipAddress;
    }
}
