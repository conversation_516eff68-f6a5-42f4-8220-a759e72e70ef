package cn.huolala.arch.hermes.spring.boot.actuate.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;

public final class JwtUtils {

    public static Jws<Claims> parserSign(String sign, String signKey) {

        String base64Security = Base64.getEncoder().encodeToString(signKey.getBytes(StandardCharsets.UTF_8));

        Jws<Claims> claimsJws = Jwts.parserBuilder()
                .setSigningKey(base64Security)
                .setAllowedClockSkewSeconds(3 * 60) // 3 minutes
                .build()
                .parseClaimsJws(sign);

        return claimsJws;
    }

    public static String createToken(String iss, String aud, Long expireSeconds, String signKey) {

        String base64Security = Base64.getEncoder().encodeToString(signKey.getBytes(StandardCharsets.UTF_8));
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;

        // 添加JWT的类
        JwtBuilder builder = Jwts.builder().setHeaderParam("type", "JsonWebToken");

        // 设置参数到jwt
        // builder.claim("test", "testvalue");

        builder.signWith(signatureAlgorithm, base64Security)// secretKey
                .setAudience(aud) // 签收者
                .setIssuer(iss); // 签发者

        long nowMillis = System.currentTimeMillis();
        Date now = new Date(nowMillis);
        // Token过期时间
        Date exp = new Date(nowMillis + expireSeconds * 1000);
        builder.setIssuedAt(now).setNotBefore(now).setExpiration(exp);

        return builder.compact();

    }

    private JwtUtils() {
    }
}
